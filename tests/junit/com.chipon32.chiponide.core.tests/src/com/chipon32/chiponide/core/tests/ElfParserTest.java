package com.chipon32.chiponide.core.tests;

import java.io.IOException;

import org.eclipse.cdt.core.IBinaryParser;
import org.eclipse.cdt.utils.elf.ElfHelper;
import org.eclipse.core.runtime.Path;
import org.junit.jupiter.api.Test;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;
import com.chipon32.chiponide.core.utils.parser.ChipOnElfParser;
import com.chipon32.chiponide.core.utils.parser.ChipOnIBinaryExecutable;
import com.chipon32.chiponide.ui.editors.binary.overview.ElfHeaderContentProvider;
import com.chipon32.chiponide.ui.editors.binary.sections.ElfSectionContentProvider;

public class ElfParserTest extends TestCaseBase implements TestCaseUtil {

	ChipOnElfParser parser = new ChipOnElfParser();
	String testFile = getTestFile("elf/A156_C.elf");
	String armTestFile = getTestFile("elf/arm_calendar.o");

	@Test
	void testParseMachine() throws IOException {
		IBinaryParser.IBinaryFile binary = parser.getBinary(new Path(testFile));

		ChipOnIBinaryExecutable object = assertInstanceOf(ChipOnIBinaryExecutable.class, binary);
		assertEquals(ChipOnElf.EM_kungfu32, object.getCPU());
	}

	@Test
	void testHeaderContentProvider() throws IOException {
		ElfHeaderContentProvider provider = new ElfHeaderContentProvider();
		ChipOnElf chipOnElf = new ChipOnElf(testFile);
		ElfHelper helper = new ElfHelper(chipOnElf);
		Object[] elements = provider.getElements(helper);

		int i = 0;
		assertEquals("(Machine class,ELFCLASS32 (32-bit))", elements[i].toString());
		assertEquals("(Data encoding,ELFDATA2LSB (Little endian))", elements[++i].toString());
		assertEquals("(Header version,EV_CURRENT (Current version))", elements[++i].toString());
		assertEquals("(File type,ET_EXEC (Executable file) (2))", elements[++i].toString());
		assertEquals("(Machine,kungfu32)", elements[++i].toString());
		assertEquals("(Debug Info,DWARF)", elements[++i].toString());
		assertEquals("(e_entry,0x00000000)", elements[++i].toString());
		assertEquals("(Flags,0x0)", elements[++i].toString());
		assertEquals("(Header Size,52 bytes (0x34))", elements[++i].toString());
		assertEquals("(Segment header entry size,32 bytes (0x20))", elements[++i].toString());
		assertEquals("(Section header entry size,40 bytes (0x28))", elements[++i].toString());
		assertEquals("(Number of program headers,4)", elements[++i].toString());
		assertEquals("(Number of section headers,17)", elements[++i].toString());
		assertEquals("(Program header table offset,0x34)", elements[++i].toString());
		assertEquals("(Section header table offset,0x114f0)", elements[++i].toString());
		assertEquals("(String header string table index,14)", elements[++i].toString());
	}

	@Test
	void testSelectionContentProvider() throws IOException {
		ElfSectionContentProvider provider = new ElfSectionContentProvider();
		ChipOnElf chipOnElf = new ChipOnElf(armTestFile);
		ElfHelper helper = new ElfHelper(chipOnElf);
		Object[] elements = provider.getElements(helper);
		assertEquals(41, elements.length);
		assertEquals("SectionItem{number=1, name=.text, offset=0x00000034, address=0x00000000, size=880, type=SHT_PROGBITS, flags=SHF_ALLOC+SHF_EXECINSTR}", elements[0].toString());
		assertEquals("SectionItem{number=2, name=.arm_vfe_header, offset=0x000003A4, address=0x00000000, size=4, type=SHT_PROGBITS, flags=}", elements[1].toString());
		assertEquals("SectionItem{number=3, name=.bss, offset=0x000003A8, address=0x00000000, size=12, type=SHT_NOBITS, flags=SHF_ALLOC+SHF_WRITE}", elements[2].toString());
		assertEquals("SectionItem{number=4, name=.comment, offset=0x000003A8, address=0x00000000, size=135, type=SHT_PROGBITS, flags=}", elements[3].toString());
		assertEquals("SectionItem{number=5, name=.data, offset=0x00000430, address=0x00000000, size=4, type=SHT_PROGBITS, flags=SHF_ALLOC+SHF_WRITE}", elements[4].toString());
		assertEquals("SectionItem{number=17, name=.debug_info, offset=0x00000CA8, address=0x00000000, size=472, type=SHT_PROGBITS, flags=SHF_GROUP}", elements[16].toString());
	}

}