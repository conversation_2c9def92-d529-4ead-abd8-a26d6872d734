package com.chipon32.hex.ui.editors;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.hex.core.GroupMemory;

// 行对象解析
public class GroupMemoryContentProvider implements IStructuredContentProvider {
	public String showformat="16bits";
	public boolean isShowFlash=true;
	public boolean isShowDate=false;
	public boolean isShowEEprom=false;
	@Override
	public void dispose() {

	}

	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {

	}
	
	//  获取不同的对象
	@Override
	public Object[] getElements(Object inputElement) {
		//  
		if(inputElement instanceof GroupMemory)
		{
			// 当前行 
			GroupMemory lMemory = (GroupMemory)inputElement;
			// 当前hex
				//		lMemory.getMemory();
			// 对象列表
			Object[] flashList = lMemory.getGroupFlashDataList().toArray();
			Object[] userList = lMemory.getGroupUserDataList().toArray();
			Object[] eepromList = lMemory.getGroupEEpromDataList().toArray();
			// 线性统一数量下管理：顺序 falsh  userflash  eeprom config debugprogramflag protect  rom ram
			int  allreslutsize=0;	
			
			if(isShowFlash)
				allreslutsize	=	flashList.length==1?0:flashList.length;		
			
			if(isShowDate)
				allreslutsize	+=	(userList.length==1?0:userList.length);
			
			if(isShowEEprom)
				allreslutsize	+=	(eepromList.length==1?0:eepromList.length);
			//##################################################
			Object[] obj=new Object[allreslutsize];
			
			int deali=0;
			
			if(isShowFlash)
			{
				if(flashList.length!=1)
				{
				for(int i=0;i<flashList.length ;i++)
					obj[deali+i]=flashList[i-0];			
				deali=flashList.length;
				}
			}
			if(isShowDate)
			{
				if(userList.length!=1)
				{
				for(int i=0;i<userList.length ;i++)
					obj[deali+i]=userList[i-0];			
				deali=+userList.length;
				}
			}
			if(isShowEEprom)
			{
				if(eepromList.length!=1)
				{	
				for(int i=0;i<eepromList.length ;i++)
					obj[deali+i]=eepromList[i-0];			
				deali=+eepromList.length;
				}
			}
			//##################################################
			return obj;			
		}
		return new Object[0];
	}
	
}	
	
	
	


