package com.chipon32.hex.ui.editorsconfig;


import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ComboBoxCellEditor;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;


import com.chipon32.hex.core.ConfigMessage;
import com.chipon32.util.ui2.OneClickCellModifier2;

// 组化的单元格内容修改类
public class ConfigCellModifier extends OneClickCellModifier2 {

	// 
	public ConfigCellModifier(TableViewer tableViewer) {
		super(tableViewer);
		// TODO Auto-generated constructor stub
	}

	@Override
	public boolean allowModify(Object element, int columnIndex) {
		if(element instanceof ConfigMessage)
		{
			ConfigMessage commData = (ConfigMessage)element;
			// 重设编辑器，更新当前内容的下拉存在列表
			CellEditor[] cellEditorsconfig = new CellEditor[3];
			cellEditorsconfig[0] = null;
			cellEditorsconfig[1] = new ComboBoxCellEditor(super.getTableViewer().getTable(),commData.getElement(),SWT.READ_ONLY);
			cellEditorsconfig[2] = null;
			super.getTableViewer().setCellEditors(cellEditorsconfig);
			// 是否允许修改
			if(commData!=null)
			{
				if(columnIndex==1)
					return true;
			}		
		}
		return false;
	}
	// 执行修改
	@Override
	public void doModify(Object element, int columnIndex, Object value){
		
		if(element instanceof ConfigMessage)
		{
			ConfigMessage commData = (ConfigMessage)element;
			switch(columnIndex)
			{
				case 1:
					commData.setSelelementtext(commData.getElement()[(int) value]);
					commData.setDefaultselid((int) value);
					break;
			}			
			// 结果的刷新
			tableViewer.refresh();
		}

	}
	// 获取当前行 某个下标的结果
	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof ConfigMessage)
		{
			ConfigMessage commData = (ConfigMessage)element;
			// 重设编辑器，更新当前内容的下拉存在列表 这里修改就晚了，判断是否允许时就更新
			//================================
			
			switch(columnIndex)
			{
			case 0:
				return commData.getName();
			case 1:
				return getSelIndex(commData,commData.getSelelementtext());
			case 2:
				return commData.getRemark();				
			
			}			
		}
		return null;
	}
	public int getSelIndex(ConfigMessage cm,String str)
	{
		for(int i=0;i<cm.getElement().length;i++)
		{
			if(cm.getElement()[i].equalsIgnoreCase(str))
			{
				return i;
			}
		}
		return -1;
	}

}
