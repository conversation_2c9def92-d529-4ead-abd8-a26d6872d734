package com.chipon32.hex.ui.editorssector;


import org.eclipse.jface.viewers.TableViewer;

import com.chipon32.hex.core.SectorElementVaule;
import com.chipon32.util.ui2.OneClickCellModifier2;

// 组化的单元格内容修改类
public class SectorCellModifier extends OneClickCellModifier2 {

	// 
	public SectorCellModifier(TableViewer tableViewer) {
		super(tableViewer);
		// TODO Auto-generated constructor stub
	}

	@Override
	public boolean allowModify(Object element, int columnIndex) {
		if(element instanceof SectorElementVaule)
		{
			SectorElementVaule sectrorone = (SectorElementVaule) element;
			if(sectrorone!=null)
			{
				if(columnIndex>2)
					return true;
			}
				
		}
		return false;
	}
	// 执行修改
	@Override
	public void doModify(Object element, int columnIndex, Object value){
		
		if(element instanceof SectorElementVaule)
		{
			SectorElementVaule sectrorone = (SectorElementVaule)element;
			switch(columnIndex)
			{
				case 0:
					break;
				case 1:break;
				case 2:break;
				case 3:				
					sectrorone.setIsCanRead((Boolean)value);					
					break;
				case 4:
					sectrorone.setIsCanWrite((Boolean)value);	
					break;
			
			}			
			// 结果的刷新
			tableViewer.refresh();
		}

	}
	// 获取当前行 某个下标的结果
	@Override
	public Object getColumnValue(Object element, int columnIndex) {
		if(element instanceof SectorElementVaule)
		{
			// 
			SectorElementVaule commData = (SectorElementVaule)element;
			switch(columnIndex)
			{
			case 0:
				return "Page"+commData.getNum();
			case 1:
				return commData.getAddressHexValue();
			case 2:
				return Integer.toString(commData.getLen());				
			case 3:
				return commData.getIsCanRead();			
			case 4:
				return commData.getIsCanWrite();	

			}
			
		}
		return null;
	}
	

}
