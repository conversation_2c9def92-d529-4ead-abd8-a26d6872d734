Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: ChipON IDE HEX Load UI
Bundle-SymbolicName: com.chipon32.hex.ui;singleton:=true
Automatic-Module-Name: com.chipon32.hex.ui
Bundle-Version: 2.0.0.qualifier
Bundle-Activator: com.chipon32.hex.ui.HexUiActivator
Bundle-Vendor: ChipON
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 com.chipon32.hex.core,
 com.chipon32.util.ui,
 com.chipon32.chiponide.core,
 org.eclipse.ui.forms,
 org.eclipse.core.resources,
 org.eclipse.ui.ide
Bundle-RequiredExecutionEnvironment: JavaSE-17
Bundle-ActivationPolicy: lazy
Export-Package: com.chipon32.hex.ui,
 com.chipon32.hex.ui.editors,
 com.chipon32.hex.ui.editorsconfig,
 com.chipon32.hex.ui.editorsm<PERSON>,
 com.chipon32.hex.ui.editorssector
