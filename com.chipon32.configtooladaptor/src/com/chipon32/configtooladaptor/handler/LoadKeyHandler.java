package com.chipon32.configtooladaptor.handler;



import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.core.commands.IHandler;
import org.eclipse.core.commands.IHandlerListener;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.PlatformUI;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONReader;
import com.chipon32.configtool.adaptor.AdaptorCache;
import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;
import com.chipon32.configtool.dialog.KeyGenerateDialog;
import com.chipon32.configtool.util.FileUtil;
import com.chipon32.configtool.util.cipher.LicenceUnit;
import com.chipon32.configtool.util.cipher.RSA2048_Server;
import com.chipon32.configtooladaptor.ProAndIDEAdaptorImpl;

public class LoadKeyHandler implements IHandler {

	@Override
	public void addHandlerListener(IHandlerListener handlerListener) {

	}

	@Override
	public void dispose() {

	}

	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
    	Shell shell = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getShell();
    	ProAndIDEAdaptor adaptor = AdaptorCache.INSTANCE.getAdaptor();
		File keyFile = FileUtil.openOpenFileDIalog(shell, adaptor);
		if(keyFile == null) {
			adaptor.printErrorMsg(Messages.LoadKeyHandler_0);
			return null;
		}
		try (
			BufferedReader fileReader = new BufferedReader( new InputStreamReader(new FileInputStream(keyFile), StandardCharsets.UTF_8));
            //FileReader fileReader = new FileReader(selectedFile);					
			JSONReader jsonReader = new JSONReader(fileReader);)  {
			JSONObject jsonRoot = JSON.parseObject(jsonReader.readObject().toString());
			String cipherKey = jsonRoot.getString(KeyGenerateDialog.CIPHER_TEXT_TITTLE);
			String plainText = new String(RSA2048_Server.decryptPrivateKey(RSA2048_Server.decodeBase64(cipherKey), LicenceUnit.priveteKey));
			String debugKey = plainText.substring(plainText.lastIndexOf("!")+1); //$NON-NLS-1$
//			CommunicateUtil.cipherKey = debugKey;
			
			adaptor.printErrorMsg(Messages.LoadKeyHandler_1);
        } catch (IOException e) {
			adaptor.printErrorMsg(Messages.LoadKeyHandler_0 + e.getMessage());
            e.printStackTrace();
        }
    	return null;
	}

	@Override
	public boolean isEnabled() {
		return true;
	}

	@Override
	public boolean isHandled() {
		return true;
	}

	@Override
	public void removeHandlerListener(IHandlerListener handlerListener) {

	}

}
