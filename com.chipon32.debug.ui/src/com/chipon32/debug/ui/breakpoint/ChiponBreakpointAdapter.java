/*******************************************************************************
 * Copyright (c) 2005, 2008 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     B<PERSON>rn <PERSON>-<PERSON> - initial API and implementation
 *     Wind River Systems - added support for IToggleBreakpointsTargetFactory
 *******************************************************************************/
package com.chipon32.debug.ui.breakpoint;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.resources.IMarker;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.debug.core.model.ILineBreakpoint;
import org.eclipse.debug.ui.actions.IToggleBreakpointsTargetExtension;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IRegion;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.texteditor.IDocumentProvider;
import org.eclipse.ui.texteditor.ITextEditor;

import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.breakpoint.ChiponLineBreakpoint;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.util.ChiponDebugUtil;
import com.chipon32.debug.ui.views.disassembly.ChiponDisassemblyView;


/**
 * <AUTHOR> @since 2013-7-5 下午2:44:51
 * 
 */
public class ChiponBreakpointAdapter implements IToggleBreakpointsTargetExtension {
	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTarget#toggleLineBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
	 */
	@Override
	public void toggleLineBreakpoints(IWorkbenchPart part, ISelection selection) throws CoreException {
		//判断是否在调试运行中，运行中需先暂停再进行断点添加
		ChiponThread fThread = ChiponDebugUtil.getCurrentChiponThread();
		if(fThread!=null){
			if(ChiponDebugTarget.isDebugRevBusy){
//				WriteMessage.getDefault().writeErrMessage(Messages.ChiponBreakpointAdapter_0);	
//				return;
			}
		}
		//#####################################################	
		
		ITextSelection textSelection = (ITextSelection) selection;
		int lineNumber = textSelection.getStartLine();
		IBreakpoint[] breakpoints = DebugPlugin.getDefault().getBreakpointManager().getBreakpoints(DebugCoreActivator.ID_CHIP_DEBUG_MODEL);
		if(part instanceof ITextEditor) {
			//获取调试程序所在编辑器
			ITextEditor textEditor = getEditor(part);
			if (textEditor != null) {
				//获取文件名和行号isSuspendFlag =
				IResource resource = (IResource) textEditor.getEditorInput().getAdapter(IResource.class);
				//####################################################
				if(breakpoints.length == 0){//添加第一个断点
					ChiponLineBreakpoint lineBreakpoint = new ChiponLineBreakpoint(resource, lineNumber + 1, "");
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(lineBreakpoint); //唤醒breakpointAdded(IBreakpoint)-> install(DebugTarget)方法
					
					if(!lineBreakpoint.isSuccess()){
						lineBreakpoint.delete();
					}
				}else{//添加非第一个断点
					//#############################################
					for (int i = 0; i < breakpoints.length; i++) {
						IBreakpoint breakpoint = breakpoints[i];
						if (breakpoint instanceof ILineBreakpoint /*&& resource.equals(breakpoint.getMarker().getResource())*/) {
							int pointLine = (int) ((ILineBreakpoint)breakpoint).getMarker().getAttribute(IMarker.LINE_NUMBER);
							if (pointLine == (lineNumber + 1)) {//判断当前双击鼠标的行数是否有断点，如果有则删除，否则添加断点。
//								if(breakpoint.getDebugTarget()!=null)
//									if(!breakpoint.getDebugTarget().getDebugTarget().isBusy())
										breakpoint.delete();
								return;
							}
						}
					}
					
					//#################################################
					ChiponLineBreakpoint lineBreakpoint = new ChiponLineBreakpoint(resource, lineNumber + 1, "");
					DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(lineBreakpoint);////唤醒breakpointAdded(IBreakpoint)-> install(DebugTarget)方法
					if(!lineBreakpoint.isSuccess()){
						lineBreakpoint.delete();
					}
					//#############################################
				}
				
			}
//			System.out.println(getVariableAndFunctionName(part, selection));
			
		}else if(part instanceof ChiponDisassemblyView) {
			ChiponDisassemblyView chiponDisassemblyView = (ChiponDisassemblyView)part;
			
			if (breakpoints == null || breakpoints.length == 0) {
				ChiponAddrBreakpoint addrBreakpoint = new ChiponAddrBreakpoint(chiponDisassemblyView.getResource(),lineNumber,getBreakpointAddr(part,selection),"");
				DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(addrBreakpoint);//唤醒breakpointAdded(IBreakpoint)-> install(DebugTarget)方法
				if(!addrBreakpoint.isSuccess()){
					addrBreakpoint.delete();
				}
			}else {
				for (int i = 0; i < breakpoints.length; i++) {
					IBreakpoint breakpoint = breakpoints[i];
					if(breakpoint instanceof ChiponAddrBreakpoint) {
						ChiponAddrBreakpoint addrBreakpoint = (ChiponAddrBreakpoint)breakpoint;
						//如果当前位置已经存在断点则移除断点
						if(addrBreakpoint.getAddress().equalsIgnoreCase(getBreakpointAddr(part,selection))) {
							addrBreakpoint.delete();
							return;
						}
					}
				}
				
				ChiponAddrBreakpoint addrBreakpoint = new ChiponAddrBreakpoint(chiponDisassemblyView.getResource(),lineNumber,getBreakpointAddr(part,selection),"");
				DebugPlugin.getDefault().getBreakpointManager().addBreakpoint(addrBreakpoint);
				if(!addrBreakpoint.isSuccess()){
					addrBreakpoint.delete();
				}
			}
			
		}
		
	}
	
	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTarget#canToggleLineBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
	 */
	@Override
	public boolean canToggleLineBreakpoints(IWorkbenchPart part, ISelection selection) {
		if(getEditor(part) != null) {
			return true;
		}else if(getBreakpointAddr(part,selection) != null) {
			return isHex(getBreakpointAddr(part,selection));
		}
		return false;
	}
	
	private String getBreakpointAddr(IWorkbenchPart part, ISelection selection) {
		if(part instanceof ChiponDisassemblyView) {
			if(selection instanceof ITextSelection) {
				ITextSelection textSelection = (ITextSelection)selection;
				try {
					IDocument document = ((ChiponDisassemblyView) part).getfDocument();
					String lineStr = document.get(textSelection.getOffset(),document.getLineLength(textSelection.getStartLine()));
//					String[] strs = lineStr.split("	");
					Pattern pattern = Pattern.compile("^[0-9a-fA-F]+:.+"); //$NON-NLS-1$
					Matcher matcher = pattern.matcher(lineStr.trim());
					if (matcher.matches()) {
						String[] temp = lineStr.trim().split(":"); //$NON-NLS-1$
						return "0x"+temp[0];
					}
//					if(strs.length>0) {
//						String firstStr = strs[0];
//						if(firstStr.contains(":")) {
//							return firstStr.split(":")[0].trim();
//						}
//					}
				} catch (BadLocationException e) {
					e.printStackTrace();
				}
				
			}
		}
		return null;
	}
	
	private boolean isHex(String input) {
		String regex = "0x[0-9A-Fa-f]+"; //$NON-NLS-1$
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);		
		return matcher.matches();
	}
	
	/**
	 * Returns the editor being used to edit a PDA file, associated with the
	 * given part, or <code>null</code> if none.
	 *  
	 * @param part workbench part
	 * @return the editor being used to edit a PDA file, associated with the
	 * given part, or <code>null</code> if none
	 */
	private ITextEditor getEditor(IWorkbenchPart part) {
		if (part instanceof ITextEditor) {
			ITextEditor editorPart = (ITextEditor) part;
			IResource resource = (IResource) editorPart.getEditorInput().getAdapter(IResource.class);
			if (resource != null) {
				String extension = resource.getFileExtension();
				if (extension != null && ( 
						extension.equalsIgnoreCase("c") 
						|| extension.equalsIgnoreCase("h")
						|| extension.equalsIgnoreCase("cc") 
						|| extension.equalsIgnoreCase("cxx") 
						|| extension.equalsIgnoreCase("cpp") 
						|| extension.equalsIgnoreCase("hpp") 
						|| extension.equalsIgnoreCase("asm") 
						|| extension.equalsIgnoreCase("s"))
						|| extension.equalsIgnoreCase("inc")
						) { //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
					return editorPart;
				}
			}
		}
		
		return null;		
	}
	
	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTarget#toggleMethodBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
	 */
	@Override
	public void toggleMethodBreakpoints(IWorkbenchPart part, ISelection selection) throws CoreException {
	}
	/* (non-Javadoc)
	 * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTarget#canToggleMethodBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
	 */
	@Override
	public boolean canToggleMethodBreakpoints(IWorkbenchPart part, ISelection selection) {
		return false;
	}

	/**
	 * Returns the variable and function names at the current line, or <code>null</code> if none.
	 * 
	 * @param part text editor
	 * @param selection text selection
	 * @return the variable and function names at the current line, or <code>null</code> if none.
	 *  The array has two elements, the first is the variable name, the second is the function name.
	 */
	protected String[] getVariableAndFunctionName(IWorkbenchPart part, ISelection selection) {
	    ITextEditor editor = getEditor(part);
	    if (editor != null && selection instanceof ITextSelection) {
	        ITextSelection textSelection = (ITextSelection) selection;
	        IDocumentProvider documentProvider = editor.getDocumentProvider();
	        try {
	            documentProvider.connect(this);
	            IDocument document = documentProvider.getDocument(editor.getEditorInput());
	            IRegion region = document.getLineInformationOfOffset(textSelection.getOffset());
	            String string = document.get(region.getOffset(), region.getLength()).trim();
	            if (string.startsWith("var ")) { //$NON-NLS-1$
	                String varName = string.substring(4).trim(); 
	                String fcnName = getFunctionName(document, varName, document.getLineOfOffset(textSelection.getOffset()));
	                return new String[] {varName, fcnName};
	            }
	        } catch (CoreException e) {
	        } catch (BadLocationException e) {
	        } finally {
	            documentProvider.disconnect(this);
	        }
	    }	
	    
	    return null;
	}
	
	/**
	 * Returns the name of the function containing the given variable defined at the given
	 * line number in the specified document.
	 * 
	 * @param document PDA source file
	 * @param varName variable name
	 * @param line line numbner at which the variable is defined
	 * @return name of function defining the variable
	 */
	private String getFunctionName(IDocument document, String varName, int line) {
	    // This is a simple guess at the function name - look for the labels preceeding
	    // the variable definition, and then see if there are any 'calls' to that
	    // label. If none, assumet the variable is in the "_main_" function
	    String source = document.get();
	    int lineIndex = line - 1;
	    while (lineIndex >= 0) {
            try {
                IRegion information = document.getLineInformation(lineIndex);
                String lineText = document.get(information.getOffset(), information.getLength());
                if (lineText.startsWith(":")) { //$NON-NLS-1$
                    String label = lineText.substring(1);
                    if (source.indexOf("call " + label) >= 0) { //$NON-NLS-1$
                        return label;
                    }
                }
                lineIndex--;
            } catch (BadLocationException e) {
            }
	    }
	    return "_main_"; //$NON-NLS-1$
	}
    
    /* (non-Javadoc)
     * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTargetExtension#toggleBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
     */
    @Override
	public void toggleBreakpoints(IWorkbenchPart part, ISelection selection) throws CoreException {
        if (canToggleWatchpoints(part, selection)) {
            toggleWatchpoints(part, selection);
        } else {
            toggleLineBreakpoints(part, selection);
        }    
    }
    
    /* (non-Javadoc)
     * @see org.eclipse.debug.ui.actions.IToggleBreakpointsTargetExtension#canToggleBreakpoints(org.eclipse.ui.IWorkbenchPart, org.eclipse.jface.viewers.ISelection)
     */
    @Override
	public boolean canToggleBreakpoints(IWorkbenchPart part, ISelection selection) {
        return canToggleLineBreakpoints(part, selection) || canToggleWatchpoints(part, selection);
    }
	@Override
	public void toggleWatchpoints(IWorkbenchPart part, ISelection selection)
			throws CoreException {
		// TODO Auto-generated method stub
		
	}
	@Override
	public boolean canToggleWatchpoints(IWorkbenchPart part,
			ISelection selection) {
		// TODO Auto-generated method stub
		return false;
	}
}
