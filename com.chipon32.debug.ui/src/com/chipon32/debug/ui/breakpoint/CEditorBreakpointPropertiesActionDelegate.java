package com.chipon32.debug.ui.breakpoint;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.source.IVerticalRulerInfo;
import org.eclipse.ui.IActionDelegate2;
import org.eclipse.ui.texteditor.AbstractRulerActionDelegate;
import org.eclipse.ui.texteditor.ITextEditor;

public class CEditorBreakpointPropertiesActionDelegate extends
		AbstractRulerActionDelegate implements IActionDelegate2 {

	public CEditorBreakpointPropertiesActionDelegate() {
		// TODO Auto-generated constructor stub
	}

	@Override
	protected IAction createAction(ITextEditor editor,
			IVerticalRulerInfo rulerInfo) {
		// TODO Auto-generated method stub
		return null;
	}

}
