package com.chipon32.debug.ui.breakpoint;

import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.ui.IViewActionDelegate;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.PlatformUI;

import com.chipon32.debug.core.breakpoint.ChiponAddrBreakpoint;
import com.chipon32.debug.core.breakpoint.ChiponLineBreakpoint;

public class BreakpointPropertiesActionDelegate implements IViewActionDelegate {

	private IBreakpoint selectedBreakpoint;
	
	public BreakpointPropertiesActionDelegate() {
		// TODO Auto-generated constructor stub
	}

	@Override
	public void run(IAction action) {
		// TODO Auto-generated method stub
		if(action.isEnabled() && selectedBreakpoint!=null){
			BreakpointPropertiesDialog dialog = new BreakpointPropertiesDialog(PlatformUI.getWorkbench().getActiveWorkbenchWindow().getShell(), Messages.BreakpointPropertiesActionDelegate_0, selectedBreakpoint);
			dialog.open();
		}
		
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		action.setEnabled(false);
		selectedBreakpoint = null;
		
		if(selection instanceof TreeSelection){
			TreeSelection treeSelection = (TreeSelection) selection;
			if(treeSelection.getFirstElement() instanceof ChiponLineBreakpoint){
				action.setEnabled(true);
				selectedBreakpoint = (ChiponLineBreakpoint) treeSelection.getFirstElement();
				
			}else if(treeSelection.getFirstElement() instanceof ChiponAddrBreakpoint){
				action.setEnabled(true);
				selectedBreakpoint = (IBreakpoint) treeSelection.getFirstElement();
			}
			
		}

	}

	@Override
	public void init(IViewPart view) {

	}

}
