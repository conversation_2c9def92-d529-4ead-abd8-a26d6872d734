package com.chipon32.debug.ui.launcher;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.debug.ui.launcher.messages"; //$NON-NLS-1$
	public static String ChiponLaunchShortcut_0;
	public static String ChiponLaunchShortcut_1;
	public static String ChiponLaunchShortcutSoft_0;
	public static String ChiponLaunchShortcutSoft_1;
	public static String ChipONLaunchShortcut_2;
	public static String ChipONLaunchShortcut_3;
	
	public static String ChiponMainTab_0;
	public static String ChiponMainTab_1;
	public static String ChiponMainTab_2;
	public static String ChiponMainTab_3;
	public static String ChiponMainTab_4;
	public static String ChiponMainTab_5;
	public static String ChiponMainTab_6;
	public static String ChiponMainTab_7;
	public static String ChiponMainTab_8;
	public static String ChiponMainTab_9;
	public static String ChiponMainTab_10;
	public static String ChiponMainTab_11;
	public static String ChiponMainTab_12;	
	public static String ChiponMainTab_13;
	public static String ChiponMainTab_14;

	public static String ChiponDebugConfigPage_0;
	public static String ChiponDebugConfigPage_1;
	public static String ChiponDebugConfigPage_2;
	public static String ChiponDebugConfigPage_3;
	public static String ChiponDebugConfigPage_6;
	public static String ChiponDebugConfigPage_7;
	public static String ChiponDebugConfigPage_8;
	public static String ChiponDebugConfigPage_9;
	
	public static String ChiponConfigTab_0;

	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
