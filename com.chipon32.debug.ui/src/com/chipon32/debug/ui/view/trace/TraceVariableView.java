package com.chipon32.debug.ui.view.trace;

import java.beans.PropertyChangeSupport;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.draw2d.LightweightSystem;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.DoubleClickEvent;
import org.eclipse.jface.viewers.IDoubleClickListener;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TableLayout;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.ScrolledComposite;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.events.DisposeEvent;
import org.eclipse.swt.events.DisposeListener;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Canvas;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.ui.IActionBars;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.elf.Variable;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.IChiponEventListener;
import com.chipon32.debug.core.model.trace.TraceTableItem;
import com.chipon32.debug.core.model.trace.TraceTableManager;
import com.chipon32.debug.core.protocol.ChiponEvent;
import com.chipon32.debug.core.protocol.ChiponTerminatedEvent;
import com.chipon32.debug.ui.ImageKeys;
import com.chipon32.debug.ui.action.SwitchDrawToTableAction;
import com.chipon32.debug.ui.util.TraceCommand;

/**
 * @author: zj
 * @date: 2022-06-03
 * @Description: Trace view
 */
public class TraceVariableView extends ViewPart implements IChiponEventListener{
	public static final String ID = "com.chipon32.debug.ui.view.TraceView"; //$NON-NLS-1$
	
	private static LightweightSystem lws;
	private TableViewer tableViewer,tableshowViewer;
	private Combo symbolCombo;
	private Combo addressCombo;
	
	private VariableTraceGraph variableTraceGraph;
	private VariableTraceTable variableTraceTable;

	//trace线程
	public static TraceRunnable updater = new TraceRunnable();
	
	//增加的操作
	private SwitchDrawToTableAction switchAction;
	public static StackLayout stacklayout = new StackLayout();
	public static Composite tableshowComposite,traceComposite;
	public static Composite homeComposite;
	public static Composite currentComposit;
	public static TraceVariableView view;
	
	public TraceVariableView() {
		super();
	}
	
	public void initTraceVariableGraph(){
		variableTraceGraph = new VariableTraceGraph(null);
		if(lws != null){
			lws.setContents(variableTraceGraph);
		}
	}
	
	@Override
	public void createPartControl(Composite parent) {
		parent.setLayout(new GridLayout(10, true));
		
		//左侧添加监控变量区
		Composite befComposite = new Composite(parent, SWT.NONE);
		GridData varGridData = new GridData(SWT.FILL, SWT.FILL, true, true, 4, 1);
		befComposite.setLayoutData(varGridData);
		befComposite.setLayout(new GridLayout(1,false));
		
		ScrolledComposite sc = new ScrolledComposite(befComposite, SWT.BORDER | SWT.H_SCROLL | SWT.V_SCROLL);
		GridLayout scLayout = new GridLayout(1,false);
		sc.setLayout(scLayout);
		sc.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		sc.setExpandHorizontal(true);
		sc.setExpandVertical(true);
		
		Composite addrComposite = new Composite(sc, SWT.NONE);
		GridData addrGridData = new GridData(SWT.FILL, SWT.FILL, true, true);
		addrComposite.setLayoutData(addrGridData);
		addrComposite.setLayout(new GridLayout(3, false));
		
		sc.setMinSize(500, 400);
		sc.setContent(addrComposite);
		
		Label Labechanel = new Label(addrComposite,SWT.NONE);
		Labechanel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		Labechanel.setText(Messages.chartView_3);	
		
		symbolCombo = new Combo(addrComposite, SWT.BORDER);
		symbolCombo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		symbolCombo.setText("symbol OR struct.object"); //$NON-NLS-1$
		
		Button buttonaddsymbol=new Button(addrComposite,  SWT.NONE);
		buttonaddsymbol.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		buttonaddsymbol.setText(Messages.chartView_5);
		buttonaddsymbol.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				//添加监控变量
				addSymbolOrAddrMonitor(symbolCombo, true);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
		});
		
		Label Labechane2 = new Label(addrComposite,SWT.NONE);
		Labechane2.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		Labechane2.setText(Messages.chartView_7);	
		
		addressCombo = new Combo(addrComposite, SWT.BORDER);
		addressCombo.setToolTipText(Messages.chartView_8);
		addressCombo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		addressCombo.setText("1000 0000:2");  //$NON-NLS-1$
		
		Button buttonaddaddr=new Button(addrComposite,  SWT.NONE);
		buttonaddaddr.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
		buttonaddaddr.setText(Messages.chartView_10);
		buttonaddaddr.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				//添加监控变量
				addSymbolOrAddrMonitor(addressCombo, false);
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
			}
			
		});
		
		Composite tableComposite = new Composite(addrComposite, SWT.NONE);
		GridData tableGridData = new GridData(SWT.FILL, SWT.FILL, true, true, 4, 1);
		tableComposite.setLayoutData(tableGridData);
		tableComposite.setLayout(new FillLayout());
		
		tableViewer = new TableViewer(tableComposite, SWT.MULTI | SWT.BORDER);
		Table table = tableViewer.getTable();
		table.setHeaderVisible(true);
		table.setLinesVisible(true);
		TableLayout tableLayout = new TableLayout();
		table.setLayout(tableLayout);
		TableColumn tableColumn = new TableColumn(table, SWT.FILL);
		tableColumn.setWidth(500);
		tableColumn.setResizable(true);
		tableColumn.setText(""); //$NON-NLS-1$
		tableViewer.setContentProvider(new TraceTableViewerContentPorvider());
		tableViewer.setLabelProvider(new TraceTableViewerLabelProvider());
		tableViewer.setInput(VariableList.getVariableList());
		//添加tableViewer双击监听事件
		addListener();
		//====================================================================================
		
		//右侧图表UI
		//表格与波形图切换的大容器
		homeComposite = new Composite(parent,SWT.NONE);
		homeComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 6, 1));
		homeComposite.setLayout(stacklayout);
		
		//变量变化曲线绘图区
	    traceComposite = new Composite(homeComposite, SWT.NONE);
		traceComposite.setLayout(new FillLayout());
		//Composite traceComposite = new Composite(parent, SWT.NONE);
		//GridData traceGridData = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 1);
		//traceComposite.setLayoutData(traceGridData);

		Canvas canvas = new Canvas(traceComposite, SWT.FILL);
		canvas.setLayout(new FillLayout(SWT.NONE));
				
		lws = new LightweightSystem(canvas);
		if(VariableList.getVariableList().isEmpty()) {
			variableTraceGraph = new VariableTraceGraph(null);
		}else {
			variableTraceGraph = new VariableTraceGraph(VariableList.getTraceVariables());
		}
		lws.setContents(variableTraceGraph);
		//-------------------------------------------------------------------------------
        
        //表格Composite
        tableshowComposite = new Composite(homeComposite,SWT.NONE);
        tableshowComposite.setLayout(new FillLayout());
        //GridData tableshowGridData = new GridData(SWT.FILL, SWT.FILL, true, true, 3, 1);
		//tableshowComposite.setLayoutData(tableshowGridData);
		
		tableshowViewer = new TableViewer(tableshowComposite, SWT.MULTI | SWT.BORDER);
		Table tableshow = tableshowViewer.getTable();
		tableshow.setHeaderVisible(true);
		tableshow.setLinesVisible(true);
		TableLayout tableshowLayout = new TableLayout();
		tableshow.setLayout(tableshowLayout);
		TableColumn tableshowColumnSymbol = new TableColumn(tableshow, SWT.FILL);
		tableshowColumnSymbol.setWidth(160);
		tableshowColumnSymbol.setResizable(true);
		tableshowColumnSymbol.setText("符号");
		
		TableColumn tableshowColumnAddress = new TableColumn(tableshow, SWT.FILL);
		tableshowColumnAddress.setWidth(160);
		tableshowColumnAddress.setResizable(true);
		tableshowColumnAddress.setText("地址");
		
		TableColumn tableshowColumnValueD = new TableColumn(tableshow, SWT.FILL);
		tableshowColumnValueD.setWidth(160);
		tableshowColumnValueD.setResizable(true);
		tableshowColumnValueD.setText("十进制值");
		
		TableColumn tableshowColumnValueH = new TableColumn(tableshow, SWT.FILL);
		tableshowColumnValueH.setWidth(160);
		tableshowColumnValueH.setResizable(true);
		tableshowColumnValueH.setText("十六进制值");
		
		if(VariableList.getVariableList().isEmpty()) {
			variableTraceTable = new VariableTraceTable();
		}else {
			variableTraceTable = new VariableTraceTable(VariableList.getTraceVariables());
		}
		
		tableshowViewer.setContentProvider(new TraceTableShowViewerContentProvider());
		tableshowViewer.setLabelProvider(new TraceTableShowViewerLabelProvider());
		tableshowViewer.setInput(TraceTableManager.getManager());
		//-----------------------------------------------------------
		
		stacklayout.topControl = traceComposite;
		currentComposit = traceComposite;
		//==============================================================
        
        //添加视图菜单操作
        makeAction();
        contributeToActionBar();
        
        //在左侧tableViewer上添加右键菜单
        TraceActionGroup traceActionGroup = new TraceActionGroup(tableViewer,tableshowViewer,variableTraceGraph,variableTraceTable);
        traceActionGroup.fillContextMenu(new MenuManager());
        
        //添加调试时间监听
        ChiponDebugTarget.addEventListener(this);
        parent.addDisposeListener(new DisposeListener() {//移除监听
			@Override
			public void widgetDisposed(DisposeEvent e) {
				ChiponDebugTarget.removeEventListener(TraceVariableView.this);
				TraceRunnable.traceRunning = false;//停止当前variableTraceGraph的updater
			}
		});
	}

	/**
	 * 添加监控变量或地址的响应事件
	 * @param combo
	 * @param isSymbol
	 */
	@SuppressWarnings("unchecked")
	protected void addSymbolOrAddrMonitor(Combo combo, boolean isSymbol) {
		if(combo.getText().trim().isEmpty()){
			MessageDialog.openWarning(null, Messages.TraceVariableView_1, Messages.TraceVariableView_2); 
			return;
		}
		//-------------------------检查是否在已选的监控变量基础上进行添加监控变量-------------------------
		List<IVariableEntry> variables = new ArrayList<>();
		IStructuredSelection selection = tableViewer.getStructuredSelection();
		if(selection != null && selection.getFirstElement() != null) {
			variables.addAll((List<IVariableEntry>) selection.getFirstElement());
		}
		 
		IVariableEntry entry = new Variable();//添加的变量
		entry.setName(combo.getText().trim());
		if(!isContains(variables, entry.getName())) {
			boolean isCommandSuccess = false;
			if(isSymbol) { //添加符号
				entry.setSymbol(true);
				
				isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph add "+combo.getText().trim(), 2); //$NON-NLS-1$
				
			}else {//添加地址
				String in=combo.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
				String lens="4"; //$NON-NLS-1$
				String rusultstr=""; //$NON-NLS-1$
				try{
					if(in.contains(":")) {  //$NON-NLS-1$
						lens=in.substring(in.indexOf(":"));  //$NON-NLS-1$
						in=in.replace(lens, "");  //$NON-NLS-1$
						lens=lens.substring(1);
						if(Integer.parseInt(lens, 10)>8){
							MessageDialog.openError(Display.getDefault().getActiveShell(), Messages.chartView_18, Messages.chartView_19);
							return;
						}
					}
					rusultstr += Long.toString(Long.parseLong(in, 16));
					rusultstr +=" "+  Integer.toString(Integer.parseInt(lens, 10)); //$NON-NLS-1$
				}catch (Exception eq) {
					MessageDialog.openError(Display.getDefault().getActiveShell(), Messages.chartView_21, Messages.chartView_22);
					return;
				}
				entry.setAddress(in);
				
				isCommandSuccess = TraceCommand.dealCommand("kf32figure_oscillograph addr "+rusultstr, 3);  //$NON-NLS-1$ //$NON-NLS-2$
			}
			if(!isCommandSuccess) {
	        	return;
	        }
			entry.setCommandNum(TraceCommand.currCommandNum);
			
			//将选择添加监控变量行移除
			Table table = tableViewer.getTable(); 
			if(table.getSelection()!=null && table.getSelectionIndices().length>0) {
				VariableList.removeVariables(table.getSelectionIndices()[0]);
			} 
	        //重置选择监控变量信息
	        variables.add(entry);
			VariableList.addVariables(variables);
			tableViewer.setInput(VariableList.getVariableList());
//			tableViewer.refresh();//tableViewer.setInput(...)会刷新一遍
			
			//更新右侧示波器显示图
			variableTraceGraph.updateVariableTrace(variables);
			variableTraceTable.updateVariableTrace(variables);
			/*TraceTableItem[] tracetableList = new TraceTableItem[variables.size()];
			//向List中添加Item
			for(int i = 0; i < variables.size(); i++) {
				String sampleName = variables.get(i).getName();
				String sampleAddr = variables.get(i).getAddress();
				TraceTableItem item = new TraceTableItem(sampleName,sampleAddr,0.0,"0");
				tracetableList[i] = item;
			}	
			TraceTableManager.getManager().addTrace(tracetableList);*/
			
		}else {
			MessageDialog.openWarning(null, Messages.TraceVariableView_4, Messages.TraceVariableView_5);
		}
		
	}

	/**
	 * 判断当前添加的监控变量是否已经在当前选择行已经添加过了
	 * @param variablesInViewer
	 * @param entryName
	 * @return
	 */
	protected boolean isContains(List<IVariableEntry> variablesInViewer, String entryName) {
		for(IVariableEntry entry:variablesInViewer) {
			if(entry.getName().equals(entryName)) {
				return true;
			}
		}
		
		return false;
	}

	@Override
	public void setFocus() {
		
	}
	
	private void addListener(){
		tableViewer.addDoubleClickListener(new IDoubleClickListener() {
			@Override
			public void doubleClick(DoubleClickEvent event) {
				IStructuredSelection selection = (IStructuredSelection)event.getSelection();
				@SuppressWarnings("unchecked")
				List<IVariableEntry> variables= (List<IVariableEntry>) selection.getFirstElement();
				TraceTableItem[] tracetableList = new TraceTableItem[variables.size()];
				if(variables!=null && !variables.isEmpty() && ChiponDebugTarget.isDebugging){
					VariableList.setTraceVariables(variables);
					variableTraceGraph.updateVariableTrace(variables);
					variableTraceTable.updateVariableTrace(variables);
			    	//向List中添加Item
					/*for(int i = 0; i < variables.size(); i++) {
						String sampleName = variables.get(i).getName();
						String sampleAddr = variables.get(i).getAddress();
						TraceTableItem item = new TraceTableItem(sampleName,sampleAddr,0.0,"0");
						tracetableList[i] = item;
					}	
					TraceTableManager.getManager().addTrace(tracetableList);*/
				}
			}
		});

	}

	@Override
	public void handleEvent(ChiponEvent event) {
		if(event instanceof ChiponTerminatedEvent) {
			//清除视图上的历史监控信息
			Display.getDefault().asyncExec(new Runnable() {

				@Override
				public void run() {
					VariableList.getVariableList().clear();
					//删除tableviewer上的监控变量
					tableViewer.getTable().removeAll();
					//移除右侧示波器监控变量——暂时保留做终止调试后仍想观察的情况
//					variableTraceGraph.updateVariableTrace(null);
					
				}
				
			});
			
		}
	}
	
	
	private void makeAction() {
		//表格切换操作
		ImageDescriptor switchImage = ImageKeys.getImageDescriptor(ImageKeys.IMG_TOOL_TOTABLE);
		switchAction = new SwitchDrawToTableAction("切换视图", 0x02); //IAction.AS_CHECK_BOX
		switchAction.setImageDescriptor(switchImage);
	}
	
	//工具栏
	private void contributeToActionBar() {
		IActionBars bars = getViewSite().getActionBars();
		fillLocalToolBar(bars.getToolBarManager());
	}
	
	//操作填充到工具栏菜单中
	private void fillLocalToolBar(IToolBarManager manager) {
		manager.add(switchAction);
	}
	
}
