package com.chipon32.debug.ui.view.trace;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.nebula.visualization.xygraph.dataprovider.Sample;
import org.eclipse.swt.widgets.Display;

import com.chipon32.debug.core.elf.IVariableEntry;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.trace.TraceTableItem;
import com.chipon32.debug.core.model.trace.TraceTableManager;
import com.chipon32.debug.core.util.ChiponDebugUtil;

public class TraceRunnable implements Runnable {

	public static List<IVariableEntry> traceVariables = new ArrayList<>();
	public static boolean traceRunning = false;

	private long t = 0; // 时间戳
	private double value;

	@Override
	public void run() {
		if (traceVariables == null || traceVariables.isEmpty()) {
			traceRunning = false;
			return;
		}
		if (!ChiponDebugTarget.isDebugging) {
			traceRunning = false;
			return;
		}

		if (!ChiponDebugUtil.isCurrentChiponThreadSuspended()) {
			Map<String, String> valueMap = new HashMap<>();
			for (String str : ChiponDebugTarget.traceDataList) {
				String[] strs = str.split(":"); //str形如：(gdb) figure_oscillographdata:0-Addr:268448760_2:value: 64028 
				if (strs.length == 5) {
					if (strs[1].contains("Symbol")) {
						valueMap.put(strs[2], strs[4]);
					} else if (strs[1].contains("Addr")) {
						String addrStr = strs[2];
						String[] strArray = addrStr.split("_");
						if (strArray.length == 2) {
							String addr = Long.toHexString(Long.parseLong(strArray[0]));
							valueMap.put(addr, strs[4]);
						}
					}
				}
			}
			
			if (traceVariables.size() == VariableTraceGraph.traceProviders.length) {
				// 屏蔽增加监控变量后traceProviders没及时更新导致两者长度不一致问题，跳过一次赋值
				t = System.currentTimeMillis();
				for (int i = 0; i < traceVariables.size(); i++) {
					if (!traceVariables.get(i).isSymbol()) {
						if (valueMap.get(traceVariables.get(i).getAddress()) != null) {
							value = Double.parseDouble(valueMap.get(traceVariables.get(i).getAddress()).trim());
							//更新图表上的数据
							Sample sample = new Sample(t, value);
							VariableTraceGraph.traceProviders[i].addSample(sample);
							VariableTraceGraph.traceProviders[i].setCurrentYDataTimestamp(t);
							
							//更新表格上的数据
							String valueH = Integer.toHexString((int) value);
							TraceTableItem item = new TraceTableItem(null, traceVariables.get(i).getAddress(), value, valueH);
							// VariableTraceTable.tracetableItems[i] = item;
							VariableTraceTable.tracetableList[i] = item;
						}
					} else {
						if (valueMap.get(traceVariables.get(i).getName()) != null) {
							value = Double.parseDouble(valueMap.get(traceVariables.get(i).getName()));
							//更新图表上的数据
							Sample sample = new Sample(t, value);
							VariableTraceGraph.traceProviders[i].addSample(sample);
							VariableTraceGraph.traceProviders[i].setCurrentYDataTimestamp(t);
							
							String valueH = Integer.toHexString((int) value);
							TraceTableItem item = new TraceTableItem(traceVariables.get(i).getName(), null, value, valueH);
							// VariableTraceTable.tracetableItems[i] = item;
							VariableTraceTable.tracetableList[i] = item;
						}
					}
				}//end for

				//TraceTableManager.getManager().modifyTrace(VariableTraceTable.tracetableItems);
				TraceTableManager.getManager().modifyTrace(VariableTraceTable.tracetableList); //更新Table上item
			}
			
		}

		if (traceRunning) {
			Display.getCurrent().timerExec(20, this);
		}

	}

}
