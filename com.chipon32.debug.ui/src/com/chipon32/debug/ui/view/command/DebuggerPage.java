package com.chipon32.debug.ui.view.command;

import org.eclipse.jface.layout.GridDataFactory;
import org.eclipse.jface.layout.GridLayoutFactory;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.part.Page;

public abstract class DebuggerPage extends Page implements PageBookViewPage{
	
	private DebuggerPagedView mView;
	
	private Composite mControl;
	
//	private LinkedContextWidget mLinkedContextWidget = null;
	
	private MessageArea mMessageArea;
	
	public DebuggerPage(DebuggerPagedView view, boolean forceUpdateWhilstHidden) {
	    this.mView = view;
	  }
	  
	  public DebuggerPage( DebuggerPagedView view) {
	    this(view, false);
	  }

	@Override
	public void createControl(Composite parent) {
		this.mControl = new Composite(parent, 0);
		GridLayoutFactory.fillDefaults().spacing(0, 0).applyTo(mControl);
	    GridDataFactory.fillDefaults().grab(true, true).applyTo(mControl);
//	    createLinkedContextWidget(this.mControl);
	    Composite clientComposite = new Composite(this.mControl, 0);
	    this.mMessageArea = new MessageArea(this.mControl, 0);
	    this.mMessageArea.show(true);
	    mMessageArea.setVisible(true);
	    GridDataFactory.fillDefaults().grab(true, true).applyTo(clientComposite);
	    GridLayoutFactory.fillDefaults().spacing(0, 0).applyTo(clientComposite);
	    createPage(clientComposite);
	}
	
	public abstract void createPage(Composite paramComposite);

	public Control getControl() {
		return this.mControl;
	}
	
//	public LinkedContextWidget getLinkedContextWidget() {
//	    return this.mLinkedContextWidget;
//	  }
	
//	 protected void createLinkedContextWidget(Composite parent) {
////		    UXContext context = getAnchor();
////		    if (!context.isDefaultContext() && !context.isEmptyContext()) {
//	      this.mLinkedContextWidget = new LinkedContextWidget(parent, 0);
//	      this.mLinkedContextWidget.show(false);
//	 } 
//		  }
//	 protected void disposeLinkedContextWidget() {
//	    if (this.mLinkedContextWidget != null) {
//	      this.mLinkedContextWidget.dispose();
//	      this.mLinkedContextWidget = null;
//	    } 
//	 }

	public DebuggerPagedView getmView() {
		return mView;
	}
	
}
