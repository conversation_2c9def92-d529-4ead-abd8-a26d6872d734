package com.chipon32.debug.ui.view.command;

import org.eclipse.ui.IViewSite;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.part.IPage;
import org.eclipse.ui.part.PageBook;

public class DebuggerPagedView extends AbstractPagedView {
	
//	private Class<DebugConsolePage> pageClass;
	
	public enum ContextType {
	    CONNECTION_ORIENTED, EXECUTION_CONTEXT_ORIENTED, BOTH;
	  }

	@Override
	protected Object getAnchorFromPart(IWorkbenchPart paramIWorkbenchPart) {
		return null;
	}

	@Override
	public void init(IViewSite site) throws PartInitException {
		super.init(site);
	}

	@Override
	protected IPage createDefaultPage(PageBook book) {
//		DebuggerMessagePage page = new DebuggerMessagePage(this);
		PageBookViewPage page = new DebugConsolePage(this);
	    initPage(page);
	    page.createControl(book);
	    return page;
	}
	public void setFocus() {}

	@Override
	protected PageRec doCreatePage(IWorkbenchPart part) {
		return null;
	}
	
//	@Override
//	protected PageBookView.PageRec doCreatePage(IWorkbenchPart part) {
//		Object anchor = getAnchorFromPart(part);
//		if (anchor != null) {
//			for (PageBookViewPage existing : getPages()) {
//				if (getPageClass().isInstance(existing) /* && existing.getAnchor() == anchor */)
//					return new PageBookView.PageRec(part, (IPage) existing);
//			}
//			PageBookViewPage page = (PageBookViewPage) part.getAdapter(pageClass);
//			PageBookViewPage page = new DebugConsolePage(this);
//			
//			initPage(page);
//			page.createControl(getPageBook());
//			getPages().add(page);
////			page.getLinkedContextWidget().show(true);
//			return new PageBookView.PageRec(part, (IPage) page);
//		return null;
//	}

}
