package com.chipon32.debug.ui.view.command;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

public class DebugCommandLine extends Composite {
	
	 private Label mLabel;
	  
	 private Button mGoButton;
	 
	 private Text mInput;

	public DebugCommandLine(Composite parent) {
		super(parent, SWT.NONE);
		GridLayout layout = new GridLayout(4, false);
	    layout.horizontalSpacing = 1;
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 1;
	    layout.marginWidth = 1;
	    layout.marginLeft = 2;
	    setLayout(layout);
	    
	    this.mLabel = new Label(this, 0);
	    this.mLabel.setText("command:");
	    this.mInput = new Text(this, 2180);
	    this.mGoButton = new Button(this, 0);
	    this.mGoButton.setText("Submit");
	    this.mLabel.setLayoutData(new GridData(36));
	    this.mInput.setLayoutData(new GridData(4, 16777216, true, false));
	    this.mGoButton.setLayoutData(new GridData(68));
	}

	public Text getTextWidget() {
		return this.mInput;
	}

	public Button getmGoButton() {
		return mGoButton;
	}
	
}
