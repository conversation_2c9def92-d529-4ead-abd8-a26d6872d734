package com.chipon32.debug.ui.view.command;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.events.ControlAdapter;
import org.eclipse.swt.events.ControlEvent;
import org.eclipse.swt.events.ControlListener;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.console.TextConsole;
import org.eclipse.ui.internal.console.IOConsoleViewer;
import org.eclipse.ui.progress.WorkbenchJob;

public class RevealingIOConsoleViewer extends IOConsoleViewer {
  private final WorkbenchJob scrollBottomLeftJob;
  
  public RevealingIOConsoleViewer(Composite parent, TextConsole console) {
    super(parent, console);
    this.scrollBottomLeftJob = new WorkbenchJob("Reveal document bottom left") {
        public IStatus runInUIThread(IProgressMonitor monitor) {
          RevealingIOConsoleViewer.this.scrollToDocumentBottomLeft();
          return Status.OK_STATUS;
        }
      };
    this.scrollBottomLeftJob.setSystem(true);
    parent.addControlListener((ControlListener)new ControlAdapter() {
          public void controlResized(ControlEvent e) {
            if (RevealingIOConsoleViewer.this.isAutoScroll())
              RevealingIOConsoleViewer.this.revealEndOfDocument(); 
          }
        });
  }
  
  protected void scrollToDocumentBottomLeft() {
    StyledText textWidget = getTextWidget();
    if (textWidget != null && !textWidget.isDisposed()) {
      int lineCount = textWidget.getLineCount();
      textWidget.setTopIndex((lineCount > 0) ? (lineCount - 1) : 0);
      textWidget.setHorizontalIndex(0);
    } 
  }
  
  protected void revealEndOfDocument() {
    this.scrollBottomLeftJob.schedule(50L);
  }
}

