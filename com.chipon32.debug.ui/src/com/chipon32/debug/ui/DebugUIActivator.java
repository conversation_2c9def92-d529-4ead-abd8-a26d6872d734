package com.chipon32.debug.ui;

import java.net.URL;
import java.util.Enumeration;

import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.eclipse.ui.progress.WorkbenchJob;
import org.osgi.framework.BundleContext;



/**
 * The activator class controls the plug-in life cycle
 */
public class DebugUIActivator extends AbstractUIPlugin {

	// The plug-in ID
	public static final String PLUGIN_ID = "com.chipon32.debug.ui"; //$NON-NLS-1$

	// The shared instance
	private static DebugUIActivator plugin;
	
	/**
	 * The constructor
	 */
	public DebugUIActivator() {
	}

	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#start(org.osgi.framework.BundleContext)
	 */
	@Override
	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
		WorkbenchJob wjob = new WorkbenchJob("Initializing Chipon Debug UI") { //$NON-NLS-1$
			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				startupInUIThread();
				return Status.OK_STATUS;
			}
		};
		wjob.schedule();
	}
	
	private void startupInUIThread() {
		ChiponEvaluationContextManager.startup();
	}

	/*
	 * (non-Javadoc)
	 * @see org.eclipse.ui.plugin.AbstractUIPlugin#stop(org.osgi.framework.BundleContext)
	 */
	@Override
	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	/**
	 * Returns the shared instance
	 *
	 * @return the shared instance
	 */
	public static DebugUIActivator getDefault() {
		return plugin;
	}
	
	/**
	   * Returns the absolut path of a entrie from the plugin's directory.
	   * 
	   * @param entrie a file or directory (don't use "dir1\dir2" or "dir1\file1")
	   * 
	   * @return Returns the path from the plugin.
	   */
	  public static String getFilePathFromPlugin(String entrie) {
	    URL url = null;
	    IPath path = null;
	    String result = "";

	    Enumeration<URL> enu = DebugUIActivator.getDefault().getBundle().findEntries("/", entrie, true);
//	    if(entrie.equals("/icons/monitorexpression_tsk.gif")){
//	    	enu = UiActivator.getDefault().getBundle().findEntries("/", entrie, false);
//	    }
	    if(enu == null){
	    	result = "";
	    }else{
			if (enu.hasMoreElements()) {
				url = enu.nextElement();
			}
			try {
				path = new Path(FileLocator.toFileURL(url).getPath());
				result = path.makeAbsolute().toOSString();
			} catch (Exception e) {
				result = "";
			}
	    }	   
	    return result;
	  }
	  
	  
	  /**
		 * Returns the active workbench shell, or the shell from the first available
		 * workbench window, or <code>null</code> if neither is available.
		 */
		public static Shell getActiveWorkbenchShell() {
			IWorkbenchWindow window = getActiveWorkbenchWindow();
			if (window == null) {
				IWorkbenchWindow[] windows = PlatformUI.getWorkbench().getWorkbenchWindows();
				if (windows.length > 0) {
					return windows[0].getShell();
				}
			}
			else {
				return window.getShell();
			}
			return null;
		}
	

	public static IWorkbenchWindow getActiveWorkbenchWindow() {
//		return getDefault().getWorkbench().getActiveWorkbenchWindow();
		return PlatformUI.getWorkbench().getActiveWorkbenchWindow();
	}

	/**
	 * Returns an image descriptor for the image file at the given
	 * plug-in relative path
	 *
	 * @param path the path
	 * @return the image descriptor
	 */
	public static ImageDescriptor getImageDescriptor(String path) {
		return imageDescriptorFromPlugin(PLUGIN_ID, path);
	}
	
}
