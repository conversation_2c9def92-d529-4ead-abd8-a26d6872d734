package com.chipon32.debug.ui.handlers;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.core.runtime.IAdaptable;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponLabelView;

//删除所有的标签的动作类
public class RemoveAllLabelHand<PERSON> extends AbstractHandler{
	
	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {

		ChiponThread  fThread=null;
		ChiponStackFrame frame = null;
        IAdaptable debugContext = DebugUITools.getDebugContext();
        if (debugContext instanceof ChiponStackFrame) {
           frame = (ChiponStackFrame) debugContext;
        } else if (debugContext instanceof ChiponThread) {
        	
        } else if (debugContext instanceof ChiponDebugTarget) {
        	
        }
        
        //在这里通过frame发送命令
        if(frame != null){
         List<ChiponLabelData> list;
         fThread=(ChiponThread) frame.getThread();
        if(fThread!=null && ChiponThread.labelDataList!=null && ChiponThread.labelDataList.size()>1){
        	 list= ChiponThread.labelDataList;
         	
         	List<ChiponLabelData> newList=new ArrayList<ChiponLabelData>();
         	for(int i=0;i<list.size();i++){
         		ChiponLabelData chiponData=list.get(i);
         		if(chiponData.getAddress()!=null && chiponData.getName().equals("+") && chiponData.getName()!=null){
         			newList.add(chiponData);
         		}
         		
         	}
         	ChiponThread.labelDataList.removeAll(list);
         	ChiponThread.labelDataList.addAll(newList);
        }
        boolean isLabelViewOpen=false;
		IViewPart labelViewPart = null;
		 IWorkbenchWindow[] windows = PlatformUI.getWorkbench().getWorkbenchWindows();
		 for(IWorkbenchWindow window : windows){
			 for(IWorkbenchPage page : window.getPages()){
				 for(IViewReference viewReference : page.getViewReferences()){
				
					 if(ChiponLabelView.ID.equals(viewReference.getId())){
						 isLabelViewOpen = true;
						 labelViewPart = viewReference.getView(false);
					 }
				 }
			 }
		 }
		 if(isLabelViewOpen && labelViewPart!=null && labelViewPart instanceof ChiponLabelView){
			 final Viewer tableViewer =((ChiponLabelView)labelViewPart).getViewer();
			 tableViewer.setInput(ChiponThread.labelDataList);
    		 tableViewer.refresh();
				
		 }
       
        }
		
		return null;
	}

}
