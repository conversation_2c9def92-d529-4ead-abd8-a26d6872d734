package com.chipon32.debug.ui.handlers;

import java.util.List;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IViewReference;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.handlers.HandlerUtil;
import com.chipon32.debug.core.model.ChiponLabelData;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.ui.views.ChiponLabelView;

public class RemoveLabelHandler extends AbstractHandler {

	
	
	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
		
		ChiponLabelData data = null;
		ISelection selection=HandlerUtil.getActiveWorkbenchWindow(event).getActivePage().getSelection();
		
		 Object element = ((IStructuredSelection)selection).getFirstElement();
		if (element instanceof ChiponLabelData) {
			data=(ChiponLabelData)element;
			
        }
		
		
        data.getfThread();
		data.getfThread();
		if(data!=null && data.getfThread()!=null && ChiponThread.labelDataList!=null && ChiponThread.labelDataList.size()>1){
        	data.getfThread();
			List<ChiponLabelData> list =ChiponThread.labelDataList;
        	for(int i=0;i<list.size();i++){
        		ChiponLabelData chiponData=list.get(i);
        		boolean isRemove=isEquals(data,chiponData);
        		if(data.getName()!=null && data.getName().equals("+")){
        			isRemove=false;
        		}
        		if(isRemove){
        			list.remove(i);
        			boolean isLabelViewOpen=false;
					IViewPart labelViewPart = null;
					 IWorkbenchWindow[] windows = PlatformUI.getWorkbench().getWorkbenchWindows();
					 for(IWorkbenchWindow window : windows){
						 for(IWorkbenchPage page : window.getPages()){
							 for(IViewReference viewReference : page.getViewReferences()){
							
								 if(ChiponLabelView.ID.equals(viewReference.getId())){
									 isLabelViewOpen = true;
									 labelViewPart = viewReference.getView(false);
								 }
							 }
						 }
					 }
					 if(isLabelViewOpen && labelViewPart!=null && labelViewPart instanceof ChiponLabelView){
						 final Viewer tableViewer =((ChiponLabelView)labelViewPart).getViewer();
						 tableViewer.setInput(list);
		        		 tableViewer.refresh();
							
					 }
        		}
        	}
        }
		return null;
	}
	private boolean isEquals(ChiponLabelData newData,ChiponLabelData olderData) {
		
		boolean isEqualsAddress=false;
		boolean isEqualsName=false;
		boolean isEqualsValue=false;
		
		if(!(newData.getAddress()==null && olderData.getAddress()==null)){
			if(newData.getAddress()!=null && olderData.getAddress()!=null && newData.getAddress().equals(olderData.getAddress())){
				isEqualsAddress=true;
			}
		}else {
			isEqualsAddress=true;
		}
		
		if(!(newData.getName()==null && olderData.getName()==null)){
			if(newData.getName()!=null && olderData.getName()!=null && newData.getName().equals(olderData.getName())){
				isEqualsName=true;
			}
		}else {
			isEqualsName=true;
		}
		
		if(!(newData.getValue()==null && olderData.getValue()==null)){
			if(newData.getValue()!=null && olderData.getValue()!=null && newData.getValue().equals(olderData.getValue())){
				isEqualsValue=true;
			}
		}else {
			isEqualsValue=true;
		}
		
		return isEqualsAddress && isEqualsName && isEqualsValue;
	}
	}
