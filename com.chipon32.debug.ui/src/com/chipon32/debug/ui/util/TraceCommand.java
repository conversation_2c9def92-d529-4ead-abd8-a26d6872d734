package com.chipon32.debug.ui.util;


import org.eclipse.jface.dialogs.MessageDialog;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.protocol.ChiponStopLocationCommandResult;
import com.chipon32.debug.core.util.ChiponDebugUtil;
import com.chipon32.debug.ui.view.trace.Messages;

public class TraceCommand {
	
	public static int currCommandNum = 0;
	public static int commandCount = 0;
	
	/**
	 * @param fThread
	 * @param target
	 * @param strCommand
	 * @param commandtype // 0 删除列表 1删除当前 2添加符号 3添加地址 4 查看列表 5 列表同步与赋值列表
	 */
	public static synchronized boolean dealCommand(String strCommand, int commandtype) {
		ChiponThread fThread = ChiponDebugUtil.getCurrentChiponThread();
		if (fThread == null || fThread.getChiponDebugTarget() == null) {
			MessageDialog.openError(null, Messages.chartView_52, Messages.chartView_53);
			return false;
			
		} else {
			ChiponDebugTarget target = fThread.getChiponDebugTarget();
			if (target.isTerminated()) {
				MessageDialog.openError(null, Messages.chartView_54, Messages.chartView_55);
				return false;
			} else if (!target.isSuspended()) {
				MessageDialog.openError(null, Messages.chartView_56, Messages.chartView_57);
				return false;
			}
		}
		//执行命令，并获取返回结果
		ChiponStopLocationCommandResult result = new ChiponStopLocationCommandResult(null);
		try {
			fThread.manual(strCommand);
			result = fThread.getLocationCommandResult();
			if(result == null) {
//				System.out.println("command error!");
				MessageDialog.openError(null, Messages.chartView_62, "command error!");
				return false;
			}
		} catch (Exception e) {
//			System.out.println("command error!");
			MessageDialog.openError(null, Messages.chartView_62, "command error!");
			return false;
		}
			
		//#####################################################################################				
		if (result.resultText == null || !result.resultText.equals("success")) { //$NON-NLS-1$
			//命令执行不成功
			String bufshowmessage = ""; //$NON-NLS-1$
			for (String str : result.resultout) {
				if (!str.contains("kf32command fail!")) //$NON-NLS-1$
					bufshowmessage += str + "\r\n"; //$NON-NLS-1$
			}
			MessageDialog.openError(null, Messages.chartView_62, bufshowmessage);
			return false;
			
		}else {//命令执行成功
			String bufshowmessage = ""; //$NON-NLS-1$
			if (commandtype == 5) {// 列表同步
//				List<String> getin = new ArrayList<>();
//				for (String str : result.resultout) {
//					if (str.startsWith("figure_oscillographinfo:") || str.startsWith("(gdb) figure_oscillographinfo:")) //$NON-NLS-1$ //$NON-NLS-2$
//					{
//						str = str.replace("(gdb) figure_oscillographinfo:", ""); //$NON-NLS-1$ //$NON-NLS-2$
//						str = str.replace("figure_oscillographinfo:", ""); //$NON-NLS-1$ //$NON-NLS-2$
//						str = str.replace("(Symbol:", "-"); //$NON-NLS-1$ //$NON-NLS-2$
//						str = str.replace("(Addr:", "-"); //$NON-NLS-1$ //$NON-NLS-2$
//						String numBuf = str.substring(0, str.indexOf("-")); //$NON-NLS-1$
//						numBuf = Integer.toString(Integer.parseInt(numBuf) + 1);
//						str = str.substring(str.indexOf("-")); //$NON-NLS-1$
//						str = str.substring(0, str.lastIndexOf("-")); //$NON-NLS-1$
//						str = str.substring(0, str.lastIndexOf("-")); //$NON-NLS-1$
//						str = numBuf + str + "-"; // 标记是同步来源 //$NON-NLS-1$
//						getin.add(str);
//					}
//				}//end for
				
			}else if (commandtype == 2 || commandtype == 3) { // 地址或符号添加
				
				for (String str : result.resultout) {
					if (str.contains("figure_add")) { //$NON-NLS-1$
						bufshowmessage = str;
						break;
					}
				}//end for
				// #########################################################
				if (bufshowmessage.startsWith("(gdb) ")) //$NON-NLS-1$
					bufshowmessage = bufshowmessage.replace("(gdb) ", ""); //$NON-NLS-1$ //$NON-NLS-2$
				if (bufshowmessage.startsWith("figure_add:")) //$NON-NLS-1$
					bufshowmessage = bufshowmessage.replace("figure_add:", ""); //$NON-NLS-1$ //$NON-NLS-2$

				if (bufshowmessage == null || bufshowmessage.length() < 1 || !bufshowmessage.contains("(")) { //$NON-NLS-1$
					MessageDialog.openError(null, Messages.chartView_88, Messages.chartView_89);
					return false;
				}
				String userNum = bufshowmessage.substring(0, bufshowmessage.indexOf("(")); //$NON-NLS-1$
				currCommandNum = Integer.parseInt(userNum);
				commandCount++;
				
			}else if(commandtype == 0) { //删除所有监控变量
				commandCount = 0;
				
			}else if(commandtype == 1){//删除当前监控变量
				commandCount--;

			}
			
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
//				e.printStackTrace();
			}
			
			return true;
		}
		
	}

}
