package com.chipon32.debug.ui.editors;

import org.eclipse.jface.text.ITextHover;
import org.eclipse.jface.text.source.IAnnotationHover;
import org.eclipse.jface.text.source.ISourceViewer;
import org.eclipse.ui.editors.text.TextSourceViewerConfiguration;

public class ChiponSourceViewerConfiguration extends
		TextSourceViewerConfiguration {
	
	 @Override
	public ITextHover getTextHover(ISourceViewer sourceViewer, String contentType) {
	        return new DebugTextHover();
	    }
	 
		/* (non-Javadoc)
		 * @see org.eclipse.jface.text.source.SourceViewerConfiguration#getAnnotationHover(org.eclipse.jface.text.source.ISourceViewer)
		 */
		@Override
		public IAnnotationHover getAnnotationHover(ISourceViewer sourceViewer) {
			return new AnnotationHover();
		}
		
		  /* (non-Javadoc)
	     * @see org.eclipse.jface.text.source.SourceViewerConfiguration#getPresentationReconciler(org.eclipse.jface.text.source.ISourceViewer)
	     */
//	    public IPresentationReconciler getPresentationReconciler(ISourceViewer sourceViewer) {
//	        PresentationReconciler reconciler = new PresentationReconciler();
//	        reconciler.setDocumentPartitioning(getConfiguredDocumentPartitioning(sourceViewer));
//	        DefaultDamagerRepairer dr = new DefaultDamagerRepairer(new PDAScanner());
//	        reconciler.setDamager(dr, IDocument.DEFAULT_CONTENT_TYPE);
//	        reconciler.setRepairer(dr, IDocument.DEFAULT_CONTENT_TYPE);
//	        return reconciler;
//	    }
}
