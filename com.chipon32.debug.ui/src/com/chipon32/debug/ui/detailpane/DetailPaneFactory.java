package com.chipon32.debug.ui.detailpane;

import java.util.Set;

import org.eclipse.debug.ui.IDetailPane;
import org.eclipse.debug.ui.IDetailPaneFactory;
import org.eclipse.jface.viewers.IStructuredSelection;

public class DetailPaneFactory  implements IDetailPaneFactory {

	@Override
	public Set getDetailPaneTypes(IStructuredSelection selection) {
		return null;
	}

	@Override
	public String getDefaultDetailPane(IStructuredSelection selection) {
		return "a";
	}

	@Override
	public IDetailPane createDetailPane(String paneID) {
		return null;
	}

	@Override
	public String getDetailPaneName(String paneID) {
		return "b";
	}

	@Override
	public String getDetailPaneDescription(String paneID) {
		return "c";
	}

}
