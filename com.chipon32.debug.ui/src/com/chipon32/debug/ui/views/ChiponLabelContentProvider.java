package com.chipon32.debug.ui.views;

import java.util.List;

import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.debug.core.model.ChiponLabelData;

/***
 *<AUTHOR>
 *2013-6-20下午2:11:21
 *内容类     由此类对输入到表格的数据进行筛选和转化。此类实现接口的3种方法
 ***/
public class ChiponLabelContentProvider implements IStructuredContentProvider {

	@Override
	public void dispose() {
		// TODO Auto-generated method stub
	}

	//当Table Viewer再次调用setInput()时触发执行此方法
	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		if(newInput == null || oldInput == null){
			return ;
		}
		if(!(oldInput instanceof List) || !(newInput instanceof List)){
			 return ;
		}
		List<ChiponLabelData> oldList = (List) oldInput;
		List<ChiponLabelData> newList = (List) newInput;
		for(int i=0; i<oldList.size(); i++){
			ChiponLabelData oldDate=oldList.get(i);
			for(int j=0;j<newList.size();j++){
				ChiponLabelData newDate=newList.get(j);
				if(oldDate!=null && oldDate.getAddress()!=null && oldDate.getAddress().length()>0 && newDate!=null && newDate.getAddress()!=null && newDate.getAddress().length()>0 && oldDate.getAddress().equals(newDate.getAddress())){
					if(oldDate.getValue()!=null && newDate.getValue()!=null && !(oldDate.getValue().equals(newDate.getValue()))){
						newDate.setColor(IChiponColor.RED_COLOR);
					}else {
						newDate.setColor(IChiponColor.DEFAULT_COLOR);
					}
				}
			}
		}
	}

	@Override
	public Object[] getElements(Object inputElement) {
		if(inputElement instanceof List){
			return ((List<?>) inputElement).toArray();
		}
		return null;
	}
}
