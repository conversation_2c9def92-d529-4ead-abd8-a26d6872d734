package com.chipon32.debug.ui.views;

import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.part.ViewPart;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;

/**
 * 
 * 
 * */
public class StopWatchview extends ViewPart {

	public static final String ID = "com.chipon32.debug.ui.views.StopWatchview"; //$NON-NLS-1$
	private Text txtStopwacth;
	private Text txtTotal;
	private Text txtZeroStopValue;
	private Text txtZeroTotalValue;
	private Text txtProcessor;
	
//	private final int WIDTH = 60;
//	private final int HEIGHT = 20;

	public StopWatchview() {
	}

	/**
	 * Create contents of the view part.
	 * @param parent
	 */
	@Override
	public void createPartControl(Composite parent) {
		Composite container = new Composite(parent, SWT.NONE);
		container.setLayout(new GridLayout(4, false));
		new Label(container, SWT.NONE);
		new Label(container, SWT.NONE);
		{
			Label lblStopWacth = new Label(container, SWT.NONE);
			lblStopWacth.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 1, 1));
			lblStopWacth.setText(Messages.StopWatchview_0);
		}
		{
			Label lblTotal = new Label(container, SWT.NONE);
			lblTotal.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 1, 1));
			lblTotal.setText(Messages.StopWatchview_1);
		}
		{
			Button btnSynch = new Button(container, SWT.NONE);
			btnSynch.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
				}
			});
			btnSynch.setText(Messages.StopWatchview_2);
		}
		{
			Label lblInstruction = new Label(container, SWT.NONE);
			lblInstruction.setLayoutData(new GridData(SWT.RIGHT, SWT.CENTER, false, false, 1, 1));
			lblInstruction.setText(Messages.StopWatchview_3);
		}
		{
			txtStopwacth = new Text(container, SWT.BORDER | SWT.CENTER);
			txtStopwacth.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
			txtStopwacth.setEditable(false);
			txtStopwacth.setText("value"); //$NON-NLS-1$
		}
		{
			txtTotal = new Text(container, SWT.BORDER | SWT.CENTER);
			txtTotal.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
			txtTotal.setEditable(false);
			txtTotal.setText("value"); //$NON-NLS-1$
		}
		{
			Button btnZero = new Button(container, SWT.NONE);
			btnZero.addSelectionListener(new SelectionAdapter() {
				@Override
				public void widgetSelected(SelectionEvent e) {
				}
			});
			btnZero.setText(Messages.StopWatchview_6);
		}
		{
			Label lblTime = new Label(container, SWT.NONE);
			lblTime.setText(Messages.StopWatchview_7);
		}
		{
			txtZeroStopValue = new Text(container, SWT.BORDER | SWT.CENTER);
			txtZeroStopValue.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
			txtZeroStopValue.setEditable(false);
			txtZeroStopValue.setText("value"); //$NON-NLS-1$
		}
		{
			txtZeroTotalValue = new Text(container, SWT.BORDER | SWT.CENTER);
			txtZeroTotalValue.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
			txtZeroTotalValue.setEditable(false);
			txtZeroTotalValue.setText("value"); //$NON-NLS-1$
		}
		{
			Label lblProcessor = new Label(container, SWT.NONE);
			lblProcessor.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 1, 1));
			lblProcessor.setText(Messages.StopWatchview_10);
		}
		{
			txtProcessor = new Text(container, SWT.BORDER | SWT.CENTER);
			txtProcessor.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 2, 1));
			txtProcessor.setText("value"); //$NON-NLS-1$
		}
		
		Composite composite = new Composite(container, SWT.NONE);
		composite.setLayoutData(new GridData(SWT.CENTER, SWT.CENTER, false, false, 1, 1));
		composite.setLayout(new GridLayout(1, false));

		Button btnrdoMhz = new Button(composite, SWT.RADIO);
		btnrdoMhz.setText("MHZ"); //$NON-NLS-1$

		Button btnrdoKHZ = new Button(composite, SWT.RADIO);
		btnrdoKHZ.setText("KHZ"); //$NON-NLS-1$

		Button btnrdoHZ = new Button(composite, SWT.RADIO);
		btnrdoHZ.setText("HZ"); //$NON-NLS-1$

		createActions();
		initializeToolBar();
		initializeMenu();
	}

	/**
	 * Create the actions.
	 */
	private void createActions() {
		// Create the actions
	}

	/**
	 * Initialize the toolbar.
	 */
	private void initializeToolBar() {
		IToolBarManager toolbarManager = getViewSite().getActionBars()
				.getToolBarManager();
	}

	/**
	 * Initialize the menu.
	 */
	private void initializeMenu() {
		IMenuManager menuManager = getViewSite().getActionBars()
				.getMenuManager();
	}

	@Override
	public void setFocus() {
		// Set the focus
	}

}
