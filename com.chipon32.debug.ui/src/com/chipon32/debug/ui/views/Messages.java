package com.chipon32.debug.ui.views;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.debug.ui.views.messages"; //$NON-NLS-1$
	public static String Chipon_CMD_Debug_0;
	public static String Chipon_CMD_Debug_1;
	public static String Chipon_CMD_Debug_12;
	public static String Chipon_CMD_Debug_13;
	public static String Chipon_CMD_Debug_15;
	public static String Chipon_CMD_Debug_18;
	public static String Chipon_CMD_Debug_31;
	public static String Chipon_CMD_Debug_33;
	public static String Chipon_CMD_Debug_37;
	public static String Chipon_CMD_Debug_39;
	public static String Chipon_CMD_Debug_5;
	public static String ChiponACCXView_1;
	public static String ChiponACCXView_3;
	public static String ChiponACCXView_4;
	public static String ChiponACCXView_5;
	public static String ChiponACCXView_6;
	public static String ChiponCanRunStepTester_1;
	public static String ChiponCurrencyRegisterView_1;
	public static String ChiponCurrencyRegisterView_4;
	public static String ChiponCurrencyRegisterView_5;
	public static String ChiponCurrencyRegisterView_6;
	public static String ChiponCurrentRegisterCellModifier_0;
	public static String ChiponCurrentRegisterCellModifier_1;
	public static String ChiponCurrentRegisterCellModifier_11;
	public static String ChiponCurrentRegisterCellModifier_15;
	public static String ChiponCurrentRegisterCellModifier_16;
	public static String ChiponCurrentRegisterCellModifier_19;
	public static String ChiponCurrentRegisterCellModifier_20;
	public static String ChiponCurrentRegisterCellModifier_21;
	public static String ChiponCurrentRegisterCellModifier_22;
	public static String ChiponCurrentRegisterCellModifier_27;
	public static String ChiponCurrentRegisterCellModifier_31;
	public static String ChiponCurrentRegisterCellModifier_32;
	public static String ChiponCurrentRegisterCellModifier_34;
	public static String ChiponCurrentRegisterCellModifier_35;
	public static String ChiponCurrentRegisterCellModifier_38;
	public static String ChiponCurrentRegisterCellModifier_39;
	public static String ChiponCurrentRegisterCellModifier_41;
	public static String ChiponCurrentRegisterCellModifier_42;
	public static String ChiponCurrentRegisterCellModifier_44;
	public static String ChiponCurrentRegisterCellModifier_45;
	public static String ChiponCurrentRegisterCellModifier_46;
	public static String ChiponCurrentRegisterCellModifier_47;
	public static String ChiponCurrentRegisterCellModifier_49;
	public static String ChiponCurrentRegisterCellModifier_50;
	public static String ChiponEEPROMView_2;
	public static String ChiponEEPROMView_28;
	public static String ChiponEEPROMView_29;
	public static String ChiponEEPROMView_30;
	public static String ChiponEEPROMView_31;
	public static String ChiponEEPROMView_34;
	public static String ChiponEEPROMView_35;
	public static String ChiponEEPROMView_4;
	public static String ChiponEEPROMView_5;
	public static String ChiponGroupMemoryCellModifier_0;
	public static String ChiponGroupMemoryCellModifier_2;
	public static String ChiponGroupMemoryCellModifier_4;
	public static String ChiponLabelCellModifier_10;
	public static String ChiponLabelCellModifier_16;
	public static String ChiponLabelCellModifier_17;
	public static String ChiponLabelCellModifier_18;
	public static String ChiponLabelCellModifier_24;
	public static String ChiponLabelCellModifier_25;
	public static String ChiponLabelCellModifier_26;
	public static String ChiponLabelCellModifier_28;
	public static String ChiponLabelCellModifier_29;
	public static String ChiponLabelCellModifier_35;
	public static String ChiponLabelCellModifier_36;
	public static String ChiponLabelCellModifier_37;
	public static String ChiponLabelCellModifier_42;
	public static String ChiponLabelCellModifier_43;
	public static String ChiponLabelCellModifier_44;
	public static String ChiponLabelCellModifier_8;
	public static String ChiponLabelCellModifier_9;
	public static String ChiponLabelLableProvider_3;
	public static String ChiponLabelView_1;
	public static String ChiponLabelView_2;
	public static String ChiponLabelView_3;
	public static String ChiponLabelView_4;
	public static String ChiponLabelView_5;
	
	public static String ChiponMemoryView1_0;
	public static String ChiponMemoryView1_1;
	public static String ChiponMemoryView1_2;
	public static String ChiponMemoryView1_30;
	public static String ChiponMemoryView1_31;
	public static String ChiponMemoryView1_32;
	public static String ChiponMemoryView1_33;
	public static String ChiponMemoryView1_4;
	public static String ChiponMemoryView1_6;
	public static String ChiponMemoryView1_UP;
	public static String ChiponMemoryView1_DOWN;
	public static String ChiponMemoryView1_Save;
	public static String ChiponMemoryView1_Load;
	
	public static String ChiponMemoryView2_0;
	public static String ChiponMemoryView2_1;
	public static String ChiponMemoryView2_30;
	public static String ChiponMemoryView2_31;
	public static String ChiponMemoryView2_32;
	public static String ChiponMemoryView2_33;
	public static String ChiponMemoryView2_35;
	public static String ChiponMemoryView2_4;
	public static String ChiponMemoryView3_1;
	public static String ChiponMemoryView3_30;
	public static String ChiponMemoryView3_31;
	public static String ChiponMemoryView3_32;
	public static String ChiponMemoryView3_33;
	public static String ChiponMemoryView3_35;
	public static String ChiponMemoryView3_4;
	public static String ChiponMemoryView3_6;
	public static String ChiponMemoryView4_1;
	public static String ChiponMemoryView4_30;
	public static String ChiponMemoryView4_31;
	public static String ChiponMemoryView4_32;
	public static String ChiponMemoryView4_33;
	public static String ChiponMemoryView4_35;
	public static String ChiponMemoryView4_4;
	public static String ChiponMemoryView4_6;
	public static String ChiponRegisterCellModifier_0;
	public static String ChiponRegisterCellModifier_13;
	public static String ChiponRegisterCellModifier_3;
	public static String ChiponRegisterCellModifier_4;
	public static String ChiponRegisterCellModifier_6;
	public static String ChiponRegisterCellModifier_7;
	public static String ChiponRegistersView_1;
	public static String ChiponRegistersView_10;
	public static String ChiponRegistersView_11;
	public static String ChiponRegistersView_12;
	public static String ChiponRegistersView_13;
	public static String ChiponRegistersView_14;
	public static String ChiponRegistersView_15;
	public static String ChiponRegistersView_16;
	public static String ChiponRegistersView_17;
	public static String ChiponRegistersView_18;
	public static String ChiponRegistersView_19;
	public static String ChiponRegistersView_2;
	public static String ChiponRegistersView_3;
	public static String ChiponRegistersView_4;
	public static String ChiponRegistersView_5;
	public static String ChiponRegistersView_6;
	public static String ChiponRegistersView_7;
	public static String ChiponRegistersView_8;
	public static String ChiponRegistersView_9;

	public static String ChiponWachpointCellModifier_0;
	public static String ChiponWachpointCellModifier_3;
	public static String ChiponWachpointCellModifier_5;
	public static String ChiponWatchpointView_1;
	public static String ChiponWatchpointView_10;
	public static String ChiponWatchpointView_11;
	public static String ChiponWatchpointView_2;
	public static String ChiponWatchpointView_24;
	public static String ChiponWatchpointView_25;
	public static String ChiponWatchpointView_32;
	public static String ChiponWatchpointView_33;
	public static String ChiponWatchpointView_34;
	public static String ChiponWatchpointView_35;
	public static String ChiponWatchpointView_39;
	public static String ChiponWatchpointView_4;
	public static String ChiponWatchpointView_40;
	public static String ChiponWatchpointView_41;
	public static String ChiponWatchpointView_42;
	public static String ChiponWatchpointView_43;
	public static String ChiponWatchpointView_44;
	public static String ChiponWatchpointView_47;
	public static String ChiponWatchpointView_6;
	public static String ChiponWatchpointView_7;
	public static String ChiponWatchpointView_9;
	public static String StopWatchview_0;
	public static String StopWatchview_1;
	public static String StopWatchview_10;
	public static String StopWatchview_2;
	public static String StopWatchview_3;
	public static String StopWatchview_6;
	public static String StopWatchview_7;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
