package com.chipon32.debug.ui.views.disassembly;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.jface.text.rules.ICharacterScanner;
import org.eclipse.jface.text.rules.IToken;
import org.eclipse.jface.text.rules.IWordDetector;
import org.eclipse.jface.text.rules.Token;
import org.eclipse.jface.text.rules.WordRule;

/**
 * A special WordRule, which ingnores the case of a given word.
 * 字体规则
 * <AUTHOR>
 * @since 25.11.2005
 */
public class WordRuleCaseInsensitive extends WordRule {

	/**
	 * Buffer used for pattern detection
	 */
	private StringBuffer fBuffer = new StringBuffer();

	/**
	 * The constructor.
	 */
	public WordRuleCaseInsensitive() {
		this(Token.UNDEFINED);
	}

	/**
	 * Creates a rule which. If no token has been associated, the specified
	 * default token will be returned.
	 * 
	 * @param defaultToken
	 *            the default token to be returned on success if nothing else is
	 *            specified, may not be <code>null</code>
	 * 
	 * @see #addWord(String, IToken)
	 */
	public WordRuleCaseInsensitive(IToken defaultToken) {
		super(new IWordDetector() { // A dummy. WordDetector will be
					// replaced a
					// few rows below.
					@Override
					public boolean isWordPart(char c) {
						return false;
					}

					@Override
					public boolean isWordStart(char c) {
						return false;
					}
				}, defaultToken);

		fDetector = new MyWordDetector();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IToken evaluate(ICharacterScanner scanner) {
		int c = 0;
		Pattern pattern = Pattern.compile("^#");
		Matcher matcher = null;
		if (scanner.getColumn() > 0) {
			scanner.unread();
			c = scanner.read();
//			/*
//			 * 下面三个判断语句主要是为了判断读入的字符是否为,[] 如果是就把他们强制转换成空格
//			 */
//			if (!((c >= 'a' && c <= 'z') || (c >= 'a' && c <= 'z') || (c >= '0' && c <= '9') || c == '_' || c == '.')) {
//				c = 32;
//			}

			 if(c == 44){
			 c=32;
			 }
			 if(c == 91){
			 c=32;
			 }
			 if(c == 93){
			 c=32;
			 }
			
			if (!Character.isWhitespace((char) c)) {
				return fDefaultToken;
			}
		}

		c = scanner.read();
		 if(c == 44){
		 c=32;
		 }
		 if(c == 91){
		 c=32;
		 }
		 if(c == 93){
		 c=32;
		 }
//		 除了"0~9"、"a~z"、"A~Z"、"."、"_"以外的字符全部被识别为空格

		c = Character.toLowerCase((char) c);
		if (fDetector.isWordStart((char) c)) {
			if (fColumn == UNDEFINED || (fColumn == scanner.getColumn() - 1)) {

				fBuffer.setLength(0);
				do {
					fBuffer.append((char) c);
					c = scanner.read();
					 if(c == 44){
					 c=32;
					 }
					 if(c == 91){
					 c=32;
					 }
					 if(c == 93){
					 c=32;
					 }
					 if(c == 43){
					 c=32;
					 }
//					if (!((c >= 'a' && c <= 'z') || 
//							(c >= 'a' && c <= 'z') || 
//							(c >= '0' && c <= '9') || 
//							c == '_' || c == '.' || c == '#')) {
//						c = 32;
//					}
					c = Character.toLowerCase((char) c);
				} while (c != ICharacterScanner.EOF && fDetector.isWordPart((char) c));
				scanner.unread();

				/*
				 * 下面一段代码是为了匹配头字母带有#的字符串 然后根据#返回Token
				 */
				matcher = pattern.matcher(fBuffer.toString());
				if (fWords.containsKey("#")) {

					if (matcher.find()) {
						// return (IToken) fWords.get(fBuffer.toString());
						// System.err.println(fBuffer.toString());
						IToken token = (IToken) fWords.get("#");
						if (token != null) {
							return token;
						}
					}
				}

				IToken token = (IToken) fWords.get(fBuffer.toString());
				if (token != null) {
					return token;
				}

				if (fDefaultToken.isUndefined()) {
					unreadBuffer(scanner);
				}

				return fDefaultToken;
			}
		}

		scanner.unread();
		return Token.UNDEFINED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void addWord(String word, IToken token) {
		super.addWord(word.toLowerCase(), token);
	}

	/**
	 * Returns the characters in the buffer to the scanner.
	 * 
	 * @param scanner
	 *            the scanner to be used
	 */
	@Override
	protected void unreadBuffer(ICharacterScanner scanner) {
		for (int i = fBuffer.length() - 1; i > -1; i--) {
			scanner.unread();
		}
	}

	/**
	 * A WordDetector, which recognizes all typable characters.
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class MyWordDetector implements IWordDetector {
		/**
		 * {@inheritDoc}
		 */
		@Override
		public boolean isWordStart(char c) {
			return ((c > ' ') && (c <= '~'));
		}

		/**
		 * {@inheritDoc}
		 */
		@Override
		public boolean isWordPart(char c) {
			return ((c > ' ') && (c <= '~'));
		}
	}

	public static void main(String[] args) {
		char c = '+';
		int d = c;
		System.out.printf("%d", d);
	}
}
