package com.chipon32.debug.ui.views;

import java.math.BigInteger;

public class ChiponMemoryServiceClass {
	
	static public String[] datetype = new String[]{
			"hex8",	


			
			"uchar",	
			"schar",	
			
			"hex16",
			"ushort",
			"sshort",	
			
			"hex32",
			"uint",
			"sint",
			
			"hex64",
			"ulonglong",			
			"slonglong",			
			"float",			
			"double",
		  };
	
	
	static public int getFormatBytes(String format)
	{
		if(format.equalsIgnoreCase("uchar"))
			return 1;
		else 		if(format.equalsIgnoreCase("schar"))
			return 1;
		
		else if(format.equalsIgnoreCase("ushort"))
			return 2;
		else 		if(format.equalsIgnoreCase("sshort"))
			return 2;	
		
		else if(format.equalsIgnoreCase("uint"))
			return 4;
		else 		if(format.equalsIgnoreCase("sint"))
			return 4;	
		
		else if(format.equalsIgnoreCase("ulonglong"))
			return 8;
		else 		if(format.equalsIgnoreCase("slonglong"))
			return 8;	
		
		else if(format.equalsIgnoreCase("float"))
			return 4;
		else 		if(format.equalsIgnoreCase("double"))
			return 8;	
		
		else 		if(format.equalsIgnoreCase("hex8"))
			return 1;
		else 		if(format.equalsIgnoreCase("hex16"))
			return 2;
		else 		if(format.equalsIgnoreCase("hex32"))
			return 4;
		else 		if(format.equalsIgnoreCase("hex64"))
			return 8;
		else
			return 1;
	}
	
	static public boolean getIsSigned(String format)
	{
		if(format.startsWith("s"))return true;
		if(format.startsWith("f"))return true;
		if(format.startsWith("d"))return true;
		return false;
	}
	
	// for modiy  watch exp part
	static public String getFullName(String format)
	{
		if(format.equalsIgnoreCase("uchar"))
			return "unsigned char";
		else 		if(format.equalsIgnoreCase("schar"))
			return "signed char";
		
		else if(format.equalsIgnoreCase("ushort"))
			return "unsigned short";
		else 		if(format.equalsIgnoreCase("sshort"))
			return "signed short";
		
		else if(format.equalsIgnoreCase("uint"))
			return "unsigned int";
		else 		if(format.equalsIgnoreCase("sint"))
			return "signed int";
		
		else if(format.equalsIgnoreCase("ulonglong"))
			return "unsigned long long";
		else 		if(format.equalsIgnoreCase("slonglong"))
			return "signed long long";
		
		else if(format.equalsIgnoreCase("float"))
			return "float";
		else 		if(format.equalsIgnoreCase("double"))
			return "double";	
		
		else 		if(format.equalsIgnoreCase("hex8"))
			return "unsigned char";
		else 		if(format.equalsIgnoreCase("hex16"))
			return "unsigned short";
		else 		if(format.equalsIgnoreCase("hex32"))
			return "unsigned int";
		else 		if(format.equalsIgnoreCase("hex64"))
			return "unsigned long long";
		else 
			return "unsigned char";
	}
	
	
// romlist内存16进制多自己字符串值解析到界面显示值.................romlist charshex to ui show	
	static public String ValueStringCovGet(String format,String inHex)
	{
		if(inHex.contains("--")||inHex.contains("??"))
		{
			return "??";
		}
		if(format.equalsIgnoreCase("uchar"))
			return Integer.toString( Integer.parseInt(inHex,16));
		else 	if(format.equalsIgnoreCase("schar"))
		{
			int bufint = Integer.parseInt(inHex,16);
			if(bufint >= 0 && bufint <=127){}
			else
			{
				bufint = 0-(256-bufint);
			}				
			return Integer.toString( bufint) ;
		}
		else if(format.equalsIgnoreCase("ushort"))
			return Integer.toString( Integer.parseInt(inHex,16));
		else 	if(format.equalsIgnoreCase("sshort"))
		//	return Integer.toString( (Integer.parseInt(inHex,16)&0xFFFF)-256*256);
		{
			int bufint = Integer.parseInt(inHex,16);
			if(bufint >= 0 && bufint <=32767){}
			else
			{
				bufint = 0-(65536-bufint);
			}				
			return Integer.toString( bufint) ;
		}
		
		else if( format.equalsIgnoreCase("uint"))
			return Long.toString( Long.parseLong(inHex,16));
		else 	if( format.equalsIgnoreCase("sint"))
			//return Long.toString( (Long.parseLong(inHex,16)&0xFFFFFFFF) - 256*256*256*256);	
		{
			long bufint = Long.parseLong(inHex,16);
			if(bufint >= 0 && bufint <=2147483647){}
			else
			{
				bufint = 0-(65536*65536l-bufint);
			}				
			return Long.toString( bufint) ;
		}
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("ulonglong")){
//			long bufint = Long.parseLong(inHex,16);
			//long bufint = MyLongGetFromHex(inHex);
			//return Long.toString( bufint) ;
						
			BigInteger ddd = new BigInteger(inHex,16);			
			return ddd.toString();
		}		
		else 	if(format.equalsIgnoreCase("slonglong"))
		{
			long bufint = MyLongGetFromHex(inHex);
			return Long.toString( bufint) ;
		}
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("float")){
//			float f1=120.25f;
//			System.out.println(Integer.toHexString(Float.floatToIntBits(f1)));
			Long l=Long.parseLong(inHex,16);
			Float f =Float.intBitsToFloat(l.intValue());
//			DecimalFormat decimalFormat = new DecimalFormat("0.000000");
//			String VALUE = decimalFormat.format(f);
//			return VALUE;
			return Float.toString(f);
		}
		//---------------------------------------------------
		else 		if(format.equalsIgnoreCase("double")){
			//return /*"0x"+*/inHex;	
//			Long l=Long.parseLong(inHex,16);
			Long l = MyLongGetFromHex(inHex);
			Double f =Double.longBitsToDouble(l.longValue());
			return Double.toString(f);
		}
		else 		if(format.equalsIgnoreCase("hex8"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("hex16"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("hex32"))
			return /*"0x"+*/inHex;	
		else 		if(format.equalsIgnoreCase("hex64"))
			return /*"0x"+*/inHex;	
		else 
		//---------------------------------------------------
			return /*"0x"+*/inHex;
	}
	// ui show for gdb  set 
	static public String ValueStringCovSet(String format,String inHex)
	{
		if(format.equalsIgnoreCase("uchar"))
			return inHex;
		else 	if(format.equalsIgnoreCase("schar"))
			return inHex;
		
		else if(format.equalsIgnoreCase("ushort"))
			return inHex;
		else 	if(format.equalsIgnoreCase("sshort"))
			return inHex;
		
		else if( format.equalsIgnoreCase("uint"))
			return inHex;
		else 	if( format.equalsIgnoreCase("sint"))
			return inHex;	
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("ulonglong"))
			return inHex;	
		else 		if(format.equalsIgnoreCase("slonglong"))
			return inHex;	
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("float")){
			return inHex;	
		}
		//---------------------------------------------------
		else 		if(format.equalsIgnoreCase("double"))
//			return "0x"+inHex;	
			return inHex;	
		//---------------------------------------------------
		else 		if(format.equalsIgnoreCase("hex8"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("hex16"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("hex32"))
			return   "0x"+inHex;	
		else 		if(format.equalsIgnoreCase("hex64"))
			return   "0x"+inHex;	
		else 
			return 	 "0x"+inHex;
		//---------------------------------------------------
	}
	// for ui   re  to rom ele  to charhex ele
	static public String ValueStringCovReSet(String format,String inStr)
	{
		if(format.equalsIgnoreCase("uchar"))
		{
			int buf=Integer.parseInt(inStr);
			
				return toLength(	Integer.toHexString(buf)	,1);
		}
		else 	if(format.equalsIgnoreCase("schar"))
		{
			int buf=Integer.parseInt(inStr);			
			return toLength(	Integer.toHexString(buf)	,1);
		}
		//-------------------------------------
		else if(format.equalsIgnoreCase("ushort"))
		{
			int buf=Integer.parseInt(inStr);
			
				return toLength(	Integer.toHexString(buf)	,2);
		}
		else 	if(format.equalsIgnoreCase("sshort"))
		{
			int buf=Integer.parseInt(inStr);
			
				return toLength(	Integer.toHexString(buf)	,2);
		}
		
		else if( format.equalsIgnoreCase("uint"))
		{
			long buf=Long.parseLong(inStr);
			
				return toLength(	Long.toHexString(buf)	,4);
		}
		else 	if( format.equalsIgnoreCase("sint"))
		{
			long buf=Long.parseLong(inStr);
			
				return toLength(	Long.toHexString(buf)	,4);
		}
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("ulonglong")){
			//long buf=Long.parseLong(inStr);
			//return toLength(	Long.toHexString(buf)	,8);
			BigInteger ddd = new BigInteger(inStr,10);		
			return toLength(	ddd.toString(16)	,8);
		}
		else 		if(format.equalsIgnoreCase("slonglong")){
			long buf=Long.parseLong(inStr);
			return toLength(	Long.toHexString(buf)	,8);
		}
		//---------------------------------------------------
		else if(format.equalsIgnoreCase("float")){
//			float f1=120.25f;
//			System.out.println(Integer.toHexString(Float.floatToIntBits(f1)));
			float f1 = Float.parseFloat(inStr);
			return toLength( Integer.toHexString(Float.floatToIntBits(f1))	,4);
		}
		//---------------------------------------------------
		else 		if(format.equalsIgnoreCase("double")){
//			return toLength(inStr,8);	
			double f1 = Double.parseDouble(inStr);
			return toLength( Long.toHexString(Double.doubleToLongBits(f1))	,8);
		}
		//---------------------------------------------------	
		else 		if(format.equalsIgnoreCase("hex8"))
			return   toLength(inStr.toLowerCase(),1);	
		else 		if(format.equalsIgnoreCase("hex16"))
			return   toLength(inStr.toLowerCase(),2);	
		else 		if(format.equalsIgnoreCase("hex32"))
			return   toLength(inStr.toLowerCase(),4);	
		else 		if(format.equalsIgnoreCase("hex64"))
			return   toLength(inStr.toLowerCase(),8);	
		else
			return inStr;
	}
	
	static public String toLength(String instr,int bytes)
	{
		while(instr.length() < bytes*2)
			instr = "0"+ instr;
		return instr.substring(instr.length()-(bytes)*2);
	}
	
	static int [] GetFormatTableChange(String format)
	{
		int [] ws=new int[]{0,0,0,0,0,0,0,0};
		//---------------------
		if(format.equalsIgnoreCase("uchar"))
		{
			ws[0]=50;	ws[1]=50;	ws[2]=50;	ws[3]=50;	ws[4]=50;	ws[5]=50;	ws[6]=50;	ws[7]=50;
		}
		else 		if(format.equalsIgnoreCase("schar"))
		{
			ws[0]=50;	ws[1]=50;	ws[2]=50;	ws[3]=50;	ws[4]=50;	ws[5]=50;	ws[6]=50;	ws[7]=50;
		}
		else if(format.equalsIgnoreCase("ushort"))
		{
			ws[0]=100;	ws[1]=0;	ws[2]=100;	ws[3]=0;	ws[4]=100;	ws[5]=0;	ws[6]=100;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("sshort"))
		{
			ws[0]=100;	ws[1]=0;	ws[2]=100;	ws[3]=0;	ws[4]=100;	ws[5]=0;	ws[6]=100;	ws[7]=0;
		}
		else if(format.equalsIgnoreCase("uint"))
		{
			ws[0]=140;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=140;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("sint"))
		{
			ws[0]=140;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=140;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}		
		else if(format.equalsIgnoreCase("ulonglong"))
		{
			ws[0]=300;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=0;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("slonglong"))
		{
			ws[0]=300;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=0;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else if(format.equalsIgnoreCase("float"))
		{
			ws[0]=200;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=200;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("double"))
		{
			ws[0]=200;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=0;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("hex8"))
		{
			ws[0]=40;	ws[1]=40;	ws[2]=40;	ws[3]=40;	ws[4]=40;	ws[5]=40;	ws[6]=40;	ws[7]=40;
		}
		else 		if(format.equalsIgnoreCase("hex16"))
		{
			ws[0]=80;	ws[1]=0;	ws[2]=80;	ws[3]=0;	ws[4]=80;	ws[5]=0;	ws[6]=80;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("hex32"))
		{
			ws[0]=160;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=160;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 		if(format.equalsIgnoreCase("hex64"))
		{
			ws[0]=200;	ws[1]=0;	ws[2]=0;	ws[3]=0;	ws[4]=0;	ws[5]=0;	ws[6]=0;	ws[7]=0;
		}
		else 
		{
			ws[0]=40;	ws[1]=40;	ws[2]=40;	ws[3]=40;	ws[4]=40;	ws[5]=40;	ws[6]=40;	ws[7]=40;
		}
		//--------------------
		return ws;
	}
	
	static long MyLongGetFromHex(String inhex)
	{
		if(inhex.length()<16)
			return Long.parseLong(inhex, 16);
		else if(inhex.length()==16)
		{
			char c= inhex.charAt(0);
			// 0-7
			if( c<'8')
				return Long.parseLong(inhex, 16);
			else
			{
				// 8 9
				if(c<='9')
				{
					if(c == '8')
						inhex= inhex.substring(1);
					else
						inhex= "1"+inhex.substring(1);					
					return Long.parseLong(inhex, 16) + Long.MIN_VALUE;
				}
				else
				{
				 // A-F a-f
					c=  (char) (c&0xDF);
					c=(char) (c-15);// a-f to 2-7
					inhex= c+inhex.substring(1);
					return Long.parseLong(inhex, 16) + Long.MIN_VALUE;
				}
			}	
		}
		else
		{
			return	Long.parseLong(inhex, 16);// let  intel errot 
		}
		
	}
	

}
