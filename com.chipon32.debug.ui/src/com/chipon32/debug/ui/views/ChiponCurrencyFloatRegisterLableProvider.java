package com.chipon32.debug.ui.views;

import org.eclipse.jface.viewers.BaseLabelProvider;
import org.eclipse.jface.viewers.IColorProvider;
import org.eclipse.jface.viewers.ITableLabelProvider;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Image;

import com.chipon32.debug.core.model.ChiponCurrencyFloatRegisterData;
import com.chipon32.debug.core.model.ChiponGroupRomData;


/**
 * <AUTHOR> @since 2013-5-31 обнГ5:06:15
 */
public class ChiponCurrencyFloatRegisterLableProvider extends BaseLabelProvider implements ITableLabelProvider,IColorProvider{

	private Color color;
	
	@Override
	public Image getColumnImage(Object element, int columnIndex) {
		return null;
	}

	@Override
	public String getColumnText(Object element, int columnIndex) {
		if(element instanceof ChiponCurrencyFloatRegisterData){
			ChiponCurrencyFloatRegisterData groupData = (ChiponCurrencyFloatRegisterData) element;
			
			switch(columnIndex){
			case 0:
				color = null;
				return groupData.getName();
			case 1:
				color = null;
				return groupData.getValue();
			case 2:
				color = null;
				return groupData.getdValue();
			default:
				return "--";
			}
		}
		return null;
	}

	@Override
	public Color getForeground(Object element) {
		
		return color;
	}
	int i=1;
	String oldColumnNum=null;
	@Override
	public Color getBackground(Object element) {
		Color color=null;
		if(element instanceof ChiponGroupRomData){
			ChiponGroupRomData groupRomData=(ChiponGroupRomData)element;
			String columnNum = groupRomData.getRowNum();
			if(oldColumnNum!=null&&oldColumnNum!=columnNum){
				i++;
			}
			if(i%2==0){
				color=IChiponColor.GRAY_COLOR;
			}
			oldColumnNum=columnNum;
		}
		return color;
	}

}
