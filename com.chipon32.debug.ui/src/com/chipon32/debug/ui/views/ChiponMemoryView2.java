package com.chipon32.debug.ui.views;


import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.ui.AbstractDebugView;
import org.eclipse.debug.ui.DebugUITools;
import org.eclipse.debug.ui.contexts.DebugContextEvent;
import org.eclipse.debug.ui.contexts.IDebugContextListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.GC;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IWorkbenchActionConstants;
import org.eclipse.ui.progress.UIJob;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponGroupRomData;
import com.chipon32.debug.core.model.ChiponRomData;
import com.chipon32.debug.core.model.ChiponRomDatas;
import com.chipon32.debug.core.model.ChiponStackFrame;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.model.PropertyChangeViewPlugin;
import com.chipon32.debug.core.protocol.ChiponPrintMemoryCommand;
import com.chipon32.debug.core.protocol.ChiponPrintMemoryCommandResult;
import com.chipon32.debug.core.util.PackRomDataList;
import com.chipon32.util.ui.HexTableViewer;
import com.chipon32.util.ui.UIUtil;


/**
 * FileRegister内存视图
 * <AUTHOR> @since 2013-6-19 下午2:20:16
 */
public class ChiponMemoryView2 extends AbstractDebugView implements IDebugContextListener,IPropertyChangeListener {


	private Text txtEnd;
	private Text txtStart;
	
	private Button btnSearch;
	private Button btnSearchUP;
	private Button btnSearchDown;
	private Combo ShowDataFormat;
//	private Button btnSave;
//	private Button btnLoad;
	public ChiponMemoryView2() {
	
	}

	public static final String ID = "com.chipon32.debug.ui.view.MemoryView2"; //$NON-NLS-1$
	
	//private Composite						fViewParent;
	private Table 	table;
	private HexTableViewer tableViewer;
	private ChiponThread  fThread;

	
	
	
	@Override
	protected Viewer createViewer(Composite parent) {
	
		parent.setLayout(new GridLayout(1, false));	
		Composite container = new Composite(parent, SWT.NONE);

		container.setLayout(new GridLayout(10, false));
		GridData gd_composite = new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1);
		container.setLayoutData(gd_composite);

		Label formatlabel = new Label(container, SWT.NONE);
		formatlabel.setText(Messages.ChiponMemoryView1_2);
		
		ShowDataFormat = new Combo(container,  SWT.READ_ONLY);
//		ShowDataFormat.setLayoutData(new GridData(SWT.FILL,SWT.FILL,true,true,1,1));
		ShowDataFormat.setLayoutData(new GridData(86,5));
		ShowDataFormat.setItems(ChiponMemoryServiceClass.datetype);				 
		ShowDataFormat.select(0);

		final Label startAddress = new Label(container, SWT.NONE);
		startAddress.setText(Messages.ChiponMemoryView1_1);
		
//		GridData txtData = new GridData(SWT.LEFT, SWT.CENTER, true, false,1,1);
//		txtData.widthHint = 100;
		txtStart = new Text(container, SWT.BORDER);
		txtStart.setLayoutData(new GridData(100,24));
		txtStart.setText("1000 0000");	//初始默认地址从0开始 //$NON-NLS-1$
		Label space = new Label(container, SWT.NONE);
		space.setText("	"); //$NON-NLS-1$
		
		Label endAddress = new Label(container, SWT.NONE);
		endAddress.setText(Messages.ChiponMemoryView1_4);
		txtEnd = new Text(container, SWT.BORDER);
		txtEnd.setLayoutData(new GridData(60,24));
		txtEnd.setText("32");			//初始默认长度32                                                            //$NON-NLS-1$
		
		btnSearch = new Button(container, SWT.NONE);
		btnSearch.setLayoutData(new GridData(66,30));
		btnSearch.setText(Messages.ChiponMemoryView1_6);

		btnSearchUP = new Button(container, SWT.NONE);
		btnSearchUP.setLayoutData(new GridData(66,30));
		btnSearchUP.setText(Messages.ChiponMemoryView1_UP);
		
		btnSearchDown = new Button(container, SWT.NONE);
		btnSearchDown.setLayoutData(new GridData(66,30));
		btnSearchDown.setText(Messages.ChiponMemoryView1_DOWN);
		
//		btnSave = new Button(container, SWT.NONE);
//		btnSave.setLayoutData(new GridData(SWT.RIGHT,SWT.FILL,true,true,1,1));
//		btnSave.setText(Messages.ChiponMemoryView1_Save);
//		
//		btnLoad = new Button(container, SWT.NONE);
//		btnLoad.setText(Messages.ChiponMemoryView1_Load);
		//###################################################
		//内存显示部分
		Composite dataComposite = new Composite(parent, SWT.FULL_SELECTION);
		dataComposite.setLayout(new GridLayout(1, false));
		GridData gData = new GridData(SWT.FILL, SWT.FILL, true, true,2,7);
		dataComposite.setLayoutData(gData);
		
		tableViewer = new HexTableViewer(dataComposite, SWT.FULL_SELECTION | SWT.BORDER);

		table = tableViewer.getTable();
		table.setLinesVisible(false);
		table.setHeaderVisible(true);
		table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 2, 7));
		
		table.addListener(SWT.MeasureItem, new Listener() {
			@Override
			public void handleEvent(Event event) {
			      // height cannot be per row so simply set
				  Object obj = event.item.getData();
				  if(obj instanceof ChiponGroupRomData)
				  {
					  //根据每个ChiponRomData里面的值自适应每个单元格的高度
					  ChiponGroupRomData rd = (ChiponGroupRomData)obj;
					  String command = rd.getRomDatas().get(0).getValue();
					  GC gc = new GC(Display.getDefault());
					  int y = gc.stringExtent(command).y;
					  event.height = y + 4;
					  gc.dispose();
				  }
			   }
			});
		
			
		TableColumn tblclmnAddress = new TableColumn(table, SWT.CENTER);
		tblclmnAddress.setWidth(100);
		tblclmnAddress.setText("Address"); //$NON-NLS-1$
		//tblclmnAddress.setAlignment(SWT.CENTER);
		
		TableColumn tblclmn00 = new TableColumn(table, SWT.CENTER);
		tblclmn00.setWidth(40);
		tblclmn00.setText("00"); //$NON-NLS-1$
		
		TableColumn tblclmn01 = new TableColumn(table, SWT.CENTER);
		tblclmn01.setWidth(40);
		tblclmn01.setText("01"); //$NON-NLS-1$
		
		TableColumn tblclmn02 = new TableColumn(table, SWT.CENTER);
		tblclmn02.setWidth(40);
		tblclmn02.setText("02"); //$NON-NLS-1$
		
		TableColumn tblclmn03 = new TableColumn(table, SWT.CENTER);
		tblclmn03.setWidth(40);
		tblclmn03.setText("03"); //$NON-NLS-1$
		
		TableColumn tblclmn04 = new TableColumn(table, SWT.CENTER);
		tblclmn04.setWidth(40);
		tblclmn04.setText("04"); //$NON-NLS-1$
		
		TableColumn tblclmn05 = new TableColumn(table, SWT.CENTER);
		tblclmn05.setWidth(40);
		tblclmn05.setText("05"); //$NON-NLS-1$
		
		TableColumn tblclmn06 = new TableColumn(table, SWT.CENTER);
		tblclmn06.setWidth(40);
		tblclmn06.setText("06"); //$NON-NLS-1$
		
		TableColumn tblclmn07 = new TableColumn(table, SWT.CENTER);
		tblclmn07.setWidth(40);
		tblclmn07.setText("07"); //$NON-NLS-1$
		
		TableColumn tblclmn08 = new TableColumn(table, SWT.CENTER);
		tblclmn08.setWidth(40);
		tblclmn08.setText("08"); //$NON-NLS-1$
		
		TableColumn tblclmn09 = new TableColumn(table, SWT.CENTER);
		tblclmn09.setWidth(40);
		tblclmn09.setText("09"); //$NON-NLS-1$
		
		TableColumn tblclmn10 = new TableColumn(table, SWT.CENTER);
		tblclmn10.setWidth(40);
		tblclmn10.setText("0A"); //$NON-NLS-1$
		
		TableColumn tblclmn11 = new TableColumn(table, SWT.CENTER);
		tblclmn11.setWidth(40);
		tblclmn11.setText("0B"); //$NON-NLS-1$
		
		TableColumn tblclmn12 = new TableColumn(table, SWT.CENTER);
		tblclmn12.setWidth(40);
		tblclmn12.setText("0C"); //$NON-NLS-1$
		
		TableColumn tblclmn13 = new TableColumn(table, SWT.CENTER);
		tblclmn13.setWidth(40);
		tblclmn13.setText("0D"); //$NON-NLS-1$
		
		TableColumn tblclmn14 = new TableColumn(table, SWT.CENTER);
		tblclmn14.setWidth(40);
		tblclmn14.setText("0E"); //$NON-NLS-1$
		
		TableColumn tblclmn15 = new TableColumn(table, SWT.CENTER);
		tblclmn15.setWidth(40);
		tblclmn15.setText("0F"); //$NON-NLS-1$
		
		TableColumn tblclmnASCII = new TableColumn(table, SWT.CENTER);
		tblclmnASCII.setWidth(100);
		tblclmnASCII.setText("ASCII"); //$NON-NLS-1$
		
		tableViewer.setLabelProvider(new ChiponMemoryLableProvider());
		tableViewer.setContentProvider(new ChiponMemoryContentProvider());	
		
		UIUtil.setDefaultProperties(tableViewer);
		CellEditor[] cellEditors = new CellEditor[18];
		cellEditors[0] = null;
		cellEditors[1] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[2] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[3] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[4] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[5] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[6] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[7] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[8] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[9] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[10] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[11] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[12] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[13] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[14] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[15] = new TextCellEditor(table, SWT.BORDER);
		cellEditors[16] = new TextCellEditor(table, SWT.BORDER);
//		cellEditors[17] = null;
		
		tableViewer.setCellEditors(cellEditors);
		tableViewer.setCellModifier(new ChiponMemoryCellModifier(tableViewer));//new GroupMemoryCellModifier(tableViewer));
		
		btnSearch.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<ChiponRomData> romDataList = getRomDataList(0);
				ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
				tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
				tableViewer.refresh();
			}
		});
		btnSearchUP.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<ChiponRomData> romDataList = getRomDataList(1);
				ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
				tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
				tableViewer.refresh();
			}
		});
		btnSearchDown.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				List<ChiponRomData> romDataList = getRomDataList(2);
				ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
				tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
				tableViewer.refresh();
			}
		});
		ShowDataFormat.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {	
				String format =ShowDataFormat.getText();				

				if(tableViewer.getContentProvider() instanceof ChiponMemoryContentProvider)
				{
					ChiponMemoryContentProvider cp =(ChiponMemoryContentProvider) tableViewer.getContentProvider();
					cp.showformat=format;
				}
				if(tableViewer.getLabelProvider() instanceof ChiponMemoryLableProvider)
				{
					ChiponMemoryLableProvider lp =(ChiponMemoryLableProvider) tableViewer.getLabelProvider();
					lp.showformat=format;
				}
				if(tableViewer.getCellModifier() instanceof ChiponMemoryCellModifier)
				{
					ChiponMemoryCellModifier mp =(ChiponMemoryCellModifier) tableViewer.getCellModifier();
					mp.showformat=format;
				}
				//************
				for(int i=0;i<2;i++)
				{
					int  ws[]=ChiponMemoryServiceClass.GetFormatTableChange(format);
					table.getColumn(1+8*i).setWidth(ws[0]);
					table.getColumn(2+8*i).setWidth(ws[1]);
					table.getColumn(3+8*i).setWidth(ws[2]);
					table.getColumn(4+8*i).setWidth(ws[3]);
					table.getColumn(5+8*i).setWidth(ws[4]);
					table.getColumn(6+8*i).setWidth(ws[5]);
					table.getColumn(7+8*i).setWidth(ws[6]);
					table.getColumn(8+8*i).setWidth(ws[7]);
				}
				//************
				List<ChiponRomData> romDataList = getRomDataList(0);
				ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
				tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
				tableViewer.refresh();
//				btnSearch.notify();
			}
		});	
		DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).addDebugContextListener(this);
//		getSite().setSelectionProvider(tableViewer);
		PropertyChangeViewPlugin.getInstant().addPropertyChangeListener(this);
		
		setInput(tableViewer);
		return tableViewer;
	}
	
	private void setInput(TableViewer tableViewer){
		List<ChiponRomData> romDataList = getRomDataList(0);
		ChiponRomDatas romDatas =  new PackRomDataList().packList(romDataList);
		tableViewer.setInput(/*compare(tableViewer.getInput(),*/ romDatas);
		tableViewer.refresh();
	}
	
	/**
	 * 基于界面 输入原始的地址 值原始  列表
	 * @return
	 */
	@SuppressWarnings("static-access")
	public List<ChiponRomData> getRomDataList(int type){
		
		List<ChiponRomData> romDataList = new ArrayList<>();
		
		String startAddr = txtStart.getText().trim().replace(" ", "");	 //$NON-NLS-1$ //$NON-NLS-2$
		String endAddr = txtEnd.getText().trim().replace(" ", ""); //$NON-NLS-1$ //$NON-NLS-2$
		if(startAddr.length() == 0 || endAddr.length()==0){
			return null;
		}else{
			// ################### 识别输入有效	
			long length = 0;
			try {
				// 考虑速度限定最多xK的容量
				length = (Long.parseLong(endAddr, 10)+3)/4;
				if(length>2048)
				{
					length=32;
					txtEnd.setText("32"); //$NON-NLS-1$
				}
					
			} catch (NumberFormatException e) {
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponMemoryView1_30, Messages.ChiponMemoryView1_31);
			}
			
			//判断正确的数据类型
			if(!isHex(startAddr)){
				MessageDialog.openWarning(Display.getCurrent().getActiveShell(), Messages.ChiponMemoryView1_32, Messages.ChiponMemoryView1_33);
				return romDataList;
			}
			//############## page up  page down deal
			if(type==1){
				startAddr = Long.toHexString((Long.parseLong(startAddr,16)-Long.parseLong(endAddr, 10))).toUpperCase();
				txtStart.setText(startAddr);
			}
			else if(type==2)
			{
				startAddr = Long.toHexString((Long.parseLong(startAddr,16)+Long.parseLong(endAddr, 10))).toUpperCase();
				txtStart.setText(startAddr);
			}
			//############
			//判断地址范围，如果不在这个区间则报错 flash区间和Ram区间
			IProject project=null;
			if(fThread!=null)
				project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
			if (project != null) {
			// 有效范围限定，这里暂不做限定
//				ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
//				ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();
//				String chip = fTargetProps.getchipName();// 获取当前项目的芯片型号
//				
//				IConfigurationProvider provider = ConfigurationFactory.getProvider(chip);
//				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//				Long flashStartAddr = Long.parseLong(provider.getFlashStartAddr(), 16);
//				Long flashSize = Long.parseLong(provider.getFlashsize(), 16);
//				
//				Long RamStartAddr = Long.parseLong(provider.getRamStartAddr(), 16);
//				Long ramSize = Long.parseLong(provider.getRamsize(), 16);
//				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//				Long startAddrNum = Long.parseLong(startAddr, 16);
//				
//				if(!(startAddrNum >= flashStartAddr && (startAddrNum+length)<(flashStartAddr+flashSize)) &&
//						!(startAddrNum >= RamStartAddr && (startAddrNum+length)<(RamStartAddr+ramSize)))
//				{
//					MessageDialog.openWarning(Display.getCurrent().getActiveShell(), "提示", "请输入正确的内存区间");
//					return romDataList;
//				}
			}
			
			// ########## 存在长度请求的处理
			if(length !=0){
				if(fThread == null){
					ISelection selection=DebugUITools.getDebugContextManager().
							getContextService(getViewSite().getWorkbenchWindow()).getActiveContext();

					if (selection instanceof IStructuredSelection) {
				         Object element = ((IStructuredSelection)selection).getFirstElement();
				         if(element instanceof ChiponDebugTarget){
				         	fThread = ((ChiponDebugTarget)element).getCurrentThread();
				         }
				         if (element instanceof ChiponThread) {
				         	fThread = (ChiponThread)element;
				         } else if (element instanceof ChiponStackFrame) {
				         	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
				         }else{
				        	 return romDataList;
				         }
					 }
				}
				if(fThread !=null){
					// 基于进程的获取内容
					if(fThread.canStepInto())
					{
					ChiponPrintMemoryCommandResult memoryCommandResult =(ChiponPrintMemoryCommandResult)fThread.sendCommand(new ChiponPrintMemoryCommand("0x"+startAddr, (int) length)); //$NON-NLS-1$
					romDataList.addAll(memoryCommandResult.romDataList);	
					ChiponThread.romDataList = romDataList;
					}
				}

			}
		}
		return romDataList;
	}

	@Override
	protected void createActions() {
		
	}

	@Override
	protected String getHelpContextId() {
		return null;
	}

	@Override
	protected void fillContextMenu(IMenuManager menu) {
		menu.add(new Separator(IWorkbenchActionConstants.MB_ADDITIONS));
	}

	@Override
	protected void configureToolBar(IToolBarManager tbm) {
		//tbm.removeAll();
	}

	/**
	 * 调试时若该视图是开启的状态，每次暂停时调用该方法
	 */
	@Override
	public void debugContextChanged(final DebugContextEvent event) {
		 new UIJob(getSite().getShell().getDisplay(), Messages.ChiponMemoryView2_35) {
		        {
		            setSystem(true);
		        }
		        
		        @Override
				public IStatus runInUIThread(IProgressMonitor monitor) {
		        	if (getViewer() != null) { // runs asynchronously, view may be disposed
		        		update(event.getContext());
		        	}
		            return Status.OK_STATUS;
		        }
		    }.schedule();
		    
		  /*  if (getViewer() != null) { // runs asynchronously, view may be disposed
        		update(event.getContext());
        	}*/
	}
	
	/**
     * Updates the view for the selected thread (if suspended)
     */
    @SuppressWarnings("static-access")
	private void update(ISelection context) {
    	fThread = null;
        
        if (context instanceof IStructuredSelection) {
            Object element = ((IStructuredSelection)context).getFirstElement();
            if(element instanceof ChiponDebugTarget){
            	fThread = ((ChiponDebugTarget)element).getCurrentThread();
            	return;
            }
            if (element instanceof ChiponThread) {
            	fThread = (ChiponThread)element;
            	return;
            } else if (element instanceof ChiponStackFrame) {
            	fThread = (ChiponThread) ((ChiponStackFrame) element).getThread();
            }else return;
        }
		ChiponRomDatas romDatas = null;
		if (fThread != null && fThread.isSuspended() && fThread.isCanSetInputMemory2()) {
			romDatas = new PackRomDataList().packList(getRomDataList(0));
			fThread.setCanSetInputMemory2(false);
		}else{
			return;
		}
		
		getViewer().setInput(/*this.compare(getViewer().getInput(), */romDatas);
		getViewer().refresh();
    }

	@Override
	public void dispose() {
		 DebugUITools.getDebugContextManager().getContextService(getSite().getWorkbenchWindow()).removeDebugContextListener(this);
		 super.dispose();
	}
	
	/**
	 * 属性变化后的操作
	 * **/
	@Override
	public void propertyChange(PropertyChangeEvent event) {

		@SuppressWarnings("unchecked")
		List<ChiponRomData> romDataList=(List<ChiponRomData>) event.getNewValue();
		
		ChiponRomDatas romDatas = new PackRomDataList().packList(romDataList);
		if (getViewer() != null) { // runs asynchronously, view may be disposed
			getViewer().setInput(romDatas);
			getViewer().refresh();		
			
		}
//			ChiponThread.romDataList=romDataList;
	}
	
	private boolean isHex(String input) {
		String regex = "[0-9A-Fa-f]{1,8}"; //$NON-NLS-1$
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);		
		return matcher.matches();
	}
	
	public Combo getShowDataFormat() {
		return ShowDataFormat;
	}


	public HexTableViewer getTableViewer() {
		return tableViewer;
	}


	public ChiponThread getfThread() {
		return fThread;
	}


	public Text getTxtStart() {
		return txtStart;
	}


	public Text getTxtEnd() {
		return txtEnd;
	}
}
