package com.chipon32.chiponide.log.view;

import java.awt.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StyleRange;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.part.ViewPart;

import com.chipon32.chiponide.log.Activator;
import com.chipon32.chiponide.log.Messages;
import com.chipon32.chiponide.log.dialog.ExportHistoryDialog;
import com.chipon32.chiponide.log.dialog.FilterHistoryDialog;
import com.chipon32.chiponide.log.model.IHistroyView;

public class HistoryView extends ViewPart implements IHistroyView {
	private StyledText consoleText;
	private List<String> inputList = new ArrayList<>();
	private List<String> outputList = new ArrayList<>();
	private StyleRange historyStyleRange = new StyleRange();
	private StyleRange responseStyleRange = new StyleRange();
	public static HistoryView INSTANCE;

	public static synchronized HistoryView getHistoryView() {
		if (INSTANCE != null && !INSTANCE.isDisposed()) {
			return INSTANCE;
		}
		return null;
	}

	public HistoryView() {
		historyStyleRange.foreground = Display.getDefault().getSystemColor(SWT.COLOR_DARK_BLUE);
		responseStyleRange.foreground = Display.getDefault().getSystemColor(SWT.COLOR_DARK_GREEN);
		INSTANCE = this;
	}

	@Override
	public void createPartControl(Composite parent) {
        Composite container = new Composite(parent, SWT.NONE);
        container.setLayout(new GridLayout(1, false));

        consoleText = new StyledText(container, SWT.MULTI | SWT.V_SCROLL | SWT.BORDER | SWT.READ_ONLY);
        consoleText.setLayoutData(new GridData(GridData.FILL_BOTH));
        consoleText.setBackground(parent.getDisplay().getSystemColor(SWT.COLOR_WHITE));
        consoleText.setWordWrap(true);
        
        createToolBarActions();
	}
	
	
    private void createToolBarActions() {
        IToolBarManager toolBarManager = getViewSite().getActionBars().getToolBarManager();

        Action clearAction = new Action(Messages.CLEAR_HISTORY) {
            @Override
            public void run() {
                clearConsole();
            }
        };
        clearAction.setImageDescriptor(Activator.getImageDescriptor("icons/clear.png"));

        toolBarManager.add(clearAction);
        
        Action exportAction = new Action(Messages.EXPORT_HISTORY) {
            @Override
            public void run() {
                ExportHistoryDialog dialog = new ExportHistoryDialog(Display.getDefault().getActiveShell());
                if(dialog.open()) {
                	int index = (int) dialog.getResult();
                	switch(index) {
                	case 0:
                		exportHistory();
                		break;
                	case 1:
                		exportInput();
                		break;
                	case 2:
                		exportOutput();
                		break;
                	}
                }
            }
        };
        exportAction.setImageDescriptor(Activator.getImageDescriptor("icons/input.png"));
        toolBarManager.add(exportAction);
        
        Action filterAction = new Action(Messages.FILTER) {
            @Override
            public void run() {
            	FilterHistoryDialog dialog = new FilterHistoryDialog(Display.getDefault().getActiveShell());
            	dialog.open();
            }

        };
        filterAction.setImageDescriptor(Activator.getImageDescriptor("icons/<EMAIL>"));
        
        toolBarManager.add(filterAction);

    }

	private void clearConsole() {
		inputList.clear();
		outputList.clear();
        if (consoleText != null) {
            consoleText.setText("");
        }		
	}

	@Override
	public void dispose() {
		super.dispose();
		if (consoleText != null && !consoleText.isDisposed()) {
			consoleText.dispose();
		}
	}

	private void exportHistory() {
		
        String historyFileName = "history.txt";
		File historyFile = new File(historyFileName);
		if(historyFile.exists()) {
			historyFile.delete();
		}
		
		try (BufferedWriter writer = new BufferedWriter(new FileWriter(historyFileName))) {
			writer.write(consoleText.getText());
		} catch (IOException e) {
			e.printStackTrace();
			return;
		}
        	
		try {
			Desktop.getDesktop().edit(historyFile);
		} catch (IOException e) {
			e.printStackTrace();
		}

	}
	
	private void exportInput() {
		
        String inputFileName = "input.txt";
		File inputFile = new File(inputFileName);
		if(inputFile.exists()) {
			inputFile.delete();
		}
		
		try (BufferedWriter writer = new BufferedWriter(new FileWriter(inputFileName))) {
			for (String input : inputList) {
				writer.write(input);
				writer.newLine();
			}
		} catch (IOException e) {
			e.printStackTrace();
			return;
		}
        	
		try {
			Desktop.getDesktop().edit(inputFile);
		} catch (IOException e) {
			e.printStackTrace();
		}

	}
	
	private void exportOutput() {
        String outputFileName = "output.txt";
		File outputFile = new File(outputFileName);
		if(outputFile.exists()) {
			outputFile.delete();
		}
		
		try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFileName))) {
			for (String output : outputList) {
				writer.write(output);
				writer.newLine();
			}
		} catch (IOException e) {
			e.printStackTrace();
			return;
		}
        	
		try {
			Desktop.getDesktop().edit(outputFile);
		} catch (IOException e) {
			e.printStackTrace();
		}
		
	}

	@Override
	public void setFocus() {

	}
	
	@Override
    public void printHistory(String text) {
		inputList.add(text);
		SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss:SSS");
		String timeValue = dateFormat.format(new Date());
		if (consoleText != null && !consoleText.isDisposed()) {
			if (text != null && text.length() > 0) {
				int start = consoleText.getCharCount();
				consoleText.append(timeValue + " " + text + "\n");
				int end = consoleText.getCharCount();
				historyStyleRange.start = start;
				historyStyleRange.length = end - start;
				consoleText.setStyleRange(historyStyleRange);
			}
		}
	}

	@Override
    public void printResponse(String text) {
		outputList.add(text);
		SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss:SSS");
		String timeValue = dateFormat.format(new Date());
		if (consoleText != null && !consoleText.isDisposed()) {
			if (text != null && text.length() > 0) {
				int start = consoleText.getCharCount();
				consoleText.append(timeValue + " " + text + "\n");
				int end = consoleText.getCharCount();
				responseStyleRange.start = start;
				responseStyleRange.length = end - start;
				consoleText.setStyleRange(responseStyleRange);
			}
		}
	}

	@Override
	public boolean isDisposed() {
		return consoleText.isDisposed();
	}

}
