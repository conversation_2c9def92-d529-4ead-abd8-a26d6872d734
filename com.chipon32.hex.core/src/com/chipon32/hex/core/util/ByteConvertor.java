package com.chipon32.hex.core.util;

public class ByteConvertor 
{
	/**
	 * 
	 * @param hex 十六进制字符串，代表指令码，例如"C0".
	 * 		在Java中byte类型为有符号，因此大于127的十六进制值，调用Byte.parseByte()方法会产生转换错误。
	 * @return
	 */
	public static byte hexToByte(String hex)
	{
		int intValue = 0;
		try {
			intValue = Integer.parseInt(hex, 16);
		} catch (NumberFormatException e) {
			intValue = 0;
			return (byte) intValue;
		}
		byte byteValue=0;   
		int temp = intValue % 256;   
		if ( intValue < 0) {   
		  byteValue =  (byte)(temp < -128 ? 256 + temp : temp);   
		}   
		else {   
		  byteValue =  (byte)(temp > 127 ? temp - 256 : temp);   
		}
		
		return byteValue;
	}
	
	public static String toHex(byte b) 
	{
		 return ("" + "0123456789ABCDEF".charAt(0xf & b >> 4) + "0123456789ABCDEF".charAt(b & 0xf));
	}
	
	public static String toHexs(byte[] bytes)
	{
		StringBuffer buffer = new StringBuffer();
		for(byte b : bytes)
		{
			buffer.append(	toHex(b)	);
		}
		
		return buffer.toString();
	}
	
	public static String toHexsArray(byte[] bytes)
	{
		StringBuffer buffer = new StringBuffer();
		for(byte b : bytes)
		{
			buffer.append(	toTwo(toHex(b))+" ");
		}
		
		return buffer.toString().trim();
	}
	
	public static String toS19(byte[] bytes,int length)
	{
		StringBuffer buffer = new StringBuffer();
		int len=0;
		for(byte b : bytes)
		{
			if(len++>1)
			{
				if(len >length)
					break;
				buffer.append(	toHex(b)	);
			}
			else
			{
				buffer.append(	(char)(b)	);
			}
		}
		
		return buffer.toString();
	}
	/**
	 * 和校验 从第0位开始
	 * @param bytes
	 * @return 校验值 为改数组的最后一位
	 */
	public static byte format(byte[] bytes){
		int j;
		int k = 0;
		for(int i=0;i<(bytes.length-3);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	
	/**
	 * 和校验   bytes的最后一位为反码，从第0位开始
	 * @param bytes
	 * @return 校验值 为改数组的最后一位
	 */
	public static byte formatFrom1(byte[] bytes){
		int j;
		int k = 0;
		for(int i=0;i<(bytes.length-2);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	
	/**
	 * 和校验    bytes的最后一位为反码，从第2位开始
	 * @param bytes
	 * @return 校验值
	 */
	public static byte byteSum(byte[] bytes){
		int j;
		int k = 0;
		for(int i=1;i<(bytes.length-2);i++){
			j=bytes[i]&0xFF;
			k += j;
		}
		return (byte) (k%256);
	}
	

	
	public static String format(byte[] bytes, int offset, int numBytes) {

		StringBuilder buffer = new StringBuilder();

		for (int i = offset; i < numBytes; i++) {
			buffer.append(String.format("%02X ", bytes[i] & 0xFF)); //$NON-NLS-1$        
		}

		return buffer.toString(); 
	}
	//======================================================================================================
	public static String toTwo(String input)
	{
		String tmp = input.toUpperCase();
		while(tmp.length()<2)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-2);
	}
	public static String toFour(String input)
	{
		String tmp = input.toUpperCase();
		while(tmp.length()<4)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-4);
	}
	public static String toSix(String input)
	{
		String tmp = input.toUpperCase();
		while(tmp.length()<6)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-6);
	}
	public static String toEight(String input)
	{
		String tmp = input.toUpperCase();
		while(tmp.length()<8)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-8);
	}
	public static String toLen16(String input)
	{
		String tmp = input.toUpperCase();
		while(tmp.length()<16)
			tmp="0"+tmp;
		
		return tmp.substring(tmp.length()-16);
	}
//======================================================================================================

	/**
	 * 将16进制或10进制数转换成8位的二进制字符串格式
	 * @param hex
	 * @param radix
	 * @return 8位的二进制字符串
	 */
	public static String To8binary(String hex, int radix){
		String result = Integer.toBinaryString(Integer.parseInt(hex, radix));
		if(result.length()<8){
			StringBuffer zeroNum = new StringBuffer();
			for(int i=0;i<(8-result.length());i++){
				zeroNum.append("0");
			}
			result = zeroNum.toString().trim() + result;
			
		}
		return result;
	}
	/**
	 * 二进制转换成16进制
	 * @param string二进制字符串
	 * @return
	 */
	public static String binary2Hex(String string){
		return Integer.toString(Integer.parseInt(string,2), 16);
	}
	//======================================================================================================
	
	/**字节数组转16进制
	 * @param b
	 * @return
	 */
	public static String printHexString(byte[] b) {

		StringBuffer sbf = new StringBuffer();
		for (int i = 0; i < b.length; i++) { 
			String hex = Integer.toHexString(b[i] & 0xFF); 
			if (hex.length() == 1) { 
				hex = '0' + hex; 
			} 
			sbf.append(hex.toUpperCase()+" ");
		}
		return sbf.toString().trim();
	}

}
