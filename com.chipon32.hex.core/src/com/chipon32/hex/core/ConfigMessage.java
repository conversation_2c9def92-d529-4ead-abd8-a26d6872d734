package com.chipon32.hex.core;



// 配置的基本项信息
public class ConfigMessage {
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	private String  	name;				// 所需名字
	private long 		configaddr; 		// 存在起始地址
	private byte 		bytes;				// 占用字节数  1~4
	private String  	remark;				// 配置字注释
	
	
	private String [] 	element;			// 可选内容列表	
	private long[] 		configvaule; 		// 选择项下内容
	
	private int 		defaultselid;		// 默认配置项
	private String      selelementtext;		// 选择的内容
	
	private byte[]		Mask_High;			// 无关位配置1
//===============================================================================
	public long getConfigaddr() {
		return configaddr;
	}
	public void setConfigaddr(long configaddr) {
		this.configaddr = configaddr;
	}
	public byte getBytes() {
		return bytes;
	}
	public void setBytes(byte bytes) {
		this.bytes = bytes;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String [] getElement() {
		return element;
	}
	public void setElement(String [] element) {
		this.element = element;
	}
	public long[] getConfigvaule() {
		return configvaule;
	}
	public void setConfigvaule(long[] configvaule) {
		this.configvaule = configvaule;
	}
	public byte[] getMask_High() {
		return Mask_High;
	}
	public void setMask_High(byte[] mask_High) {
		Mask_High = mask_High;
	}
	public int getDefaultselid() {
		return defaultselid;
	}
	public void setDefaultselid(int defaultselid) {
		this.defaultselid = defaultselid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getSelelementtext() {
		return selelementtext;
	}
	public void setSelelementtext(String selelementtext) {
		this.selelementtext = selelementtext;
	}

	
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
}
