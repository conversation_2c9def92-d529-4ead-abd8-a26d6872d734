package com.chipon32.hex.core;


import java.util.ArrayList;

import java.util.List;
// CMAC Arithmetic
public class CheckAESCMAKCalculateLibrary {
	
	public static int  watch_enc_count=0;
	
//############################################################	

//############################################################	
	public static void setBufVector(String bufVectorin,int startaddress,int stopaddress) {
		input_Key=bufVectorin;
		watch_enc_count=0;
		while(bufVectorin.length()<32)
		{
			bufVectorin ="0"+bufVectorin;
		}
		//=================================================
		for(int i=0;i<16;i++)
		{
			String buf=bufVectorin.substring(2*i,2*i+2);
			dealKey[i]= (short) (Integer.parseInt(buf,16)&0xFF);
		}
//		//=================================================  use function	
//		short []tempout = new short[16];		
//		aes_128_encrypt(Zear_16,tempout,dealKey);  // 
//		//=================================================
//		block_leftshift(dealGetK1,tempout);
//		if((dealKey[0]&0x80) !=0)
//		{
//			dealGetK1[15] ^=0X87;
//		}
//		//=================================================
//		block_leftshift(dealGetK2,dealGetK1);
//		if((dealGetK1[0]&0x80) !=0)
//		{
//			dealGetK2[15] ^=0X87;
//		}
//		//=================================================  
		GenerateSubkey(dealKey,dealGetK1,dealGetK2);
//		//=================================================		
		bufVector[0]=0;bufVector[1]=0;bufVector[2]=0;bufVector[3]=0;
		bufVector[4]=0;bufVector[5]=0;bufVector[6]=0;bufVector[7]=0;
		bufVector[8]=0;bufVector[9]=0;bufVector[10]=0;bufVector[11]=0;
		bufVector[12]=0;bufVector[13]=0;bufVector[14]=0;bufVector[15]=0;
		//=================================================
		int length=stopaddress-startaddress+1;
		String legthstr= Long.toHexString(length).toUpperCase();
		while(legthstr.length()<32) legthstr="0"+legthstr;
		//=================================================
		short oneInput[]={
			0,0,0,0,	0,0,0,0,
			0,0,0,0,	0,0,0,0
		};;
		for(int i=0;i<16;i++)
		{
			String buf=legthstr.substring(2*i,2*i+2);
			oneInput[i]= (short) (Integer.parseInt(buf,16)&0xFF);
		}
		aes_128_encrypt(oneInput,bufVector,dealKey);  // 		
		
//		System.out.print("CMAC:\t"+"Start:"+Integer.toHexString(startaddress)+"\tStop:"+Integer.toHexString(stopaddress)+"\tLength:"+Integer.toHexString(length)+"\r\n" );
//		System.out.print("Key :\t"+GetOutValue(dealKey));
//		System.out.print("Key1:\t"+GetOutValue(dealGetK1));
//		System.out.print("Key2:\t"+GetOutValue(dealGetK2));
//		System.out.print("SEED:\t"+GetOutValue(bufVector));
		
	}
//############################################################	


	public static List<String> process_cmak_cal (int startaddress,int stopaddress,SectorElementVaule ele, List<AddrVauleData> content)
	{
		List<String> nowresult=new ArrayList<String>();
		long thisContenStarAddress = content.get(0).getAddress();
		long thisContenStopAddress = content.get(content.size()-1).getAddress();
		
		int thisContenLength=0;
		
		//System.out.print("do:\t"+Integer.toHexString(startaddress)+" stop " +Integer.toHexString(stopaddress) +" now "+ Integer.toHexString(ele.getAddress()) +"\r\n");
		
		if(thisContenStarAddress>=startaddress && thisContenStopAddress<= stopaddress)
		{// all  1K
			thisContenLength = (int) (thisContenStopAddress-thisContenStarAddress+1);
			for(int i=0; i<thisContenLength ;)
			{
				if(i<=(thisContenLength-16))
				{
					int j=16;
					while((--j)>=0)
					{
						nowresult.add(content.get(i+j).getVaule());
					}
					i+=16;continue;	// 
				}
				else
				{
					int j=thisContenLength-i;
					while((--j)>=0)
					{
						nowresult.add(content.get(i+j).getVaule());
					}
					i+=thisContenLength;
					break;	
				}
			}
		}
		else if ( thisContenStarAddress <startaddress  && thisContenStopAddress >=startaddress  && thisContenStopAddress<= stopaddress ) 
		{// prefix not add to
			thisContenLength = (int) (thisContenStopAddress-startaddress+1);
			for(int i=0; i<thisContenLength ;)
			{				
				//nowresult.add(content.get(startaddress-thisContenStarAddress +i).getVaule());
				if(i<=(thisContenLength-16))
				{
					int j=16;
					while((--j)>=0)
					{
						nowresult.add(content.get((int) (startaddress-thisContenStarAddress +i+j)).getVaule());
					}
					i+=16;continue;	// 
				}
				else
				{
					int j=thisContenLength-i;
					while((--j)>=0)
					{
						nowresult.add(content.get((int) (startaddress-thisContenStarAddress +i+j)).getVaule());
					}
					i+=thisContenLength;
					break;	
				}				
			}
		}
		else if(thisContenStarAddress>=startaddress && thisContenStarAddress<stopaddress 	  &&  thisContenStopAddress> stopaddress )		
		{// postfix not add to
			thisContenLength = (int) (stopaddress-thisContenStarAddress+1);
			
			for(int i=0; i<thisContenLength ;)
			{
				//nowresult.add(content.get(i).getVaule());
				if(i<=(thisContenLength-16))
				{
					int j=16;
					while((--j)>=0)
					{
						nowresult.add(content.get(i+j).getVaule());
					}
					i+=16;continue;	// 
				}
				else
				{
					int j=thisContenLength-i;
					while((--j)>=0)
					{
						nowresult.add(content.get(i+j).getVaule());
					}
					i+=thisContenLength;
					break;	
				}	
			}
		}
		else if( thisContenStarAddress < startaddress   && thisContenStopAddress >= stopaddress)
		{ // middle add to
			thisContenLength = stopaddress-startaddress+1;
			
			for(int i=0; i<thisContenLength ;)
			{
				//nowresult.add(content.get(startaddress-thisContenStarAddress + i).getVaule());
				if(i<=(thisContenLength-16))
				{
					int j=16;
					while((--j)>=0)
					{
						nowresult.add(content.get((int) (startaddress-thisContenStarAddress +i+j)).getVaule());
					}
					i+=16;continue;	// 
				}
				else
				{
					int j=thisContenLength-i;
					while((--j)>=0)
					{
						nowresult.add(content.get((int) (startaddress-thisContenStarAddress +i+j)).getVaule());
					}
					i+=thisContenLength;
					break;	
				}	
			}
		}
		else
		{
			// not exist
		}
		//
		boolean isWithAddtion=false;
		int forcount=thisContenLength/16;
		
		if(thisContenLength%16 !=0)
		{
			nowresult.add("80");
			while(nowresult.size()%16 !=0)
				nowresult.add("00");
			isWithAddtion=true;
		}
		else
			forcount-=1;
		//#####################################################################################	
		short[] out_point =new short[16];		
		
		for(int t=0;t<forcount ;t++)
		{				
			short[] in1_point =new short[16];	
			for(int m=0;m<16;m++)
			{
				in1_point[m]= (short) Integer.parseInt(nowresult.get(t*16+m),16);
			}// in  to  out			
//				System.out.print("IN:\t"+GetOutValue(in1_point));
			block_xor( in1_point, bufVector, in1_point);
//				System.out.print("XOR:\t"+GetOutValue(in1_point));			
			aes_128_encrypt(in1_point, out_point,dealKey );				
//				System.out.print(Integer.toString(watch_enc_count++)+":\t"+GetOutValue(out_point));			
			block_xor( bufVector, Zear_16, out_point);
		}
		//######################################################################
		short[] in1_point =new short[16];	
		for(int m=0;m<16;m++)
		{
			in1_point[m]= (short) Integer.parseInt(nowresult.get(forcount*16+m),16);
		}// in  to  out			
//			System.out.print("IN:\t"+GetOutValue(in1_point));
		block_xor( in1_point, bufVector, in1_point);
//			System.out.print("XOR:\t"+GetOutValue(in1_point));			
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			// only end with xor k1 or k2
			if(thisContenStopAddress >= stopaddress)
			{
				if(isWithAddtion==true)			
				{
					block_xor( in1_point, dealGetK2, in1_point);
//					System.out.print("X_K2:\t"+GetOutValue(in1_point));	
				}
				else
				{
					block_xor( in1_point, dealGetK1, in1_point);
//					System.out.print("X_K1:\t"+GetOutValue(in1_point));	
				}
				
			}
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		aes_128_encrypt(in1_point, out_point,dealKey );		
//			System.out.print(Integer.toString(watch_enc_count++)+":\t"+GetOutValue(out_point));			
		block_xor( bufVector, Zear_16, out_point);
		//#####################################################################################
		List<String> result=new ArrayList<String>();
		for(int m=0;m<16;m++)
		{
			String bufv=Integer.toHexString(out_point[m]);
			if(bufv.length()<2) bufv="0"+bufv;
			result.add(bufv);
		}// in  to  out		
		return result;
	}
	
	public static List<String> process_cmak_cal_inner(List<String> nowresult, boolean isLastData, int thisContenLength) {
		boolean isWithAddtion=false;
		int forcount=thisContenLength/16;
		
		if(thisContenLength%16 !=0)
		{
			nowresult.add("80");
			while(nowresult.size()%16 !=0)
				nowresult.add("00");
			isWithAddtion=true;
		}
		else
			forcount-=1;
		//#####################################################################################	
		short[] out_point =new short[16];		
		
		for(int t=0;t<forcount ;t++)
		{				
			short[] in1_point =new short[16];	
			for(int m=0;m<16;m++)
			{
				in1_point[m]= (short) Integer.parseInt(nowresult.get(t*16+m),16);
			}// in  to  out			
//				System.out.print("IN:\t"+GetOutValue(in1_point));
			block_xor( in1_point, bufVector, in1_point);
//				System.out.print("XOR:\t"+GetOutValue(in1_point));			
			aes_128_encrypt(in1_point, out_point,dealKey );				
//				System.out.print(Integer.toString(watch_enc_count++)+":\t"+GetOutValue(out_point));			
			block_xor( bufVector, Zear_16, out_point);
		}
		//######################################################################
		short[] in1_point =new short[16];	
		for(int m=0;m<16;m++)
		{
			in1_point[m]= (short) Integer.parseInt(nowresult.get(forcount*16+m),16);
		}// in  to  out			
//			System.out.print("IN:\t"+GetOutValue(in1_point));
		block_xor( in1_point, bufVector, in1_point);
//			System.out.print("XOR:\t"+GetOutValue(in1_point));			
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			// only end with xor k1 or k2
			if(isLastData)
			{
				if(isWithAddtion==true)			
				{
					block_xor( in1_point, dealGetK2, in1_point);
//					System.out.print("X_K2:\t"+GetOutValue(in1_point));	
				}
				else
				{
					block_xor( in1_point, dealGetK1, in1_point);
//					System.out.print("X_K1:\t"+GetOutValue(in1_point));	
				}
				
			}
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		aes_128_encrypt(in1_point, out_point,dealKey );		
//			System.out.print(Integer.toString(watch_enc_count++)+":\t"+GetOutValue(out_point));			
		block_xor( bufVector, Zear_16, out_point);
		//#####################################################################################
		List<String> result=new ArrayList<String>();
		for(int m=0;m<16;m++)
		{
			String bufv=Integer.toHexString(out_point[m]);
			if(bufv.length()<2) bufv="0"+bufv;
			result.add(bufv);
		}// in  to  out		
		return result;
	}
	
	static String  GetOutValue(short[]out_point )
	{
		String result="";
		for(int m=0;m<16;m++)
		{
			String bufv=Integer.toHexString(out_point[m]);
			if(bufv.length()<2) bufv="0"+bufv;
			result = result+" "+bufv;
		}
		result+="\r\n" ;
		return result;
	}
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	
	static String input_Key;
	
	static short Zear_16[]={
		0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0
	};;
	static short dealKey[]={
		0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0		
	};
	static short dealGetK1[]={
		0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0
	};; //128bit
	static short dealGetK2[]={
		0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0
	};; //128bit	
	
	static short bufVector[]={
		0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0
	}; // 128bit	
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
	static void block_xor( short[] dst,  short[] a,  short[] b)
	{
	    for (int j = 0; j < 16; j++) {
	        dst[j] =  (short) ((a[j] ^ b[j])&0xFF);
	    }
	}

	static void block_leftshift( short[] dst,  short[] src)
	{
		int ovf = 0x00;
	    for (int i = 15; i >= 0; i--) {
	        dst[i] = (short)( (src[i] << 1) &0xFF);
	        dst[i] |= ovf;
	        ovf =  ((src[i] & 0x80)!=0) ? 1 : 0;
	    }
	}

	//############################################################		
	public static int KDF( short[] in1,short[] in2, short[] out)
	{

		short AES_Key2Zero[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00
	    };
		//unsigned char outbuf[16];
	///////////////////////////////////////////////////////////////////////	

		aes_128_encrypt(in1, out, AES_Key2Zero);
	
		block_xor( AES_Key2Zero, AES_Key2Zero,in1);

		block_xor( AES_Key2Zero, AES_Key2Zero,out);
	
	///////////////////////////////////////////////////////////////////////
		aes_128_encrypt(in2, out, AES_Key2Zero);
		
		block_xor( AES_Key2Zero, AES_Key2Zero,in2);
		
		block_xor( out, AES_Key2Zero, out);

	///////////////////////////////////////////////////////////////////////
		return 0;
	}
	//  
	public static int CBC( short[] in1, short[] key1, short [] IV, int inputlength, short out[])
	{
		short Zero[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00
	    };
		short INIT_IVZero[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00
	    };
	//#####################################################
		if(IV!=null)
		{		
			System.arraycopy(IV, 0,  INIT_IVZero,0, 16);   // in  to  out
		}
	//#####################################################
		int chari=0;
		while(inputlength>0){
			
			short[] in1_point =new short[16];			
			System.arraycopy(in1, chari, in1_point,0, 16);// in  to  out
			
			block_xor( in1_point, INIT_IVZero, in1_point);
					
			short[] out_point =new short[16];			
			aes_128_encrypt(in1_point, out_point, key1);	
			
			block_xor( INIT_IVZero, Zero, out_point);
			
			System.arraycopy(out_point, 0, out, chari,16);  // in  to  out
			
			chari  +=16;
			inputlength 	-=16;
		}
	//#####################################################
		return 0;
	}

	// Calculate the CAMC
	public static short[] aes_cmac( short[] in,  int length,  short[] out,  short[] key)
	{
		short K1[]=new short[16];
		short K2[]=new short[16];
	///////////////////////////////////////////////////////////////////////	
	    GenerateSubkey(key, K1, K2);
	///////////////////////////////////////////////////////////////////////	
	    int n = (length / 16);
	    boolean flag = false;
	    if (length % 16 != 0) {
	        n++;
	    }

	    if (n == 0) {
	        n = 1;
	    } else if (length % 16 == 0) {
	        flag = true;
	    }
	///////////////////////////////////////////////////////////////////////	
	    short Zero[] = {
			        0x00, 0x00, 0x00, 0x00,
			        0x00, 0x00, 0x00, 0x00,
			        0x00, 0x00, 0x00, 0x00,
			        0x00, 0x00, 0x00, 0x00
			    };
	    //unsigned char M[n][const_Bsize];
	    short []M = new short[n*16];		 
		 for(int i=0;i<n;i++)
		 {
			 System.arraycopy(Zero, 0, M, i*16,16);  // in  to  out
		 }   	  
	    System.arraycopy(in,0,M,0,length);  // in  to  out

	    if (!flag) {
	        //memset(M + length, 0x80, 1);	// M[0]
	    	M[length]= 0x80;
	    }
		
	    
	    short []Mbuf = new short[1*16];		
	    System.arraycopy(M,(n-1)*16,Mbuf,0,16);  // in  to  out
	    if (flag) {
	        //block_xor(	M+(n-1)*16, 	M+(n-1)*16, 	K1);	// M[n-1]  M[n - 1]
	    	block_xor(Mbuf,Mbuf,K1);
	    } else {
	        //block_xor(	M+(n-1)*16, 	M+(n-1)*16, 	K2);	// M[n-1] M[n - 1]
	    	block_xor(Mbuf,Mbuf,K2);
	    }
	    System.arraycopy(Mbuf,0,M,(n-1)*16,16); // in  to  out
	///////////////////////////////////////////////////////////////////////	
	    short X[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00
	    };
	    short[] Y=new short[16];
	    short[] databuf=new short[16];

	    for (int i = 0; i < n - 1; i++) {

	    	System.arraycopy(M,i*16,databuf,0,16); // in  to  out
	        block_xor(Y, 	databuf, 	X);				//M[i]

	        aes_128_encrypt(Y, X, key);
	    }
	    System.arraycopy(M,(n-1)*16,databuf,0,16); // in  to  out
	    block_xor(Y, databuf, X);		//M[n - 1]

	    aes_128_encrypt(Y, out, key);
	///////////////////////////////////////////////////////////////////////	
	 
	    return out;
	}

	// Generate the Sub keys    [16]
	static void GenerateSubkey( short[] key,  short[] K1,  short[] K2)
	{
		short const_Zero[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00
	    };

		short const_Rb[] = {
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x00,
	        0x00, 0x00, 0x00, 0x87
	    };

		short L[]=new short[16];

	    aes_128_encrypt(const_Zero, L, key);
	///////////////////////////////////////////////////////////////////////	
	    block_leftshift(K1, L);

	    if ((L[0] & 0x80)!=0) {
	        block_xor(K1, K1, const_Rb);
	    }
	///////////////////////////////////////////////////////////////////////	
	    block_leftshift(K2, K1);

	    if ((K1[0] & 0x80)!=0) {
	        block_xor(K2, K2, const_Rb);
	    }
	///////////////////////////////////////////////////////////////////////	
	}
	
	// Main Functions
	public static short[] aes_128_encrypt( short in[],  short out[],  short key[])
	{	   
		 short Nk = 4, Nr = 10;
		 short[] w = new short[16 * (Nr + 1)];// 16*11

	    KeyExpansion(key, w, Nk, Nr);
	    Cipher(in, out, w, Nk, Nr);

	    return out;
	}

	 static short[] aes_128_decrypt(short[] in, short[] out, short[] key)
	{
		 short Nk = 4, Nr = 10;
		 short[]w = new short[16 * (Nr + 1)];

	    KeyExpansion(key, w, Nk, Nr);
	    InvCipher(in, out, w, Nk, Nr);

	    return out;
	}

	 static short[] aes_192_encrypt(short[] in, short[] out, short[] key)
	{
		 short Nk = 6, Nr = 12;
		 short[]w = new short[16 * (Nr + 1)];

	    KeyExpansion(key, w, Nk, Nr);
	    Cipher(in, out, w, Nk, Nr);


	    return out;
	}

	 static short[] aes_192_decrypt(short[] in, short[] out, short[] key)
	{	    
		 short Nk = 6, Nr = 12;
		 short[]w = new short[16 * (Nr + 1)];

	    KeyExpansion(key, w, Nk, Nr);
	    InvCipher(in, out, w, Nk, Nr);

	    return out;
	}

	 static short[] aes_256_encrypt(short[] in, short[] out, short[] key)
	{

		 short Nk = 8, Nr = 14;
		 short[]w = new short[16 * (Nr + 1)];
	    
	    KeyExpansion(key, w, Nk, Nr);
	    Cipher(in, out, w, Nk, Nr);

	    return out;
	}

	 static short[] aes_256_decrypt(short[] in, short[] out, short[] key)
	{	   
		 short Nk = 8, Nr = 14;
		 short[]w = new short[16 * (Nr + 1)];

	    KeyExpansion(key, w, Nk, Nr);
	    InvCipher(in, out, w, Nk, Nr);
	   
	    return out;
	}
	///////////////////////////////////////////////////////////////////////	
	// The Cipher         16 16 176  4 10
	 static void Cipher(short[] in, short[] out, short[] w,  short Nk,  short Nr)
	{
	    //unsigned char state[Nk][4];
		 short []state = new short[Nk*4];	 
	    System.arraycopy(in, 0, state, 0, 4 * Nk); // in  to  out
	    
	    short []wbuf=new short[16];
	    System.arraycopy(w, 0, wbuf, 0, 4 * Nk); // in  to  out
	    AddRoundKey(state, wbuf);

	    for ( int round = 0; round < Nr; round++) {

	        SubBytes(state);
	        ShiftRows(state);

	        if (round != (Nr - 1))
	            MixColumns(state);
	        
	        short []datebuf = new short[16];	 
	        System.arraycopy(w, (round + 1) * 16, datebuf, 0, 16); // in  to  out
	        AddRoundKey(state, datebuf);
	    }
	    
	    System.arraycopy(state, 0, out, 0, 4 * Nk); // in  to  out
	}

	 static void InvCipher(short[] in, short[] out, short[] w,  short Nk,  short Nr)
	{
	    //unsigned char state[Nk][4];
		 short []state = new short[Nk*4];	    
	    System.arraycopy(in, 0, state, 0, 16);// in  to  out

	    short[] datebuf=new short[4];
	    System.arraycopy(w,  Nr*16, datebuf,0, 16);// in  to  out
	    AddRoundKey(state, datebuf);

	    for (int round = Nr - 1; round >= 0; round--) {

	        InvShiftRows(state);
	        InvSubBytes(state);
	        
	        System.arraycopy(w, round*16, datebuf, 0, 16);// in  to  out
	        AddRoundKey(state, datebuf);

	        if (round>0)
	            InvMixColumns(state);
	    }

	    System.arraycopy(state, 0, out, 0, Nk * 4);// in  to  out
	}
	///////////////////////////////////////////////////////////////////////	
	// Key Expansion
	 static void KeyExpansion(short[] key, short[] w,  short Nk,  short Nr)
	{
		 short tmp[]=new short[4];	    
	    System.arraycopy(key, 0, w, 0, 4 * Nk);// in  to  out
	    
	    for ( int i = 4 * Nk; i < 4 * (Nr + 1) * 4; i += 4) {

	        //memcpy(tmp, w + i - 4, 4);
	        System.arraycopy(w, i-4, tmp, 0, 4);// in  to  out

	        if (i % (Nk * 4) == 0) {

	            SubWord(RotWord(tmp));

	            for ( char j = 0; j < 4; j++) {
	                tmp[j] ^= Rcon[i / Nk + j];
	            }

	        } else if (Nk > 6 && (i % (Nk * 4)) == 16) {
	            SubWord(tmp);
	        }

	        for (int j = 0; j < 4; j++)
	            w[i + j] = (short) (w[i - Nk * 4 + j] ^ tmp[j]);

	    }
	}

	 static short[] SubWord(short[] word)
	{
	    for (int i = 0; i < 4; i++) {
	        word[i] = sbox[word[i]];
	    }
	    return word;
	}

	 static short[] RotWord(short[] word)
	{
		 short tmp[]={0,0,0,0};	   
	    System.arraycopy(word,0, tmp, 0, 4);// in  to  out
	    for (int i = 0; i < 4; i++) {
	        word[i] = tmp[(i + 1) % 4];
	    }
	    return word;
	}

	// Round Ops  state[4][4]
	 static void SubBytes( short []state)
	{
	    for (int row = 0; row < 4; row++) {
	        for (int col = 0; col < 4; col++) {
	            //state[col][row] = sbox[state[col][row]];
	        	if(state[col*4+row]>255)
	        	{
	        		System.out.print("");
	        	}
				state[col*4+row] =  sbox[state[col*4+row]];
	        }
	    }
//	    System.out.print("");
	}
	//state[4][4]
	 static void InvSubBytes( short []state)
	{
	    for (int row = 0; row < 4; row++) {
	        for (int col = 0; col < 4; col++) {
	            //state[col][row] = invsbox[state[col][row]];
				state[col*4+row] =  invsbox[state[col*4+row]];
	        }
	    }
//	    System.out.print("");
	}
	//state[4][4]
	 static void ShiftRows( short []state)
	{
		 short tmp[]={0,0,0,0};

	    for ( int row = 1; row < 4; row++) {

	        for ( int col = 0; col < 4; col++) {
	            //tmp[col] = state[(row + col) % 4][row];
				tmp[col] = state[ ((row + col) % 4)*4+row];
	        }

	        for ( char col2 = 0; col2 < 4; col2++) {
	            //state[col2][row] = tmp[col2];
				state[col2*4+row] = tmp[col2];
	        }
	    }
//	    System.out.print("");
	}
	//state[4][4]
	 static void InvShiftRows( short []state)
	{
		 short tmp[]={0,0,0,0};
	    for (int row = 1; row < 4; row++) {

	        for ( char col = 0; col < 4; col++) {
	            //tmp[(row + col) % 4] = state[col][row];
				tmp[(row + col) % 4] = state[col*4+row];
	        }

	        for ( char col2 = 0; col2 < 4; col2++) {
	            //state[col2][row] = tmp[col2];
				state[col2*4+row] = tmp[col2];
	        }
	    }
//	    System.out.print("");
	}
	//state[4][4]
	 static void MixColumns( short []state)
	{
		 short tmp[]={0,0,0,0};

		 short matmul[][] = {
	        {0x02, 0x03, 0x01, 0x01},
	        {0x01, 0x02, 0x03, 0x01},
	        {0x01, 0x01, 0x02, 0x03},
	        {0x03, 0x01, 0x01, 0x02}
	    };

	    for (int col = 0; col < 4; col++) {

	        for (int row = 0; row < 4; row++) {
	            //tmp[row] = state[col][row];
				tmp[row] = state[col*4+row];
	        }

	        for (int i = 0; i < 4; i++) {
	            //state[col][i] = 0x00;
				state[col*4+i] = 0x00;
	            for (int j = 0; j < 4; j++) {
	                //state[col][i] ^= mul(matmul[i][j], tmp[j]);
					state[col*4+i] ^= mul(matmul[i][j], tmp[j]);
	            }
	        }
	    }
//	    System.out.print("");
	}
	//state[4][4]
	 static void InvMixColumns( short state[])
	{
		 short tmp[]=new short[4];
		 short matmul[][]={
	        {0x0e, 0x0b, 0x0d, 0x09},
	        {0x09, 0x0e, 0x0b, 0x0d},
	        {0x0d, 0x09, 0x0e, 0x0b},
	        {0x0b, 0x0d, 0x09, 0x0e}
	    };
	    for (int col = 0; col < 4; col++) {
	        for (int row = 0; row < 4; row++) {
	            //tmp[row] = state[col][row];
				tmp[row] = state[col*4+row];
	        }
	        for (int i = 0; i < 4; i++) {
	            //state[col][i] = 0x00;
				state[col*4+i] = 0x00;
	            for (int j = 0; j < 4; j++) {
	                //state[col][i] ^= mul(matmul[i][j], tmp[j]);
					state[col*4+i] ^= mul(matmul[i][j], tmp[j]);
	            }
	        }
	    }
	}

	 static short mul( short a,  short b)
	{
		 short sb[]=new short[4];
		 short out = 0;
	    sb[0] = b;

	    for ( char i = 1; i < 4; i++) {
	    	
	        sb[i] =   (short) ((sb[i - 1] << 1)&0xFF);
	        
	        if ( (sb[i - 1] & 0x80) !=0) {
	        	
	            sb[i] ^= 0x1b;
	        }
	    }
	    for ( int i2 = 0; i2 < 4; i2++) {
	        if ((a >> i2 & 0x01)!=0) {
	            out ^= sb[i2];
	        }
	    }
	    return out;
	}

	//unsigned char state[4][4]   //char[][]  one size?
	 static void AddRoundKey( short[] state, short[] key)
	{
	    for (int row = 0; row < 4; row++) {

	        for (int col = 0; col < 4; col++) {

	            //state[col][row] ^= key[col * 4 + row];
				state[col*4+row] ^= key[col * 4 + row];				
	        }
	    }
	}
	
	// S-box
	static  short [] sbox = {
	    0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5,
	    0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
	    0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0,
	    0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
	    0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc,
	    0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
	    0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a,
	    0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
	    0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0,
	    0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
	    0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b,
	    0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
	    0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85,
	    0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
	    0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5,
	    0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
	    
	    0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17,
	    0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
	    0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88,
	    0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
	    0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c,
	    0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
	    0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9,
	    0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
	    0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6,
	    0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
	    0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e,
	    0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
	    0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94,
	    0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
	    0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68,
	    0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16
	};

	static  short[] invsbox = {
	    0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38,
	    0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
	    0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87,
	    0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
	    0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d,
	    0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
	    0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2,
	    0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
	    0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16,
	    0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
	    0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda,
	    0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
	    0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a,
	    0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
	    0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02,
	    0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
	    0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea,
	    0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
	    0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85,
	    0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
	    0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89,
	    0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
	    0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20,
	    0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
	    0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31,
	    0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
	    0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d,
	    0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
	    0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0,
	    0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
	    0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26,
	    0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d
	};

	static  short  []Rcon = {
	    0x00, 0x00, 0x00, 0x00,
	    0x01, 0x00, 0x00, 0x00,
	    0x02, 0x00, 0x00, 0x00,
	    0x04, 0x00, 0x00, 0x00,
	    0x08, 0x00, 0x00, 0x00,
	    0x10, 0x00, 0x00, 0x00,
	    0x20, 0x00, 0x00, 0x00,
	    0x40, 0x00, 0x00, 0x00,
	    0x80, 0x00, 0x00, 0x00,
	    0x1b, 0x00, 0x00, 0x00,
	    0x36, 0x00, 0x00, 0x00
	};	
	

}
