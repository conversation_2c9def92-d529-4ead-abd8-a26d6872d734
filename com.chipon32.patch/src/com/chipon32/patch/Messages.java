package com.chipon32.patch;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {

	/**
	 * Name of the message bundle.
	 */
	private static final String BUNDLE_NAME = "com.chipon32.patch.messages";
	
	public static String PATCH_MANAGEMENT;
	public static String PATCH_HISTORY;
	public static String UPDATE_DATE;
	public static String UPDATE_VERSION;
	public static String UPDATE_DESCRIPTION;
	public static String PATCH_CONTENT;
	public static String PLUGIN_NAME;
	public static String PLUGIN_VERSION;
	public static String INSTALL;
	public static String UNINSTALL;
	public static String CLOSE;
	public static String SELECT_PATCH_PATH;
	public static String UNINSTALL_START;
	public static String UNINSTALL_IN_PROGRESS;
	public static String WARNING;
	public static String UNINSTALL_COMPLETE;
	public static String MAIN_PATCH_FILE_PARSING_FAILED;
	public static String UPDATE_START;
	public static String UPDATE_IN_PROGRESS;
	public static String CONFIGURATION_FILE_NOT_EXIST;
	public static String PATCH_FILE_NOT_EXIST;
	public static String UNZIP_PATCH_FILE_FAILED;
	public static String NOT_A_VALID_PATCH_FILE;
	public static String FAILED_TO_PARSE_MAIN_PATCH;
	public static String FAILED_TO_PARSE_PATCH;
	public static String DUPLICATION_PATCH_VERSION;
	public static String UPDATE_SUCCESS;
	public static String UPDATE_FAILED;
	public static String UPDATE_SUCCESS_INFO;

	

	static {
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

}
