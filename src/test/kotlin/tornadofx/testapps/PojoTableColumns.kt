package tornadofx.testapps

import javafx.collections.FXCollections
import tornadofx.*
import tornadofx.tests.JavaPerson

class PojoTableColumnsApp : App(PojoTableColumns::class)

class PojoTableColumns : View("Pojo Table Columns") {
    val people = FXCollections.observableArrayList(
            JavaPerson().apply { name = "<PERSON> Do<PERSON>"; primaryEmail = "<EMAIL>"; secondaryEmail = "<EMAIL>"},
            JavaPerson().apply { name = "<PERSON>"; primaryEmail = "<EMAIL>"; secondaryEmail = "<EMAIL>"}
    )

    override val root = tableview(people) {
        column("Name", JavaPerson::getName )
        nestedColumn("Email addresses") {
            column("Primary Email", JavaPerson::getPrimaryEmail )
            column("Secondary Email", JavaPerson::getSecondaryEmail )
        }
        resizeColumnsToFitContent()
    }
}