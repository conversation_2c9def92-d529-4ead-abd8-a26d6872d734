package tornadofx.testapps

import javafx.scene.control.TreeItem
import tornadofx.*
import tornadofx.tests.JavaPerson

class PojoTreeTableColumnsApp : App(PojoTreeTableColumns::class)

class PojoTreeTableColumns : View("Pojo Tree Table Columns") {

    val people = observableListOf(
            <PERSON><PERSON><PERSON>("<PERSON>", "IT Administration", "<EMAIL>", "<EMAIL>", listOf(
                    <PERSON><PERSON><PERSON>("<PERSON>", "IT Help Desk", "<EMAIL>", "<EMAIL>"),
                    <PERSON><PERSON><PERSON>("<PERSON>", "IT Help Desk", "<EMAIL>", "<EMAIL>"))),
            JavaPerson("Erin James", "Human Resources", "<EMAIL>", "<EMAIL>", listOf(
                    <PERSON><PERSON>erson("<PERSON><PERSON><PERSON>", "Customer Service", "<EMAIL>", "<EMAIL>"),
                    <PERSON><PERSON><PERSON>("<PERSON>", "Customer Service", "<EMAIL>", "<EMAIL>"),
                    JavaPerson("Larry Cable", "Customer Service", "<EMAIL>", "<EMAIL>")))
    )

    // Create the root item that holds all top level employees
    val rootItem = TreeItem(JavaPerson().apply { name = "Employees by Manager"; employees = people })

    override val root = treetableview(rootItem) {
        prefWidth = 800.0

        column<JavaPerson, String>("Name", "name").contentWidth(50) // non type safe
        column("Department", JavaPerson::getDepartment).remainingWidth()
        nestedColumn("Email addresses") {
            column("Primary Email", JavaPerson::getPrimaryEmail)
            column("Secondary Email", JavaPerson::getSecondaryEmail)
        }

        // Always return employees under the current person
        populate { it.value.employees }

        // Expand the two first levels
        root.isExpanded = true
        root.children.withEach { isExpanded = true }
        smartResize()
    }
}