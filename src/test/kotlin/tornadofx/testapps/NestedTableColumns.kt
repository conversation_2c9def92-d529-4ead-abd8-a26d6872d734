package tornadofx.testapps

import javafx.collections.FXCollections
import tornadofx.*

class NestedTableColumnsApp : App(NestedTableColumns::class)

class NestedTableColumns : View("Nested Table Columns") {
    data class Person(val name: String, val primaryEmail: String, val secondaryEmail: String)

    val people = FXCollections.observableArrayList(
            Person("<PERSON> Doe", "<EMAIL>", "<EMAIL>"),
            Person("<PERSON>", "<EMAIL>", "<EMAIL>")
    )

    override val root = tableview(people) {
        readonlyColumn("Name", Person::name)
        nestedColumn("Email addresses") {
            readonlyColumn("Primary Email", Person::primaryEmail)
            readonlyColumn("Secondary Email", Person::secondaryEmail)
        }
    }
}