<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ButtonBar?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>


<VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" xmlns="http://javafx.com/javafx/8.0.111" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <GridPane>
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label text="Field1" />
            <Label text="Field2" GridPane.rowIndex="1" />
            <Label text="Field3" GridPane.rowIndex="2" />
            <TextField fx:id="tfField1" GridPane.columnIndex="1" />
            <TextField fx:id="tfField2" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <TextField fx:id="tfField3" GridPane.columnIndex="1" GridPane.rowIndex="2" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </VBox.margin>
      </GridPane>
      <Separator prefWidth="200.0" />
      <ButtonBar prefHeight="40.0" prefWidth="200.0">
        <buttons>
          <Button defaultButton="true" mnemonicParsing="false" onAction="#ok" text="Ok" />
            <Button cancelButton="true" mnemonicParsing="false" onAction="#cancel" text="Cancel" />
        </buttons>
         <VBox.margin>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </VBox.margin>
      </ButtonBar>
   </children>
</VBox>
