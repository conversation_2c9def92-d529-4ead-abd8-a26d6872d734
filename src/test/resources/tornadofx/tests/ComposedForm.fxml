<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ButtonBar?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>

<VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" xmlns="http://javafx.com/javafx/8.0.112" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <Pane fx:id="searchPane">
              <fx:include source="ComposedFormSearch.fxml" />
      </Pane>
      <Pane fx:id="listPane" prefHeight="200.0" prefWidth="200.0">
              <fx:include source="ComposedFormList.fxml" />
      </Pane>
      <Separator prefWidth="200.0" />
      <ButtonBar prefHeight="40.0" prefWidth="200.0">
        <buttons>
          <Button defaultButton="true" mnemonicParsing="false" onAction="#ok" text="Ok" />
            <Button cancelButton="true" mnemonicParsing="false" onAction="#cancel" text="Cancel" />
        </buttons>
      </ButtonBar>
   </children>
</VBox>
