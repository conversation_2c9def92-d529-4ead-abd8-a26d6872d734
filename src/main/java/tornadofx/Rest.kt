package tornadofx

import jakarta.json.JsonArray
import jakarta.json.JsonObject
import javafx.collections.FXCollections
import javafx.collections.ObservableList
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.security.MessageDigest

internal fun MessageDigest.concat(vararg values: String): String {
    reset()
    update(values.joinToString(":").toByteArray(StandardCharsets.ISO_8859_1))
    return digest().hex
}

private val HexChars = "0123456789abcdef".toCharArray()

val ByteArray.hex get() = map(Byte::toInt).joinToString("") { "${HexChars[(it and 0xF0).ushr(4)]}${HexChars[it and 0x0F]}" }

inline fun <reified T : JsonModel> JsonObject.toModel(): T {
    val model = T::class.java.newInstance()
    model.updateModel(this)
    return model
}

inline fun <reified T : JsonModel> JsonArray.toModel(): ObservableList<T> {
    return FXCollections.observableArrayList(map { (it as JsonObject).toModel<T>() })
}

val String.urlEncoded: String get() = URLEncoder.encode(this, StandardCharsets.UTF_8.name())

val Map<*, *>.queryString: String get() {
    val q = StringBuilder()
    forEach { k, v ->
        if (k != null) {
            q.append(if (q.isEmpty()) "?" else "&")
            q.append(k.toString().urlEncoded)
            if (v != null) q.append("=${v.toString().urlEncoded}")
        }
    }
    return q.toString()
}
