.workspace .header {
    -fx-spacing: 0.4em;
}

.workspace .header .heading-container {
    -fx-font-weight: bold;
    -fx-font-size: 1.2em;
    -fx-alignment: center-left;
    -fx-padding: 0 0 0 0.2em;
    -fx-spacing: 1em;
}

.workspace .header .heading-container .text {
    -fx-fill: #5f5f5f;
}

.workspace .header .container {
    -fx-padding: 0 0.2em 0 0.2em;
    -fx-alignment: center-left;
}

.workspace .header .button.icon-only {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 3px;
    -fx-padding: 0.333333em;
}

.workspace .header .button.icon-only:hover {
    -fx-background-color: #e3e3e3;
}

.workspace .header .button.icon-only:armed {
    -fx-background-color: #e3e3e3;
    -fx-background-radius: 1em;
}

.workspace .header .icon {
    -fx-min-width: 16px;
    -fx-min-height: 16px;
    -fx-max-width: 16px;
    -fx-max-height: 16px;
    -fx-background-color: #818181;
}

.workspace .header .icon.back {
    -fx-shape: "M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"
}

.workspace .header .icon.forward {
    -fx-shape: "M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"
}

.workspace .header .icon.refresh {
    -fx-shape: "M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
}

.workspace .header .icon.save {
    -fx-shape: "M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
}

.workspace .header .icon.create {
    -fx-shape: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"
}

.workspace .header .icon.delete {
    -fx-shape: "M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
}