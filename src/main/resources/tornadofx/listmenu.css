.list-menu {
    -fx-graphic-fixed-size: 2em;
}

.list-menu .list-item {
    -fx-cursor: hand;
    -fx-padding: 10;
    -fx-background-color: -fx-shadow-highlight-color, -fx-outer-border, -fx-inner-border, -fx-body-color;
    -fx-background-insets: 0 0 -0.5 0, 0, 0.5, 1.5;
}

.list-menu .list-item .label {
    -fx-text-fill: -fx-text-base-color;
}

.list-menu .list-item:active {
    -fx-background-color: -fx-focus-color, -fx-inner-border, -fx-body-color, -fx-faint-focus-color, -fx-body-color;
    -fx-background-insets: -0.2, 1, 2, -1.4, 2.6;
}

.list-menu .list-item:hover {
    -fx-color: -fx-hover-base;
}

.list-menu .list-item:disabled {
    -fx-opacity: 0.4;
}

.list-menu.blue {
    -fx-graphic-fixed-size: 2.5em;
    -fx-background-color: #1e88cf;
}

.list-menu.blue .list-item {
    -fx-background-color: #1e88cf;
}

.list-menu.blue .list-item * {
    -fx-fill: #fff;
}

.list-menu.blue .list-item:hover .graphic {
    -fx-fill: #ddd;
}

.list-menu.blue .list-item:active {
    -fx-background-color: #3da0e3;
}

.list-menu.blue .list-item:active:hover .graphic {
    -fx-fill: #fff;
}