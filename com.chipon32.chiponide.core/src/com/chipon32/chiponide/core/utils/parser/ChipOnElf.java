package com.chipon32.chiponide.core.utils.parser;

import java.io.IOException;

import org.eclipse.cdt.core.IAddressFactory;
import org.eclipse.cdt.utils.elf.Elf;

/**
 * <AUTHOR> Wu
 */
public class ChipOnElf extends Elf {

	public static final String EM_kungfu32 = "kungfu32";
	public static final String ELF32 = "ELFCLASS32 (32-bit)";

	public ChipOnElf() {
	}

	public ChipOnElf(String file) throws IOException {
		super(file);
	}

	public static Attribute getAttributes(String file) throws IOException {
		try (ChipOnElf elf = new ChipOnElf(file)) {
			return elf.getAttributes();
		}
	}

	public static Attribute getAttributes(byte[] array) throws IOException {
		try (ChipOnElf emptyElf = new ChipOnElf()) {
			emptyElf.ehdr = emptyElf.new ChipOnELFhdr(array);
			emptyElf.sections = new Elf.Section[0];

			return emptyElf.getAttributes();
		}
	}

	@Override
	public Attribute getAttributes() throws IOException {
		Elf.Attribute attribute = super.getAttributes();
		String cpu = switch (ehdr.e_machine) {
			case 0x103 -> ChipOnElf.EM_kungfu32;
			default -> attribute.getCPU();
		};

		return new Attribute(attribute, cpu);
	}

	public class ChipOnELFhdr extends Elf.ELFhdr {

		protected ChipOnELFhdr(byte[] bytes) throws IOException {
			super(bytes);
		}
	}

	public class Attribute extends Elf.Attribute {
		String m_cpu;
		int m_type;
		int m_debugType;
		boolean m_isle;
		IAddressFactory m_addressFactory;

		public Attribute(Elf.Attribute attr, String cpu) {
			this.m_cpu = cpu;
			this.m_type = attr.getType();
			this.m_debugType = attr.getDebugType();
			this.m_isle = attr.isLittleEndian();
			this.m_addressFactory = attr.getAddressFactory();
		}

		public String getElfClass() {
			return switch (ehdr.e_ident[Elf.ELFhdr.EI_CLASS]) {
				case 0 -> "ELFCLASSNONE (Invalid class)";
				case 1 -> {
					yield ELF32;
				}
				case 2 -> "ELFCLASS64 (64-bit)";
				default -> "Unknown class";
			};
		}

		public String getCPU() {
			return this.m_cpu;
		}

		public int getType() {
			return this.m_type;
		}

		public boolean hasDebug() {
			return this.m_debugType != DEBUG_TYPE_NONE;
		}

		public int getDebugType() {
			return this.m_debugType;
		}

		public boolean isLittleEndian() {
			return this.m_isle;
		}

		public IAddressFactory getAddressFactory() {
			return this.m_addressFactory;
		}
	}

}
