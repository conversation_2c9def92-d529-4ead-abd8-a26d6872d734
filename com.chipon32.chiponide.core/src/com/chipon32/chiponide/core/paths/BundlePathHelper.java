/*******************************************************************************
 * 
 * Copyright (c) 2008 <PERSON> (<EMAIL>) and others
 * 
 * This program and the accompanying materials are made
 * available under the terms of the GNU Public License v3
 * which accompanies this distribution, and is available at
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Contributors:
 *     <PERSON> - initial API and implementation
 *     
 * $Id: BundlePathHelper.java,v 1.1 2012/04/01 08:09:44 yanjiahua Exp $
 *     
 *******************************************************************************/
package com.chipon32.chiponide.core.paths;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;

/**
 * Convenience class to get the path for a given resource from a Eclipse bundle.
 * 
 * <AUTHOR>
 * @since 2.1
 */
final class BundlePathHelper {

	/**
	 * @param path
	 *            ChipOnPath for the path
	 * @param bundeid
	 *            Id of the Bundle from which to get the path
	 * @return IPath with the path
	 */
	public static IPath getPath(final ChipOnPath path, final String bundeid) {

		// TODO: not implemented yet
		return new Path("");
	}

}
