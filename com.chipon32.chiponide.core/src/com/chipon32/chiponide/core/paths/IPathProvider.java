/*******************************************************************************
 * 
 * Copyright (c) 2008 <PERSON> (<EMAIL>) and others
 * 
 * This program and the accompanying materials are made
 * available under the terms of the GNU Public License v3
 * which accompanies this distribution, and is available at
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Contributors:
 *     <PERSON> - initial API and implementation
 *     
 * $Id: IPathProvider.java,v 1.1 2012/04/01 08:09:44 yanjiahua Exp $
 *     
 *******************************************************************************/
package com.chipon32.chiponide.core.paths;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;

/**
 * Interface to get the current path from the preference store.
 * 
 * <AUTHOR>
 * @since 2.1
 */
public interface IPathProvider {

	/**
	 * Gets the currently active path.
	 * 
	 * @return <code>IPath</code> to the active source directory
	 */
	public IPath getPath();

	public IPath getPath(IProject project);
}
