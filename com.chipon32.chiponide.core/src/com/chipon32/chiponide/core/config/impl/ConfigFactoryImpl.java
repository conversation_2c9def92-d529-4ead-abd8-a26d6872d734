/**
 */
package com.chipon32.chiponide.core.config.impl;

import com.chipon32.chiponide.core.config.*;

import org.eclipse.emf.ecore.EClass;
import org.eclipse.emf.ecore.EObject;
import org.eclipse.emf.ecore.EPackage;

import org.eclipse.emf.ecore.impl.EFactoryImpl;

import org.eclipse.emf.ecore.plugin.EcorePlugin;

/**
 * <!-- begin-user-doc -->
 * An implementation of the model <b>Factory</b>.
 * <!-- end-user-doc -->
 * @generated
 */
public class ConfigFactoryImpl extends EFactoryImpl implements ConfigFactory {
    /**
     * Creates the default factory implementation.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    public static ConfigFactory init() {
        try {
            ConfigFactory theConfigFactory = (ConfigFactory)EPackage.Registry.INSTANCE.getEFactory(ConfigPackage.eNS_URI);
            if (theConfigFactory != null) {
                return theConfigFactory;
            }
        }
        catch (Exception exception) {
            EcorePlugin.INSTANCE.log(exception);
        }
        return new ConfigFactoryImpl();
    }

    /**
     * Creates an instance of the factory.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    public ConfigFactoryImpl() {
        super();
    }

    /**
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    @Override
    public EObject create(EClass eClass) {
        switch (eClass.getClassifierID()) {
            case ConfigPackage.CHIPON_MESSAGE_CONFIG_MODEL: return createChiponMessageConfigModel();
            case ConfigPackage.CHIPON_DISASSEMBLY_BREAK_POINT_INFO: return createChiponDisassemblyBreakPointInfo();
            default:
                throw new IllegalArgumentException("The class '" + eClass.getName() + "' is not a valid classifier");
        }
    }

    /**
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    public ChiponMessageConfigModel createChiponMessageConfigModel() {
        ChiponMessageConfigModelImpl chiponMessageConfigModel = new ChiponMessageConfigModelImpl();
        return chiponMessageConfigModel;
    }

    /**
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    public ChiponDisassemblyBreakPointInfo createChiponDisassemblyBreakPointInfo() {
        ChiponDisassemblyBreakPointInfoImpl chiponDisassemblyBreakPointInfo = new ChiponDisassemblyBreakPointInfoImpl();
        return chiponDisassemblyBreakPointInfo;
    }

    /**
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @generated
     */
    public ConfigPackage getConfigPackage() {
        return (ConfigPackage)getEPackage();
    }

    /**
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @deprecated
     * @generated
     */
    @Deprecated
    public static ConfigPackage getPackage() {
        return ConfigPackage.eINSTANCE;
    }

} //ConfigFactoryImpl
