/**
 *
 * $Id$
 */
package com.chipon32.chiponide.core.config.validation;


/**
 * A sample validator interface for {@link com.chipon32.chiponide.core.config.ChiponMessageConfigModel}.
 * This doesn't really do anything, and it's not a real EMF artifact.
 * It was generated by the org.eclipse.emf.examples.generator.validator plug-in to illustrate how EMF's code generator can be extended.
 * This can be disabled with -vmargs -Dorg.eclipse.emf.examples.generator.validator=false.
 */
public interface ChiponMessageConfigModelValidator {
    boolean validate();

    boolean validateChipType(String value);
    boolean validateChipTool(String value);
    boolean validateRamValue(String value);

    boolean validateFlashValue(String value);

    boolean validateRamPercent(int value);

    boolean validateFalshPercent(int value);

    boolean validateRamPercent(String value);
    boolean validateFalshPercent(String value);
    boolean validateCheckSumValue1(String value);

    boolean validateCheckCode(String value);
    boolean validateProjectName(String value);

    boolean validateCheckSumValue2(String value);

	boolean validateBuildMode(String value);
}
