/**
 *
 * $Id$
 */
package com.chipon32.chiponide.core.config.validation;

import java.math.BigInteger;

/**
 * A sample validator interface for {@link com.chipon32.chiponide.core.config.ChiponDisassemblyBreakPointInfo}.
 * This doesn't really do anything, and it's not a real EMF artifact.
 * It was generated by the org.eclipse.emf.examples.generator.validator plug-in to illustrate how EMF's code generator can be extended.
 * This can be disabled with -vmargs -Dorg.eclipse.emf.examples.generator.validator=false.
 */
public interface ChiponDisassemblyBreakPointInfoValidator {
    boolean validate();

    boolean validateAddr(String value);
    boolean validateLine(BigInteger value);
}
