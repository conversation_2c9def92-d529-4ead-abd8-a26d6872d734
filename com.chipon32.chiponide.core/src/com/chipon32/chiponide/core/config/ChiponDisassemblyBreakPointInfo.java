/**
 */
package com.chipon32.chiponide.core.config;

import java.math.BigInteger;

import org.eclipse.emf.ecore.EObject;

/**
 * <!-- begin-user-doc -->
 * A representation of the model object '<em><b>Chipon Disassembly Break Point Info</b></em>'.
 * <!-- end-user-doc -->
 *
 * <p>
 * The following features are supported:
 * </p>
 * <ul>
 *   <li>{@link com.chipon32.chiponide.core.config.ChiponDisassemblyBreakPointInfo#getAddr <em>Addr</em>}</li>
 *   <li>{@link com.chipon32.chiponide.core.config.ChiponDisassemblyBreakPointInfo#getLine <em>Line</em>}</li>
 * </ul>
 *
 * @see com.chipon32.chiponide.core.config.ConfigPackage#getChiponDisassemblyBreakPointInfo()
 * @model
 * @generated
 */
public interface ChiponDisassemblyBreakPointInfo extends EObject {
    /**
     * Returns the value of the '<em><b>Addr</b></em>' attribute.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @return the value of the '<em>Addr</em>' attribute.
     * @see #setAddr(String)
     * @see com.chipon32.chiponide.core.config.ConfigPackage#getChiponDisassemblyBreakPointInfo_Addr()
     * @model
     * @generated
     */
    String getAddr();

    /**
     * Sets the value of the '{@link com.chipon32.chiponide.core.config.ChiponDisassemblyBreakPointInfo#getAddr <em>Addr</em>}' attribute.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @param value the new value of the '<em>Addr</em>' attribute.
     * @see #getAddr()
     * @generated
     */
    void setAddr(String value);

    /**
     * Returns the value of the '<em><b>Line</b></em>' attribute.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @return the value of the '<em>Line</em>' attribute.
     * @see #setLine(BigInteger)
     * @see com.chipon32.chiponide.core.config.ConfigPackage#getChiponDisassemblyBreakPointInfo_Line()
     * @model
     * @generated
     */
    BigInteger getLine();

    /**
     * Sets the value of the '{@link com.chipon32.chiponide.core.config.ChiponDisassemblyBreakPointInfo#getLine <em>Line</em>}' attribute.
     * <!-- begin-user-doc -->
     * <!-- end-user-doc -->
     * @param value the new value of the '<em>Line</em>' attribute.
     * @see #getLine()
     * @generated
     */
    void setLine(BigInteger value);

} // ChiponDisassemblyBreakPointInfo
