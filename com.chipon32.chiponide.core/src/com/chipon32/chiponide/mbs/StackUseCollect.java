package com.chipon32.chiponide.mbs;

import java.util.ArrayList;
import java.util.List;

public class StackUseCollect {
    
    boolean    isParent;
    
    String SegmentName;
    String   SegmentAddr;
    Long  SP_Length;
    
    String Content;
    
    private List<StackUseCollect> fChildren = new ArrayList<StackUseCollect>();

    
    public boolean isParent() {
        return isParent;
    }

    public void setParent(boolean isParent) {
        this.isParent = isParent;
    }

    public String getSegmentName() {
        return SegmentName;
    }

    public void setSegmentName(String segmentName) {
        SegmentName = segmentName;
    }

    public String getSegmentAddr() {
        return SegmentAddr;
    }

    public void setSegmentAddr(String segmentAddr) {
        SegmentAddr = segmentAddr;
    }

    public Long getSP_Length() {
        return SP_Length;
    }

    public void setSP_Length(Long sP_Length) {
        SP_Length = sP_Length;
    }

    public String getContent() {
        return Content;
    }

    public void setContent(String content) {
        Content = content;
    }
    public void addContent(String content) {
        if(Content==null )
            Content = content;
        else
            Content +=content;
    }
    
    public List<StackUseCollect> getfChildren() {
        return fChildren;
    }

    public void setfChildren(List<StackUseCollect> fChildren) {
        this.fChildren = fChildren;
    }

    public void addfChildren(StackUseCollect fChildren) {
        if(this.fChildren==null)
        this.fChildren = new ArrayList<StackUseCollect>();
        this.fChildren.add(fChildren);
    }

}
