/*******************************************************************************
 * 
 * Copyright (c) 2008 <PERSON> (<EMAIL>) and others
 * 
 * This program and the accompanying materials are made
 * available under the terms of the GNU Public License v3
 * which accompanies this distribution, and is available at
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Contributors:
 *     Thomas Holland - initial API and implementation
 *     
 * $Id: BuildVariableValues.java,v 1.5 2012/11/09 06:22:21 zhangji Exp $
 *     
 *******************************************************************************/
package com.chipon32.chiponide.mbs;

import java.io.File;

import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.envvar.IBuildEnvironmentVariable;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;

import com.chipon32.chiponide.core.paths.ChipOnPath;
import com.chipon32.chiponide.core.paths.ChipOnPathProvider;
import com.chipon32.chiponide.core.paths.IPathProvider;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;



/**
 * This <code>Enum</code> contains a list of all available variable names.
 * <p>
 * Each Variable knows how to extract its current value from an {@link ChipOnProjectProperties} object,
 * respectively from an {@link IConfiguration}.
 * </p>
 * <p>
 * Currently these Environment Variables are handled:
 * <ul>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Holland
 * @since 2.2
 * @since 2.3 Added DUDEPATH variable (fix for Bug 2136888)
 */

public enum BuildVariableValues {
    //  Cygwin 的nodosfilewarning 属性设置，不提示 平台差异化的 路径 间隔符号 \ or  /
    CYGWIN() {
        // This is only defined to export the BuildArtifact Build Macro as an
        // environment variable in case some cywin dir diff waring
        @Override
        public String getValue(final IConfiguration buildcfg) {            
            return "nodosfilewarning";
        }

        @Override
        public boolean isMacro() {
            return false;
        }
    },
	// 系统路径，编译工具的路径临时添加到 原电脑的前面，优先使用
	PATH() {
		@Override
		public String getValue(final IConfiguration buildcfg) {
			// Get the paths to "cc" and "make" from the PathProvider
			// and return the paths, separated with a System specific path
			// separator.
			// The path to the ccdude executable is handled as a separate
			// variable because at least with cc and ccdude are
			// in the same directory and adding the path to ccdude to the
			// global path would have no effect as the ccdude executable
			// from the ccpath would be used anyway.
		    final IProject project = (IProject) buildcfg.getOwner();
			final StringBuilder paths = new StringBuilder();
			IPathProvider gccpathprovider;
			String buildcfgId = buildcfg.getId();
			if(buildcfgId.contains("llvm")) {
			    gccpathprovider = new ChipOnPathProvider(ChipOnPath.CHIPONCLANG);
			}else {
			    gccpathprovider = new ChipOnPathProvider(ChipOnPath.CHIPONCC);
			}
			final String gccpath = gccpathprovider.getPath(project).toOSString();
			if (gccpath != null && !gccpath.isEmpty()) {
				paths.append(gccpath);
				paths.append(PATH_SEPARATOR);
			}
			/*//make放置到 同一个目录中，不单独定义路径
			final IPathProvider makepathprovider = new ChipOnPathProvider(ChipOnPath.MAKE);
			final String makepath = makepathprovider.getPath().toOSString();
			if (makepath != null && !("".equals(makepath))) {
				paths.append(makepath);
				paths.append(PATH_SEPARATOR);
			}*/
			
			// 编译器的C编译器会单独放置文件的追加到系统 cc1.exe
//	         final IPathProvider cc1pathprovider = new ChipOnPathProvider(ChipOnPath.CHIPONCC_EXE);
//	            final String cc1path = cc1pathprovider.getPath(project).toOSString();
//	            if (cc1path != null && !("".equals(cc1path))) {
//	                paths.append(cc1path);
//	                paths.append(PATH_SEPARATOR);
//	            }
			
            // 编译器的平台工具链位置注册  ld.exe
            final IPathProvider toolpathprovider = new ChipOnPathProvider(ChipOnPath.CHIPONCC_TOOL_EXE);
            final String toolpath = toolpathprovider.getPath(project).toOSString();
            if (toolpath != null && !("".equals(toolpath))) {
                paths.append(toolpath);
                paths.append(PATH_SEPARATOR);
            } 
            
            // 通用工具路径..............................   
            final IPathProvider commontoolpathprovider = new ChipOnPathProvider(ChipOnPath.CHIPON_COMMON);
            if (commontoolpathprovider != null) {
                final String commontoolpath = commontoolpathprovider.getPath(project).toOSString();
                if (commontoolpath!=null && !commontoolpath.isEmpty()) {
                    paths.append(commontoolpath);
                    paths.append(PATH_SEPARATOR);
                }    
            }
                
            //移除ccr1_issue_v0工具链
            String returnPath =  paths.toString();
            returnPath = returnPath.replace("ccr1_issue_v0", "ccr1_issue");
			return returnPath;
		}

		@Override
		public int getOperation() {
			// Prepend our paths to the System paths
			return IBuildEnvironmentVariable.ENVVAR_PREPEND;
		}

		@Override
		public boolean isMacro() {
			// PATH not supported as a BuildMacro，路径不能被编译工具宏指令调用
			return false;
		}

	},
	// 非路径，纯粹的 选择的芯片型号 名称
	CHIP_NAME() {
		@Override
		public String getValue(final IConfiguration buildcfg) {
			final ChipOnProjectProperties props = getPropsFromConfig(buildcfg);
			if (props == null) {
                return "";
            }
			final String chipName = props.getChipName();

			return chipName;
		}
	},
	CHIP_SCRIPT_DIR() {
		@Override
		public String getValue(final IConfiguration buildcfg) {
			final IProject project = (IProject) buildcfg.getOwner();
			final ChipOnProjectProperties props = getPropsFromConfig(buildcfg);
			if (props == null) {
				return "";
			}

			final IPathProvider chipNamepathprovider = new ChipOnPathProvider(ChipOnPath.CHIPON_SCRIPTPATHPATH);
			IPath lkrPath = chipNamepathprovider.getPath(project);
			String returnPath =  lkrPath.toPortableString();
			returnPath = returnPath.replace("ccr1_issue_v0", "ccr1_issue");
			return returnPath;
		}
	},
	// 芯片名称的基本lib文件
//	CHIP_NAME_LIB() {
//		@Override
//		public String getValue(final IConfiguration buildcfg) {
//			final ChipOnProjectProperties props = getPropsFromConfig(buildcfg);
//			if (props == null) {
//                return "";
//            }
//			final String chipName = props.getchipName();
//
//			final String fileName = chipName ;//+ ".lib";
//
//			return fileName;
//		}
//	},
	// 工具链编译的 型号类头文件路径   如std
	GPUTILS_BASIC_INCLUDE_PATH()
	{

		@Override
		public String getValue(final IConfiguration buildcfg) {
		    final IProject project = (IProject) buildcfg.getOwner();
			final StringBuilder paths = new StringBuilder();
			final IPathProvider includePathProvider = new ChipOnPathProvider(ChipOnPath.CHIPON_INCLUDE_HEAD);
			//final IPath chipon16HeaderPath = includePathProvider.getPath().append("ASM_HEAD");
			//final String tmpPath = chipon16HeaderPath.toOSString();
			final String tmpPath = includePathProvider.getPath(project).toPortableString();
			if (tmpPath != null && !("".equals(tmpPath))) {
				paths.append(tmpPath);
//				paths.append(PATH_SEPARATOR);
			}

			return paths.toString();
		}
		
		@Override
		public int getOperation() {
			// Prepend our paths to the System paths
			return IBuildEnvironmentVariable.ENVVAR_REPLACE;
		}
		
	},	

	// 其他通用c的头文件路径，如型号
   GPUTILS_EXPAND_INCLUDE_PATH()
    {

        @Override
        public String getValue(final IConfiguration buildcfg) {
            final IProject project = (IProject) buildcfg.getOwner();
            final StringBuilder paths = new StringBuilder();

            final IPathProvider includePathProvider = new ChipOnPathProvider(ChipOnPath.CHIPON_INCLUDE);
            final String tmpPath = includePathProvider.getPath(project).toPortableString();
            if (tmpPath != null && !("".equals(tmpPath))) {
                paths.append(tmpPath);
            }

            return paths.toString();
        }
        
        @Override
        public int getOperation() {
            // Prepend our paths to the System paths
            return IBuildEnvironmentVariable.ENVVAR_REPLACE;
        }
        
    },
	// 工具链芯片自身的库，如编译器附加部分的浮点操作等.........
	GPUTILS_LIB_PATH() {
		@Override
		public String getValue(final IConfiguration buildcfg) {
			// Get the paths to "gcc" and "make" from the PathProvider
			// and return the paths, separated with a System specific path
			// separator.
			// The path to the avrdude executable is handled as a separate
			// variable because at least with  gcc and dude are
			// in the same directory and adding the path to dude to the
			// global path would have no effect as the dude executable
			// from the gccpath would be used anyway.

			final StringBuilder paths = new StringBuilder();
			final IProject project = (IProject) buildcfg.getOwner();
			final IPathProvider gccpathprovider = new ChipOnPathProvider(ChipOnPath.CHIPON_LIB);  // 查询
			final String gccpath = gccpathprovider.getPath(project).toPortableString();
			if (gccpath != null && !("".equals(gccpath))) {
				paths.append(gccpath);
//				paths.append(PATH_SEPARATOR);
			}
			//移除ccr1_issue_v0工具链
			String returnPath =  paths.toString();
			returnPath = returnPath.replace("ccr1_issue_v0", "ccr1_issue");
			return returnPath;
		}

		@Override
		public int getOperation() {
			// Prepend our paths to the System paths
			return IBuildEnvironmentVariable.ENVVAR_REPLACE;
		}

		@Override
		public boolean isMacro() {
			// PATH not supported as a BuildMacro
			return false;
		}

	},
	// 芯片自身的算法实现库文件，仅C语言调用，汇编也可以连接，但按c寄存器规则使用......
	CHIP_KERNEL_LIB() {
        @Override
        public String getValue(final IConfiguration buildcfg) {
            
            final ChipOnProjectProperties props = getPropsFromConfig(buildcfg);
            if (props == null) {
                return "";
            }     
            String fileName;
            String chipName = props.getChipName();
            if(chipName == null || chipName.equalsIgnoreCase("NULL")){
                return "crtv1";
            }
            IConfigurationProvider  provider = ConfigurationFactory.getProvider(chipName);     
            if(provider==null)
            {            	
            	return "crtv1";
            }
            
            String kernel = provider.getKernel();
            if(Integer.parseInt(kernel,16)==3) 
            {
            	fileName = "crtv3";
            }else if(Integer.parseInt(kernel,16)==2) 
            {
                fileName = "crtv2";
            } else if(Integer.parseInt(kernel,16)==1)
            {
                fileName = "crtv1";
            } else //Integer.parseInt(kernel,16)==0  ，当前规划支持4类差异
            {
                fileName = "crtv1";
            }
            return fileName;
        }
    };

	/** System default Path Separator. On Windows ";", on Posix ":" */
	private final static String	PATH_SEPARATOR	= System.getProperty("path.separator");

	/**
	 * Get the current variable value for the given Configuration
	 * 
	 * @param buildcfg
	 *            <code>IConfiguration</code> for which to get the variable value.
	 * @return <code>String</code> with the current value of the variable.
	 */
	public abstract String getValue(IConfiguration buildcfg);

	/**
	 * @return <code>true</code> if this variable is supported as a build macro.
	 */
	public boolean isMacro() {
		// This method is overridden in some Enum values
		return true;
	}

	/**
	 * @return <code>true</code> if this variable is supported as an environment variable.
	 */
	public boolean isVariable() {
		// This method could be overridden in some Enum values.
		return true;
	}

	/**
	 * Get the Operation code for environment variables.
	 * <p>
	 * Most Variables will return {@link IBuildEnvironmentVariable#ENVVAR_REPLACE}. However the
	 * <code>PATH</code> environment variable will return
	 * {@link IBuildEnvironmentVariable#ENVVAR_PREPEND}.
	 * </p>
	 * 
	 * @see IBuildEnvironmentVariable#getOperation()
	 * 
	 * @return <code>int</code> with the operation code.
	 */
	public int getOperation() {
		// Default is REPLACE.
		// The PATH Variable, which requires ENVVAR_PREPEND, will override this
		// method.
		return IBuildEnvironmentVariable.ENVVAR_REPLACE;
	}

	/**
	 * Get the AVR Project properties for the given Configuration.
	 * 
	 * @param buildcfg
	 *            <code>IConfiguration</code> for which to get the properties.
	 * @return
	 */
	private static ChipOnProjectProperties getPropsFromConfig(final IConfiguration buildcfg) {
		final ProjectPropertyManager manager = ProjectPropertyManager
				.getPropertyManager((IProject) buildcfg.getOwner());
		final ChipOnProjectProperties props = manager.getConfigurationProperties(buildcfg);
		
		return props;
	}

}
