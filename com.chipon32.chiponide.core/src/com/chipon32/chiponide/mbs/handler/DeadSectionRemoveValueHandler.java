package com.chipon32.chiponide.mbs.handler;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.internal.core.Tool;
import org.eclipse.cdt.managedbuilder.internal.core.ToolChain;

public class DeadSectionRemoveValueHandler
        implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {
        if (event == IManagedOptionValueHandler.EVENT_APPLY
                && holder instanceof Tool) {
            String commandValue = "--gc-sections";
            boolean value;
            try {
                value = option.getBooleanValue();
                String command = ((Tool)holder).getToolCommand();

                if(value) {
                    if(!command.contains("--gc-sections")) {
                        command += " " + commandValue;
                    }
                } else {
                    command = command.replaceAll(commandValue, "");
                }
                
                ((Tool)holder).setToolCommand(command);

            } catch (BuildException e1) {
                e1.printStackTrace();
            }
        }
        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        return false;
    }

}
