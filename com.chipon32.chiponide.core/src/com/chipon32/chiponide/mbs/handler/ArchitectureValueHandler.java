package com.chipon32.chiponide.mbs.handler;

import java.util.ArrayList;
import java.util.Iterator;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.internal.core.ToolChain;

public class ArchitectureValueHandler implements IManagedOptionValueHandler {

    @SuppressWarnings("rawtypes")
    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {
        if (event == IManagedOptionValueHandler.EVENT_APPLY && holder instanceof ToolChain) {
            
            IOption cppTargetOption = ((Tool<PERSON>hain) holder)
                    .getToolsBySuperClassId(
                            "com.chipon32.chiponide.tool.cpp.compiler.lang")[0]
                                    .getOptionBySuperClassId(
                                            "com.chipon.tool.c.compiler.clang.base.option.arch");
            if (cppTargetOption != null) {
                cppTargetOption.setValue(option.getValue());
            }

            IOption cTargetOption = ((ToolChain) holder).getToolsBySuperClassId(
                    "com.chipon32.chiponide.tool.c.compiler.lang")[0]
                            .getOptionBySuperClassId(
                                    "com.chipon.tool.c.compiler.clang.base.option.arch");

            if (cTargetOption != null) {
                cTargetOption.setValue(option.getValue());
            }
            
            if (option.getValue() instanceof String) {
                String value = (String) option.getValue();
                IOption linkLibOption = ((ToolChain) holder)
                        .getToolsBySuperClassId(
                                "com.chipon32.chiponide.tool.llvm.linker")[0]
                                        .getOptionBySuperClassId(
                                                "com.chipon32.chiponide.tool.llvm.linker.option.libpath");
                
                IOption cppIncludePathOption = ((ToolChain) holder)
                        .getToolsBySuperClassId(
                                "com.chipon32.chiponide.tool.cpp.compiler.lang")[0]
                                        .getOptionBySuperClassId(
                                                "com.chipon.tool.c.compiler.clang.base.option.incpath");
                IOption cIncludePathOption = ((ToolChain) holder)
                        .getToolsBySuperClassId(
                                "com.chipon32.chiponide.tool.c.compiler.lang")[0]
                                        .getOptionBySuperClassId(
                                                "com.chipon.tool.c.compiler.clang.base.option.incpath");
                

                ArrayList cppIncludePaths = clearIncludePath(cppIncludePathOption);
                ArrayList cIncludePaths = clearIncludePath(cIncludePathOption);

                String linkLib = "";

                try {
                    switch (value) {
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list1":
                        break;
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list2":
                        linkLib = "-L\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/kf32_reduce/lib\"";
                        setIncludePathValue(cppIncludePaths, "kf32_reduce");
                        setIncludePathValue(cIncludePaths, "kf32_reduce");
                        break;
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list3":
                        linkLib = "-L\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/kf32_general/lib\"";
                        setIncludePathValue(cppIncludePaths, "kf32_general");
                        setIncludePathValue(cIncludePaths, "kf32_general");
                        break;
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list4":
                        linkLib = "-L\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/kf32_general_fpu/lib\"";
                        setIncludePathValue(cppIncludePaths, "kf32_general_fpu");
                        setIncludePathValue(cIncludePaths, "kf32_general_fpu");
                        break;
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list5":
                        linkLib = "-L\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/kf32_general_dsp/lib\"";
                        setIncludePathValue(cppIncludePaths, "kf32_general_dsp");
                        setIncludePathValue(cIncludePaths, "kf32_general_dsp");
                        break;
                    case "com.chipon.tool.c.compiler.clang.base.option.arch.list6":
                        linkLib = "-L\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/kf32_general_dsp_fpu/lib\"";
                        setIncludePathValue(cppIncludePaths, "kf32_general_dsp_fpu");
                        setIncludePathValue(cIncludePaths, "kf32_general_dsp_fpu");
                        break;
                    default:
                        break;
                    }

                    linkLibOption.setValue(linkLib);
                    cppIncludePathOption.setValue(cppIncludePaths);
                    cIncludePathOption.setValue(cIncludePaths);
                } catch (BuildException e) {
                    e.printStackTrace();
                }
            }

        }

        return false;
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void setIncludePathValue(ArrayList includePaths, String key) {
        if (includePaths != null) {
            includePaths.add(
                    "\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/" + key + "/lib\"");
            includePaths.add("\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/" + key
                    + "/include\"");
            includePaths.add("\"${GPUTILS_LIB_PATH}/clang-runtimes/kf32/" + key
                    + "/c++/v1\"");
        }

    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private ArrayList clearIncludePath(IOption includePathOption) {
        ArrayList includePaths = null;
        if (includePathOption != null
                && includePathOption.getValue() instanceof ArrayList) {
            includePaths = (ArrayList) includePathOption.getValue();
            Iterator<String> iterator = includePaths.iterator();
            while (iterator.hasNext()) {
                String element = iterator.next().replaceAll("\\\\", "/");
                if (element.contains("clang-runtimes/kf32")) {
                    iterator.remove();
                }
            }
        }
        return includePaths;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        return true;
    }

}
