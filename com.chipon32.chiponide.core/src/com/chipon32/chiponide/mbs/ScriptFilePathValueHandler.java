package com.chipon32.chiponide.mbs;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;


/**
 * 用户自定义设置ld文件路径
 * <AUTHOR>
 *
 */
public class ScriptFilePathValueHandler implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration, IHoldsOptions holder,
            IOption option, String extraArgument, int event) {
       
        String express = "^(-T\").*\"$";
        String scriptfilepath = (String) option.getValue();
        if(!scriptfilepath.matches(express)) {
            if(!scriptfilepath.startsWith("-T\"")) {
                scriptfilepath = "-T\""+scriptfilepath;
            }
            if(!scriptfilepath.endsWith("\"")) {
                scriptfilepath = scriptfilepath+"\"";
            }
            
            try {
                option.setValue(scriptfilepath);
            } catch (BuildException e) {
                e.printStackTrace();
            }
        }
       
        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        // TODO Auto-generated method stub
        return false;
    }

}
