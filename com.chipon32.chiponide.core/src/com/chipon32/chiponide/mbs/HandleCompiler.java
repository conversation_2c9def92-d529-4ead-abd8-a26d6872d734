package com.chipon32.chiponide.mbs;

import org.eclipse.cdt.managedbuilder.core.IBuildObject;
import org.eclipse.cdt.managedbuilder.core.IHoldsOptions;
import org.eclipse.cdt.managedbuilder.core.IManagedOptionValueHandler;
import org.eclipse.cdt.managedbuilder.core.IOption;

public class <PERSON>leCompiler implements IManagedOptionValueHandler {

    @Override
    public boolean handleValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            int event) {
        System.out.println("handle Value?");
        return false;
    }

    @Override
    public boolean isDefaultValue(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument) {
        System.out.println("is DefaultValue?");
        return false;
    }

    @Override
    public boolean isEnumValueAppropriate(IBuildObject configuration,
            IHoldsOptions holder, IOption option, String extraArgument,
            String enumValue) {
        System.out.println("isEnumValueAppropriate?");
        return false;
    }

}
