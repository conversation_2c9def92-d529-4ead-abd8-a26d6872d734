package com.chipon32.chiponide.mbs;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
    private static final String BUNDLE_NAME = "com.chipon32.chiponide.mbs.messages"; //$NON-NLS-1$
    public static String ChiponBuilder_1;
    public static String CrosspathValueHandler_1;
    public static String CrosspathValueHandler_2;
    static {
        // initialize resource bundle
        NLS.initializeMessages(BUNDLE_NAME, Messages.class);
    }

    private Messages() {
    }
}
