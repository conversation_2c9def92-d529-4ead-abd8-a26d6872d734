package com.chipon32.chiponide.mbs;

import org.eclipse.cdt.managedbuilder.core.BuildException;
import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IOption;
import org.eclipse.cdt.managedbuilder.core.IOptionCommandGenerator;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.internal.macros.BuildfileMacroSubstitutor;
import org.eclipse.cdt.managedbuilder.internal.macros.ExplicitFileMacroCollector;
import org.eclipse.cdt.managedbuilder.macros.BuildMacroException;
import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
import org.eclipse.cdt.utils.cdtvariables.IVariableSubstitutor;
import org.eclipse.core.resources.IProject;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;




@SuppressWarnings("restriction")
public class WaValueExLib implements IOptionCommandGenerator {
    
    public WaValueExLib() {
    }        
    /**
     * 根据实际选择的编译器版本选择编译指令；
     */
    @SuppressWarnings({"unused"})
    @Override
    public String generateCommand(IOption option, IVariableSubstitutor macroSubstitutor) {
        
        IConfiguration configuration;
        IProject project;
        ChipOnProjectProperties projectProperties;
        String ReturnStr="";  
        //############################################################################################
        if(macroSubstitutor instanceof ExplicitFileMacroCollector){//


        }
        //############################################################################################
        else if(macroSubstitutor instanceof BuildfileMacroSubstitutor){    //构建文件宏构造设计
            // 调用传递2 获取已有，可以根据项目属性追加
            String[] inputs={""};
            String commd="-l";
            try {
                inputs = option.getBasicStringListValue();
                commd  = option.getCommand();
            } catch (BuildException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
           //################################################# 
            BuildfileMacroSubstitutor buildfileMacroSubstitutor =      (BuildfileMacroSubstitutor)macroSubstitutor;
            IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
            // 从传递的服务对象获取配置，从而可以从配置中获取到项目
            configuration = buildfileMacroSubstitutor.getConfiguration();    
            if(inputs!=null && inputs.length>0)
            {
                for(int i=0;i<inputs.length;i++)
                {
                    String mysrciptFile= inputs[i];                  
                    
                    if(mysrciptFile.startsWith("\""))mysrciptFile=mysrciptFile.substring(1);
                    if(mysrciptFile.endsWith("\""))mysrciptFile=mysrciptFile.substring(0,mysrciptFile.length()-1);                   

                        try {
                            mysrciptFile = provider.resolveValue(mysrciptFile,
                                    "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, configuration);   
                        } catch (BuildMacroException e) {
                            
                        }
                        mysrciptFile=mysrciptFile.replace(" ", "\\ ");
                        mysrciptFile=mysrciptFile.replace("\\", "/");
                        
                       if(mysrciptFile.lastIndexOf("/")>0 && (mysrciptFile.indexOf(":")==1 || mysrciptFile.indexOf("/")==0) )
                           mysrciptFile=mysrciptFile.substring(mysrciptFile.lastIndexOf("/")+1);
                    ReturnStr += commd+"\""+mysrciptFile+"\" ";     
                }
            }
            
        }
        //############################################################################################
     return ReturnStr;  
    }
}
