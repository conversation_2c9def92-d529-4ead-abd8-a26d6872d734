<?xml version="1.0" encoding="UTF-8" standalone="no"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!--
      This is a version of TornadoFx updated for Java21, JavaFx21, and Kotlin 1.9.25.
      It was forked from the jdk10 branch at https://github.com/edvin/tornadofx.
      There are a few minor changes to work with the newer versions of Java, JavaFx, and Kotlin,
      but the majority of the code is unchanged.
    -->

    <groupId>com.github.camork</groupId>
    <artifactId>tornadofx2</artifactId>
    <version>2.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>TornadoFX</name>
    <description>Lightweight JavaFX Framework for Kotlin</description>
    <url>https://github.com/edvin/tornadofx</url>

    <dependencies>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.json</artifactId>
            <version>${org.glassfish.javax.json.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>de.jensd</groupId>
            <artifactId>fontawesomefx-fontawesome</artifactId>
            <version>${de.jensd.fontawesomefx.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit5.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-core</artifactId>
            <version>${org.testfx.testfx-core.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-junit</artifactId>
            <version>${org.testfx.testfx-junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <version>3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-base</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-media</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-swing</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-web</artifactId>
            <version>${javafx.version}</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus</id>
            <name>Nexus Release Repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>sonatype-nexus</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <organization>
        <name>SYSE</name>
        <url>https://www.syse.no/</url>
    </organization>

    <licenses>
        <license>
            <name>The Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Edvin Syse</name>
            <email><EMAIL></email>
            <organization>SYSE AS</organization>
            <organizationUrl>https://www.syse.no</organizationUrl>
        </developer>
        <developer>
            <name>Thomas Nield</name>
            <email><EMAIL></email>
            <organization>Southwest Airlines</organization>
            <organizationUrl>https://www.southwest.com/</organizationUrl>
        </developer>
        <developer>
            <name>Matthew Turnblom</name>
            <email><EMAIL></email>
            <organization>Xactware</organization>
            <organizationUrl>https://www.xactware.com/</organizationUrl>
        </developer>
        <developer>
            <name>Elisha Peterson</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:**************:camork/tornadofx2.git</connection>
        <developerConnection>scm:git:**************:camork/tornadofx2.git</developerConnection>
        <url>**************:camork/tornadofx2.git</url>
    </scm>

    <build>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven.release.version}</version>
                    <configuration>
                        <mavenExecutorId>forked-path</mavenExecutorId>
                        <useReleaseProfile>false</useReleaseProfile>
                        <arguments>-Psonatype-oss-release</arguments>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <version>${kotlin.version}</version>
                <configuration>
                    <jvmTarget>21</jvmTarget>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <release>17</release>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.dokka</groupId>
                <artifactId>dokka-maven-plugin</artifactId>
                <version>${kotlin.version.dokka}</version>
                <executions>
                    <execution>
                        <phase>pre-site</phase>
                        <goals>
                            <goal>javadocJar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <reportNotDocumented>false</reportNotDocumented>
                    <skipEmptyPackages>true</skipEmptyPackages>
                    <jdkVersion>21</jdkVersion>
                    <impliedPlatforms>
                        <platform>JVM</platform>
                    </impliedPlatforms>
                    <noStdlibLink>true</noStdlibLink>
                    <noJdkLink>true</noJdkLink>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.version}</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>aggregate</goal>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>${maven.gpg.version}</version>
                <executions>
                    <execution>
                        <id>sign-artifacts</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven.surefire.version}</version>
                <configuration>
                    <argLine>
                        -Xmx2048m
                        --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED
                        --add-exports tornadofx/tornadofx.tests=kotlin.reflect
                        --add-opens tornadofx/tornadofx.tests=javafx.base
                        --add-reads tornadofx=jdk.httpserver
                    </argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>
<!--
    -add-opens tornadofx.tests=javafx.base
    -add-reads jdk.httpserver=tornadofx
-->


    <profiles>
        <profile>
            <!-- this is for building sources & javadocs, signing, & deploying to staging repo -->
            <id>sonatype-oss-release</id>
            <build>
                <plugins>
                    <!-- build sources -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- build javadocs -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.2.0</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- sign the artifacts with gpg (requires command-line utility) -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- deploy to staging repo -->
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.6.13</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <pluginRepositories>
        <pluginRepository>
            <id>jcenter</id>
            <name>JCenter</name>
            <url>https://jcenter.bintray.com/</url>
        </pluginRepository>
    </pluginRepositories>

    <properties>
        <javafx.version>21.0.7</javafx.version>
        <kotlin.version>1.9.25</kotlin.version>
        <kotlin.version.dokka>1.9.20</kotlin.version.dokka>

        <org.glassfish.javax.json.version>2.0.1</org.glassfish.javax.json.version>
        <junit.version>4.13.2</junit.version>
        <junit5.version>5.13.2</junit5.version>
        <org.testfx.testfx-core.version>4.0.18</org.testfx.testfx-core.version>
        <org.testfx.testfx-junit.version>4.0.18</org.testfx.testfx-junit.version>
        <de.jensd.fontawesomefx.version>4.7.0-9.1.2</de.jensd.fontawesomefx.version>

        <maven.release.version>2.1</maven.release.version>
        <maven.compiler.version>3.8.0</maven.compiler.version>
        <maven.source.version>3.0.1</maven.source.version>
        <maven.jar.version>3.0.2</maven.jar.version>
        <maven.bundle.version>3.5.1</maven.bundle.version>
        <maven.gpg.version>1.4</maven.gpg.version>
        <maven.surefire.version>3.1.2</maven.surefire.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>
</project>
