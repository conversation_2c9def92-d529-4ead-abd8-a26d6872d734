/*******************************************************************************
 * Copyright (c) 2012 Wind River Systems and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     Wind River Systems - initial API and implementation
 *******************************************************************************/
package org.eclipse.cdt.debug.internal.ui.actions;

/**
 * This class was moved to a public package and this implementation is left here
 * for backward compatibility for clients that used this internal class.
 *
 * @deprecated Use the {@link org.eclipse.cdt.debug.ui.breakpoints.AddWatchpointOnVariableActionDelegate}
 * class instead.
 */
@Deprecated
public class AddWatchpointOnVariableActionDelegate
		extends org.eclipse.cdt.debug.ui.breakpoints.AddWatchpointOnVariableActionDelegate {

}
