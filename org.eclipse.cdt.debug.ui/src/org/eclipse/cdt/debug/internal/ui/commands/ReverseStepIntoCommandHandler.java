/*******************************************************************************
 * Copyright (c) 2010 <PERSON><PERSON> and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     <PERSON><PERSON> - initial API and implementation
 *******************************************************************************/
package org.eclipse.cdt.debug.internal.ui.commands;

import org.eclipse.cdt.debug.core.model.IReverseStepIntoHandler;
import org.eclipse.debug.ui.actions.DebugCommandHandler;

/**
 * Command handler to trigger a reverse stepinto operation
 *
 * @since 7.0
 */
public class ReverseStepInto<PERSON>om<PERSON>Handler extends Debug<PERSON>ommandHandler {
	@Override
	protected Class<?> getCommandType() {
		return IReverseStepIntoHandler.class;
	}
}
