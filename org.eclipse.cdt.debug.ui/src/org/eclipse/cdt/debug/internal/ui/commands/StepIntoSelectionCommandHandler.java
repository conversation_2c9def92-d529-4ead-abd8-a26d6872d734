/*******************************************************************************
 * Copyright (c) 2013 <PERSON>sson AB and others.
 *
 * This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License 2.0
 * which accompanies this distribution, and is available at
 * https://www.eclipse.org/legal/epl-2.0/
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 * Contributors:
 *     <PERSON><PERSON> (<PERSON><PERSON> AB) - Support for Step into selection (bug 244865)
 *******************************************************************************/
package org.eclipse.cdt.debug.internal.ui.commands;

import org.eclipse.cdt.debug.core.model.IStepIntoSelectionHandler;
import org.eclipse.debug.ui.actions.DebugCommandHandler;

public class StepIntoSelectionCommandHandler extends Debug<PERSON>ommandHandler {
	@Override
	protected Class<?> getCommandType() {
		return IStepIntoSelectionHandler.class;
	}
}
