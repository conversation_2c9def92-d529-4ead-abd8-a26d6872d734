###############################################################################
# Copyright (c) 2005, 2010 Freescale, Inc.
#
# This program and the accompanying materials
# are made available under the terms of the Eclipse Public License 2.0
# which accompanies this distribution, and is available at
# https://www.eclipse.org/legal/epl-2.0/
#
# SPDX-License-Identifier: EPL-2.0
#
# Contributors:
#     Freescale, Inc. - initial API and implementation
###############################################################################
AddMemBlockDlg_enterAddrAndMemSpace=Enter memory space and address
AddMemBlockDlg_enterExpression=Enter expression
AddMemBlockDlg_or=or
AddMemBlockDlg_MonitorMemory = Monitor Memory

AddMemBlocks_title=Add Memory Monitor
AddMemBlocks_noMemoryBlock = Failed to get memory monitor.
AddMemBlocks_failed =Add Memory Monitor Failed.
AddMemBlocks_input_invalid = Input is invalid.

