<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension-point id="com.chipon32.configtooladaptor.point" name="configtooladaptor" schema="schema/com.chipon32.configtooladaptor.point.exsd"/>
   <extension
         point="org.eclipse.ui.handlers">
       <handler
             class="com.chipon32.configtool.handler.SafeStartAndKeySetHandler"
             commandId="com.chipon32.configtool.command.safeStartAndKeySet">
       </handler>
       <handler
             class="com.chipon32.configtool.handler.UpdateKeyHandler"
             commandId="com.chipon32.configtool.command.updatekey">
       </handler>
       <handler
             class="com.chipon32.configtool.handler.KeyGenerateHandler"
             commandId="com.chipon32.configtool.command.genkey">
       </handler>
       <handler
             class="com.chipon32.configtool.handler.ConfigToolHandler"
             commandId="com.chipon32.configtool.command.root">
       </handler>
   </extension>
   <extension
         point="org.eclipse.ui.commands">
      <command
            id="com.chipon32.configtool.command.safeStartAndKeySet"
            name="SafeStart">
      </command>
      <command
            id="com.chipon32.configtool.command.updatekey"
            name="UpdateKey">
      </command>
      <command
            id="com.chipon32.configtool.command.genkey"
            name="GenerateKey">
      </command>
      <command
            id="com.chipon32.configtool.command.root"
            name="ConfigTool">
      </command>
   </extension>
  <!-- <extension
         point="org.eclipse.ui.menus">
      <menuContribution
            allPopups="false"
            locationURI="menu:help?after=additions">
           <command
                 commandId="com.chipon32.configtool.command.root"
                 icon="icons/ConfigTool.png"
                 label="%Menu_ToolBar_ConfigTool.name"
                 style="push"
                 tooltip="%Menu_ToolBar_ConfigTool.name">
           </command>
      </menuContribution>
      
   </extension>-->

</plugin>
