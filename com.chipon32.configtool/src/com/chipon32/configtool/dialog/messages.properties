ConfigToolDialog_0=Select ChipType
ConfigToolDialog_1=ChipType:
ConfigToolDialog_14=Load Original Key
ConfigToolDialog_15=Load Original Key
ConfigToolDialog_17=Error
ConfigToolDialog_18=Please check that the key file is loaded correctly
ConfigToolDialog_19=Error
ConfigToolDialog_20=Decryption failure
ConfigToolDialog_KeyGenerateTittle=Auth Config and Key Generation
ConfigToolDialog_SafeStartTittle=Secure Boot and Key Config
ConfigToolDialog_TootTittle=ConfigTool
ConfigToolDialog_UserKeyUpdateTittle=User Key Update
CustomDialog_0=export config file success.
CustomDialog_1=import config file success.
CustomDialog_ExportButtonText=Export Config
CustomDialog_ImportButtonText=Import Config

KeyGenerateDialog_0=Prompt
KeyGenerateDialog_1=The debug key validation failed, please import right debug key.
KeyGenerateDialog_2=No permission to update key.
KeyGenerateDialog_3=Write debugkey failed.
KeyGenerateDialog_4=Write debugkey success.
KeyGenerateDialog_5=Generate debugkey cipher text success.
KeyGenerateDialog_10=Generate debugkey cipher text failed.
KeyGenerateDialog_11=Power-off failure
KeyGenerateDialog_12=Key error
KeyGenerateDialog_13=Decryption failed. Please check whether the key is correct
KeyGenerateDialog_14=Key error
KeyGenerateDialog_15=Decryption failed. Please check whether the key is correct
KeyGenerateDialog_6=GenedCipher:
KeyGenerateDialog_7=Please generate debug key.
KeyGenerateDialog_8=AddInfo:
KeyGenerateDialog_9=AuthLevel:
KeyGenerateDialog_AddInfoRule=Additional info should not have "\!" and less than 256 chars.
KeyGenerateDialog_CheckFormFailedInfo=Check form failed.
KeyGenerateDialog_ConnectHardWareFailedInfo=Hardware chip environment connect failed.
KeyGenerateDialog_GenCipherButtonText=GenDebugKey
KeyGenerateDialog_WriteButtonText=Write

SafeStartAndKeySetDialog_0=Power-off failure
SafeStartAndKeySetDialog_1=write key failed.
SafeStartAndKeySetDialog_10=CID must be 28bit hex string. support separator '- _'
SafeStartAndKeySetDialog_11=Enable
SafeStartAndKeySetDialog_12=KeyName
SafeStartAndKeySetDialog_13=*Start Address(4byte)
SafeStartAndKeySetDialog_14=*Length
SafeStartAndKeySetDialog_2=write keys success.
SafeStartAndKeySetDialog_3=4 bytes hex string no support 0x
SafeStartAndKeySetDialog_4=int value between 16 and 2097152
SafeStartAndKeySetDialog_5=write safe start area failed.
SafeStartAndKeySetDialog_6=write safe start area success.
SafeStartAndKeySetDialog_7=Not all keys are empty.
SafeStartAndKeySetDialog_8=safe start area not empty.
SafeStartAndKeySetDialog_9=key must be 16byte hex string. support separator '- _'

UserKeyUpdateDialog_0=BootMac validation failed.
UserKeyUpdateDialog_29=UID must be 15 bytes hex string. support separator '- _'
UserKeyUpdateDialog_1=*New Key(16bytes)
UserKeyUpdateDialog_2=*Auth Key(16bytes)
UserKeyUpdateDialog_3=New Key Name
UserKeyUpdateDialog_4=Auth Key Name
UserKeyUpdateDialog_30=CalcParams
UserKeyUpdateDialog_31=UpdateKey
UserKeyUpdateDialog_35=Auth Key not right.
UserKeyUpdateDialog_37=Update key failed.
UserKeyUpdateDialog_38=Update key success.
UserKeyUpdateDialog_39=Input key check failed.
UserKeyUpdateDialog_46=Generate parameters success.
UserKeyUpdateDialog_5=Power-off failure
UserKeyUpdateDialog_6=Load error
UserKeyUpdateDialog_7=Please check whether the loaded file is correct
