package com.chipon32.configtool.dialog;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONReader;
import com.chipon32.configtool.adaptor.AdaptorCache;
import com.chipon32.configtool.util.cipher.LicenceUnit;
import com.chipon32.configtool.util.cipher.RSA2048_Server;
import com.chipon32.util.provider.ConfigurationFactory;
import com.chipon32.util.provider.MyToolConfig;


public class ConfigToolDialog extends Dialog {
	public static final String CIPHER_TEXT_TITTLE = Messages.KeyGenerateDialog_6;
	Combo chipTypeCombo;
	
	public ConfigToolDialog(Shell parentShell) {
		super(parentShell);
		setShellStyle(getShellStyle()/* | SWT.RESIZE */);
	}

	
	@Override
	protected Control createContents(Composite parent) {
		getShell().setText(Messages.ConfigToolDialog_TootTittle);
		return super.createContents(parent);
	}
	
	@Override
	protected Control createButtonBar(Composite parent) {
		return parent;
	}
	
	@Override
	protected Point getInitialSize() {
		return new Point(500, 200);
	}
	
	@Override
	protected Control createDialogArea(Composite parent) {
		parent.setLayout(new GridLayout(2, true));
		parent.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false));
		
		Group group = new Group(parent, SWT.NONE);
		group.setText(Messages.ConfigToolDialog_0);
		group.setLayout(new GridLayout(2,false));
		group.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		Label chipTypeLb = new Label(group, SWT.NONE);
		chipTypeLb.setText(Messages.ConfigToolDialog_1);
		chipTypeCombo = new Combo(group, SWT.READ_ONLY);
//		String[] chipTypeItems = new String[] {"KF32A158SQX","KF32A158SQW","KF32A158NQW","KF32A158SQV","KF32A158NQV", //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$
//				"KF32A158MQV","KF32A158NQT","KF32A158MQT","KF32A158SQT","KF32A168SQX","KF32A168SQW","KF32A168SQV"}; //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$ //$NON-NLS-4$ //$NON-NLS-5$ //$NON-NLS-6$ //$NON-NLS-7$
		String[] chipTypeItems = getConfigChipTypes();
		chipTypeCombo.setItems(chipTypeItems);
		chipTypeCombo.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		chipTypeCombo.select(3);
		
		Label lbSpace = new Label(group, SWT.NONE); //占空
		Button btnLoadSrcKey = new Button(group, SWT.NONE);
		btnLoadSrcKey.setText(Messages.ConfigToolDialog_14);
		btnLoadSrcKey.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		btnLoadSrcKey.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				FileDialog fileDialog = new FileDialog(Display.getDefault().getActiveShell(), SWT.OPEN);
				fileDialog.setText(Messages.ConfigToolDialog_15);
				fileDialog.setFilterExtensions(new String[] {"*.txt","*.json"}); //$NON-NLS-1$ //$NON-NLS-2$
				String selectedFile = fileDialog.open();
				if(selectedFile!=null) {
					File debugKeyFile = new File(selectedFile);
					if(debugKeyFile.exists()) {
						try {
							String debugKeyStr = ""; //$NON-NLS-1$
							BufferedReader fileReader = new BufferedReader( new InputStreamReader(new FileInputStream(selectedFile), StandardCharsets.UTF_8));
		  	                //FileReader fileReader = new FileReader(selectedFile);					
		  	                JSONReader jsonReader = new JSONReader(fileReader);
		  				    JSONObject jsonRoot = JSON.parseObject(jsonReader.readObject().toString());
		  				    debugKeyStr = jsonRoot.getString(Messages.KeyGenerateDialog_6);
		  				    if(debugKeyStr==null) {
		  				    	MessageDialog.openError(null, Messages.ConfigToolDialog_17, Messages.ConfigToolDialog_18);
		  				    	return;
		  				    }
		  	                
		  				    byte[] plainDebugText = RSA2048_Server.decryptPrivateKey(RSA2048_Server.decodeBase64(debugKeyStr), LicenceUnit.priveteKey);
			  				if(plainDebugText==null) {
			  					MessageDialog.openError(null, Messages.ConfigToolDialog_19, Messages.ConfigToolDialog_20);
			  					return;
			  				}
			  				String plainText = new String(plainDebugText);
			  				String debugKey = plainText.substring(plainText.lastIndexOf("!")+1); //$NON-NLS-1$
			  				
			  				MyToolConfig.debugKey = debugKey;//
		  				    
						} catch (IOException e1) {
							e1.printStackTrace();
						}
					}
				}
							
				
			}
		});
		//=====================================================================================================
		
		Composite containerBtn = new Composite(parent, SWT.NONE);
		containerBtn.setLayout(new GridLayout(1, true));
		containerBtn.setLayoutData(new GridData(GridData.FILL_BOTH));
		Button button1 = new Button(containerBtn, SWT.PUSH);
		button1.setText(Messages.ConfigToolDialog_KeyGenerateTittle);
		button1.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		button1.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				getShell().setVisible(false);
		    	CustomDialog dialog = new KeyGenerateDialog(getShell(), AdaptorCache.INSTANCE.getAdaptor()).init(true, true);
				dialog.setChipType(chipTypeCombo.getItem(chipTypeCombo.getSelectionIndex()).trim());
		    	dialog.open();
			}
		});
		
		Button button2 = new Button(containerBtn, SWT.PUSH);
		button2.setText(Messages.ConfigToolDialog_SafeStartTittle);
		button2.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		button2.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				getShell().setVisible(false);
		    	CustomDialog dialog = new SafeStartAndKeySetDialog(getShell(), AdaptorCache.INSTANCE.getAdaptor()).init(true, true);
		    	dialog.setChipType(chipTypeCombo.getItem(chipTypeCombo.getSelectionIndex()).trim());
		    	dialog.open();
			}
		});
		
		Button button3 = new Button(containerBtn, SWT.PUSH);
		button3.setText(Messages.ConfigToolDialog_UserKeyUpdateTittle);
		button3.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
		button3.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				getShell().setVisible(false);
		    	CustomDialog dialog = new UserKeyUpdateDialog(getShell(), AdaptorCache.INSTANCE.getAdaptor()).init(true, true);
		    	dialog.setChipType(chipTypeCombo.getItem(chipTypeCombo.getSelectionIndex()).trim());
		    	dialog.open();
			}
		});
		
		return containerBtn;
		
	}


	/**
	 * get chipTypes
	 * @return
	 */
	private String[] getConfigChipTypes() {
		List<String> allChiptypes = ConfigurationFactory.getchipTypeList();//芯片型号分类，如KF32A156、KF32A158
		Map<String, List<String>> chipTypesMap= ConfigurationFactory.getTypeAndNameMap();
		
		List<String> configChiptypes = new ArrayList<>();
		
		for(String chiptype:allChiptypes) {
			if(chiptype.contains("158")||chiptype.contains("168")) {
				configChiptypes.addAll(chipTypesMap.get(chiptype));
			}
		}
		
		//String[] cfgChiptypesArray = new String[configChiptypes.size()];
		String[] cfgChiptypesArray = configChiptypes.toArray(new String[0]);
		return cfgChiptypesArray;
	}
}
