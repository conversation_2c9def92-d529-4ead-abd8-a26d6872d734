package com.chipon32.configtool.dialog;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Widget;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONReader;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chipon32.configtool.adaptor.ProAndIDEAdaptor;
import com.chipon32.configtool.enums.Key;
import com.chipon32.configtool.util.Constants;
import com.chipon32.configtool.util.FileUtil;
import com.chipon32.configtool.util.FormatUtil;

public class UserKeyUpdateDialog extends CustomDialog implements ModifyListener, FocusListener {
	private static final String FID_TITTLE = "FID"; //$NON-NLS-1$
	private static final String CID_TITTLE = "*CID"; //$NON-NLS-1$
	private static final String NEW_KEY_VALUE_TITTLE = Messages.UserKeyUpdateDialog_1; 
	private static final String AUTH_KEY_VALUE_TITTLE = Messages.UserKeyUpdateDialog_2; 
	private static final String NEW_KEY_NAME_TITTLE = Messages.UserKeyUpdateDialog_3; 
	private static final String AUTH_KEY_NAME_TITTLE = Messages.UserKeyUpdateDialog_4; 
	private static final String DEFAULT_CONFIG_FILE_NAME = "UserKeyUpdate"; //$NON-NLS-1$
	private static final String M1_TITTLE = "M1"; //$NON-NLS-1$
	private static final String M2_TITTLE = "M2"; //$NON-NLS-1$
	private static final String M3_TITTLE = "M3"; //$NON-NLS-1$
	private static final String M4_TITTLE = "M4"; //$NON-NLS-1$
	private static final String M5_TITTLE = "M5"; //$NON-NLS-1$
	private static final String K1_TITTLE = "K1"; //$NON-NLS-1$
	private static final String K2_TITTLE = "K2"; //$NON-NLS-1$
	private static final String K3_TITTLE = "K3"; //$NON-NLS-1$
	private static final String K4_TITTLE = "K4"; //$NON-NLS-1$
	private static final String UID_TITTLE = "UID"; //$NON-NLS-1$
	private static final short[] KEY_UPDATE_ENC_C = new short[] {
			0x01,0x01,0x53,0x48,
			0x45,0x00,0x80,0x00,
			0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0xB0};
	private static final short[] KEY_UPDATE_MAC_C = new short[] {
			0x01,0x02,0x53,0x48,
			0x45,0x00,0x80,0x00,
			0x00,0x00,0x00,0x00,
			0x00,0x00,0x00,0xB0};
	private Text authKeyVT;
	private Text newKeyVT;
	private Text CIDT;
	private Button setFIDButton;
	private Combo authKeyC;
	private Combo newKeyC;
	private Text m1VL;
	private Text m2VL;
	private Text m3VL;
	private Text m4VL;
	private Text m5VL;
	private Text k1VL;
	private Text k2VL;
	private Text k3VL;
	private Text k4VL;
	private Text uidVT;
	private Button applyButton;

	public UserKeyUpdateDialog(Shell shell, ProAndIDEAdaptor adaptor) {
		super(shell,Messages.ConfigToolDialog_UserKeyUpdateTittle, adaptor);
	}
	
	@Override
	public Control createDialogArea(Composite parent) {
		final Composite container = new Composite(parent, SWT.NONE);
		container.setLayoutData(new GridData(GridData.FILL_BOTH));
		container.setLayout(new GridLayout(1, true));

		makeUpdateKeyArea(container);
		
		makeMKArea(container);
		return container;
	}

	private void makeUpdateKeyArea(final Composite container) {
		Composite group = new Composite(container, SWT.None);
		group.setLayout(new GridLayout(4, false));
		group.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false,1,1));
		
		Label authKeyLabel = new Label(group, SWT.NONE);
		authKeyLabel.setText(AUTH_KEY_NAME_TITTLE + ":"); //$NON-NLS-1$
		authKeyLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
        authKeyC = new Combo(group, SWT.READ_ONLY);
        authKeyC.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
        String[] firstComboLiStrings = new String[] {Key.MASTER_ECU_KEY.getName(), Key.BOOT_MAC_KEY.getName(), Key.KEY_01.getName(), Key.KEY_02.getName(), Key.KEY_03.getName(), Key.KEY_04.getName(), Key.KEY_05.getName(), Key.KEY_06.getName(), Key.KEY_07.getName(), Key.KEY_08.getName(), Key.KEY_09.getName(), Key.KEY_10.getName()};
        authKeyC.setItems(firstComboLiStrings);
        authKeyC.select(0);
        authKeyC.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				String fromKey = authKeyC.getText();
				Key selKey = Key.getKey(fromKey);
				newKeyC.setItems(selKey.getNext());
				newKeyC.select(0);
			}
		});

		Label newKeyLabel = new Label(group, SWT.NONE);
		newKeyLabel.setText(NEW_KEY_NAME_TITTLE+":"); //$NON-NLS-1$
		newKeyLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
        newKeyC = new Combo(group, SWT.READ_ONLY);
        newKeyC.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 1, 1));
        newKeyC.setItems(Key.MASTER_ECU_KEY.getNext());
        newKeyC.select(0);
		
        
		Label authKeyVLabel = new Label(group, SWT.NONE);
		authKeyVLabel.setText(AUTH_KEY_VALUE_TITTLE+":"); //$NON-NLS-1$
		authKeyVLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		authKeyVT = new Text(group, SWT.BORDER);
		authKeyVT.setToolTipText(Messages.SafeStartAndKeySetDialog_9);
		authKeyVT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
		authKeyVT.addModifyListener(this);
		authKeyVT.addFocusListener(this);
		
		
		Label newKeyVLabel = new Label(group, SWT.NONE);
		newKeyVLabel.setText(NEW_KEY_VALUE_TITTLE+":"); //$NON-NLS-1$
		newKeyVLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		newKeyVT = new Text(group, SWT.BORDER);
		newKeyVT.setToolTipText(Messages.SafeStartAndKeySetDialog_9); //$NON-NLS-1$
		newKeyVT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
		newKeyVT.addModifyListener(this);
		newKeyVT.addFocusListener(this);

		
		Label CIDLabel = new Label(group, SWT.NONE);
		CIDLabel.setText(CID_TITTLE+":"); //$NON-NLS-1$
		CIDLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		CIDT = new Text(group, SWT.BORDER);
		CIDT.setToolTipText(Messages.SafeStartAndKeySetDialog_10);
		CIDT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
		CIDT.addModifyListener(this);
		CIDT.addFocusListener(this);

		Label FIDLabel = new Label(group, SWT.NONE);
		FIDLabel.setText(FID_TITTLE+":"); //$NON-NLS-1$
		FIDLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		
		Composite groupFID = new Composite(group, SWT.None);
		GridLayout FIDGL = new GridLayout(2, false);
		FIDGL.marginHeight= 0;
		FIDGL.marginWidth= 0;
		groupFID.setLayout(FIDGL);
		groupFID.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false,1,1));
		
		final Text FIDText = new Text(groupFID, SWT.BORDER);
		FIDText.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, true, false, 1, 1));
		FIDText.setEditable(false);
		FIDText.setText("000000"); //$NON-NLS-1$
		
		setFIDButton = new Button(groupFID, SWT.PUSH);
		setFIDButton.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false, 1, 1));
		setFIDButton.setText(String.format("Set %s", FID_TITTLE)); //$NON-NLS-1$
		setFIDButton.setData(FIDConfigDialog.DATA_KEY_FID, "000000"); //$NON-NLS-1$
		setFIDButton.addListener(SWT.Selection, new Listener() {

			@Override
			public void handleEvent(Event event) {
				String FID = (String) setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID);
				FIDConfigDialog fidSetDialog = new FIDConfigDialog(getShell(), FID);
				if(fidSetDialog.open() == Dialog.OK) {
					setFIDButton.setData(FIDConfigDialog.DATA_KEY_FID, fidSetDialog.getFID());
					FIDText.setText(fidSetDialog.getFID());
				}
			}
			
		});
	}
	
	
	private void makeMKArea(Composite container) {
		Composite mkGroup = new Composite(container, SWT.BORDER);
		mkGroup.setLayout(new GridLayout(4, false));
		mkGroup.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false,1,1));
		
		Label uidL = new Label(mkGroup, SWT.NONE);
		uidL.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		uidL.setText(UID_TITTLE);
		uidVT = new Text(mkGroup, SWT.BORDER);
		uidVT.setToolTipText(Messages.UserKeyUpdateDialog_29);
		uidVT.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
		uidVT.addModifyListener(this);
		uidVT.addFocusListener(this);
		
		Label k1L = new Label(mkGroup, SWT.NONE);
		k1L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		k1L.setText(K1_TITTLE);
	    k1VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
	    k1VL.setEditable(false);
		k1VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label k2L = new Label(mkGroup, SWT.NONE);
		k2L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		k2L.setText(K2_TITTLE);
		k2VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		k2VL.setEditable(false);
		k2VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label m1L = new Label(mkGroup, SWT.NONE);
		m1L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		m1L.setText(M1_TITTLE);
		m1VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		m1VL.setEditable(false);
		m1VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label m2L = new Label(mkGroup, SWT.NONE);
		m2L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		m2L.setText(M2_TITTLE);
		m2VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		m2VL.setEditable(false);
		m2VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));

		Label m3L = new Label(mkGroup, SWT.NONE);
		m3L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		m3L.setText(M3_TITTLE);
		m3VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		m3VL.setEditable(false);
		m3VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label k3L = new Label(mkGroup, SWT.NONE);
		k3L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		k3L.setText(K3_TITTLE);
		k3VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		k3VL.setEditable(false);
		k3VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label k4L = new Label(mkGroup, SWT.NONE);
		k4L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		k4L.setText(K4_TITTLE);
		k4VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		k4VL.setEditable(false);
		k4VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label m4L = new Label(mkGroup, SWT.NONE);
		m4L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		m4L.setText(M4_TITTLE);
		m4VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		m4VL.setEditable(false);
		m4VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		Label m5L = new Label(mkGroup, SWT.NONE);
		m5L.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER,false, false, 1, 1));
		m5L.setText(M5_TITTLE);
		m5VL = new Text(mkGroup, SWT.WRAP | SWT.BORDER);
		m5VL.setEditable(false);
		m5VL.setLayoutData(new GridData(SWT.FILL, SWT.CENTER,true, false, 3, 1));
		
		applyButton = new Button(mkGroup, SWT.PUSH);
		applyButton.setText(Messages.UserKeyUpdateDialog_30);
		applyButton.setLayoutData(new GridData(SWT.RIGHT, SWT.CENTER,false, false, 4, 1));
		applyButton.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				calcMK();
			}
		});

	}
	
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		super.createButtonsForButtonBar(parent);
		getButton(IDialogConstants.OK_ID).setText(Messages.UserKeyUpdateDialog_31);
	}

	@Override
	protected boolean checkFormValid() {
		boolean res = true;
		res &= checkKey(authKeyVT);
		res &= checkKey(newKeyVT);
		res &= checkCID(CIDT);
		return res;
	}
	
	@Override
	protected void handleChanged() {
		boolean checkForm = checkFormValid();
		getButton(IDialogConstants.OK_ID).setEnabled(checkForm);
		applyButton.setEnabled(checkUID() && checkForm);
		
	}

	private boolean checkUID() {
		return FormatUtil.checkHexStr(uidVT, 30);
	}
	
	@Override
	protected void okPressed() {
		boolean res = true;
		res &= checkFormValid();
		if(!res) {
			adaptor.printErrorMsg(Messages.KeyGenerateDialog_CheckFormFailedInfo);
			return;
		}

		Key authKey = Key.getKey(authKeyC.getText());
		Key newKey = Key.getKey(newKeyC.getText());
		String cid = FormatUtil.replaceString(CIDT.getText(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		String authKeyV = FormatUtil.replaceString(authKeyVT.getText(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		String newKeyV = FormatUtil.replaceString(newKeyVT.getText(), Constants.KEY_SPERATOR_REGEX, ""); //$NON-NLS-1$
		String fidBinStr = (String) setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID);
		res &=adaptor.powerOnBeforeWriteData(chipType);
		if(!res) {
			adaptor.printErrorMsg(Messages.KeyGenerateDialog_ConnectHardWareFailedInfo);
			return;
		}
		String authKeyVOld = getKey(authKey.getStartAddr());
		if(!authKeyV.toUpperCase().equals(authKeyVOld.toUpperCase())) {
			adaptor.powerOff();
			adaptor.printErrorMsg(Messages.UserKeyUpdateDialog_35);
			return;
		}
		long oldCID = getCID(FormatUtil.decimalStr2HexStr(String.valueOf(FormatUtil.hexStr2Decimal(newKey.getStartAddr()) + Constants.KEY_LEN)));
		if(oldCID>=FormatUtil.hexStr2Decimal(cid)) {
			adaptor.powerOff();
			adaptor.printErrorMsg(String.format("new CID: %s is smaller than last in hardware: %s.", FormatUtil.hexStr2Decimal(cid), oldCID)); //$NON-NLS-1$
			return;
		}
		String fidBinStrOld = getFID(FormatUtil.decimalStr2HexStr(String.valueOf(FormatUtil.hexStr2Decimal(newKey.getStartAddr()) + Constants.KEY_LEN)));
		if(!FIDConfigDialog.getBootProtection(fidBinStrOld)) {
			if(!FIDConfigDialog.getDebugProtection(fidBinStrOld)) {
				res &= updateKey(newKey.getStartAddr(), newKeyV, cid, fidBinStr, adaptor);
			} else {
				// no need judge in debug state, it must be false.
				res &= updateKey(newKey.getStartAddr(), newKeyV, cid, fidBinStr, adaptor);
			}
		} else {
			if(!checkBootMACKey()) {
				res &=  false;
			} else {
				if(!FIDConfigDialog.getDebugProtection(fidBinStrOld)) {
					res &= updateKey(newKey.getStartAddr(), newKeyV, cid, fidBinStr, adaptor);
				} else {
					// no need judge in debug state, it must be false.
					res &= updateKey(newKey.getStartAddr(), newKeyV, cid, fidBinStr, adaptor);
				}
			}
		} 
		if(!res) {
			adaptor.printErrorMsg(Messages.UserKeyUpdateDialog_37);
		}else {
			adaptor.printInfoMsg(Messages.UserKeyUpdateDialog_38);
		}
		
		if(!adaptor.powerOff()) {
			adaptor.printInfoMsg(Messages.UserKeyUpdateDialog_5);
		}
	}
	
	public static boolean updateKey(String addr, String newKey, String cid, String fidBinStr, ProAndIDEAdaptor adaptor) {
		String cid8 = FormatUtil.toEight(cid);
		String fid8 = FormatUtil.toEight(FormatUtil.binaryStr2HexStr(fidBinStr));
		newKey = FormatUtil.list2String(FormatUtil.reWriteReverseData(FormatUtil.string2List(newKey)));
		cid8 = FormatUtil.list2String(FormatUtil.reWriteReverseData(FormatUtil.string2List(cid8)));
		fid8 = FormatUtil.list2String(FormatUtil.reWriteReverseData(FormatUtil.string2List(fid8)));
		List<String> data = FormatUtil.string2List(newKey + cid8 + fid8);
		if(data.size()!=24) {
			adaptor.printErrorMsg(Messages.UserKeyUpdateDialog_39);
			return false;
		}
		return adaptor.writeData(data, addr);
	}

	public String getKey(String keyAddr) {
		List<String> key = adaptor.readData(adaptor.getAimStorageCFGUser(), keyAddr, Constants.KEY_LEN);
		return FormatUtil.list2String(FormatUtil.reWriteReverseData(key));
	}

	public String getFID(String startAddr) {
		List<String> cidfid = adaptor.readData(
				adaptor.getAimStorageCFGUser(), 
				startAddr, Constants.FID_LEN+Constants.CID_LEN);
		if(FormatUtil.isEmpty(cidfid)) {
			return "00000000"; //$NON-NLS-1$
		}
		List<String> fid = cidfid.subList(Constants.CID_LEN, Constants.FID_LEN+Constants.CID_LEN);
		String fidString = FormatUtil.hexString2binaryString(FormatUtil.list2String(FormatUtil.reWriteReverseData(fid)));
		return fidString.substring(fidString.length()-6);
	} 
	
	public Long getCID(String startAddr) {
		List<String> cidfid = adaptor.readData(
				adaptor.getAimStorageCFGUser(), 
				startAddr, Constants.FID_LEN+Constants.CID_LEN);
		if(FormatUtil.isEmpty(cidfid)) {
			return 0l;
		}
		List<String> cidL = cidfid.subList(0, Constants.CID_LEN);
		return FormatUtil.hexStr2Decimal(FormatUtil.list2String(FormatUtil.reWriteReverseData(cidL)));
	} 

	public boolean checkBootMACKey() {
		List<String> old_boot_mac_key = adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.BOOT_MAC_KEY_ADDR, Constants.KEY_LEN);
		if(FormatUtil.isEmpty(old_boot_mac_key)) {
			return true;
		}
		old_boot_mac_key = FormatUtil.reWriteReverseData(old_boot_mac_key);
		List<String> old_startAddr = adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.SAFE_START_CONFIG_ADDR, 4);
		old_startAddr = FormatUtil.reWriteReverseData(old_startAddr);
		List<String> old_len = adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.SAFE_START_CONFIG_LEN_ADDR, 4);
		old_len = FormatUtil.reWriteReverseData(old_len);
		List<String> old_mode = adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.SAFE_START_CONFIG_MODE_ADDR, 4);
		if(!FormatUtil.list2String(old_mode).toUpperCase().equals(Constants.SAFE_START_STRICT_MODE)) {
			return true;
		}
		List<String> old_boot_mac = adaptor.readData(adaptor.getAimStorageCFGUser(), Constants.BOOT_MAC_ADDR, Constants.KEY_LEN);
		old_boot_mac = FormatUtil.reWriteReverseData(old_boot_mac);
		adaptor.rePower();
		List<String> data = adaptor.readData(adaptor.getAimStorageFlash(), FormatUtil.list2String(old_startAddr), FormatUtil.hexStrList2Decimal(old_len).intValue());
		data = FormatUtil.reVerseDataForCMAC(data);
		List<String> CMAC_Result = adaptor.calcCMAK(old_boot_mac_key, FormatUtil.hexStrList2Decimal(old_startAddr), FormatUtil.hexStrList2Decimal(old_len), data);
		if(FormatUtil.isSame(CMAC_Result, old_boot_mac)) {
			return true;
		}
		adaptor.printErrorMsg(Messages.UserKeyUpdateDialog_0);
		return false;
	}

	static boolean checkCID(Text text) {
		return FormatUtil.checkHexStr(text, 7);
	}

	static boolean checkKey(Text text) {
		return FormatUtil.checkHexStr(text, 32);
	}
	
	@Override
	protected void exportConfigFile() {
		JSONObject jsonRoot = new JSONObject();
		jsonRoot.put(AUTH_KEY_NAME_TITTLE, authKeyC.getText());
		jsonRoot.put(NEW_KEY_NAME_TITTLE, newKeyC.getText());
		jsonRoot.put(AUTH_KEY_VALUE_TITTLE, authKeyVT.getText());
		jsonRoot.put(NEW_KEY_VALUE_TITTLE, newKeyVT.getText());
		jsonRoot.put(CID_TITTLE, CIDT.getText());
		jsonRoot.put(FID_TITTLE, setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID));
		jsonRoot.put(UID_TITTLE, uidVT.getText());
		jsonRoot.put(M1_TITTLE, m1VL.getText());
		jsonRoot.put(M2_TITTLE, m2VL.getText());
		jsonRoot.put(M3_TITTLE, m3VL.getText());
		jsonRoot.put(M4_TITTLE, m4VL.getText());
		jsonRoot.put(M5_TITTLE, m5VL.getText());
		jsonRoot.put(K1_TITTLE, k1VL.getText());
		jsonRoot.put(K2_TITTLE, k2VL.getText());
		jsonRoot.put(K3_TITTLE, k3VL.getText());
		jsonRoot.put(K4_TITTLE, k4VL.getText());
		File file = FileUtil.openSaveFileDialog(getShell(), DEFAULT_CONFIG_FILE_NAME, adaptor);
		if(file == null) {
			return;
		}
		try(FileWriter fileWriter = new FileWriter(file, StandardCharsets.UTF_8)) {
			JSON.writeJSONStringTo(jsonRoot, fileWriter, SerializerFeature.PrettyFormat);
		} catch (IOException e) {
			e.printStackTrace();
		}
		super.exportConfigFile();
	}
	
	@Override
	protected void importConfigFile() {
		File file = FileUtil.openOpenFileDIalog(getShell(), adaptor);
		if(file == null) {
			return;
		}
		try (
			BufferedReader fileReader = new BufferedReader( new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
            //FileReader fileReader = new FileReader(selectedFile);				
			JSONReader jsonReader = new JSONReader(fileReader);)  {
			JSONObject jsonRoot = JSON.parseObject(jsonReader.readObject().toString());
			if(jsonRoot.getString(AUTH_KEY_VALUE_TITTLE)==null || jsonRoot.getString(NEW_KEY_VALUE_TITTLE)==null || jsonRoot.getString(AUTH_KEY_NAME_TITTLE)==null ||
					jsonRoot.getString(NEW_KEY_NAME_TITTLE)==null || jsonRoot.getString(CID_TITTLE)==null || jsonRoot.getString(FID_TITTLE)==null ||
					jsonRoot.getString(UID_TITTLE)==null || jsonRoot.getString(M1_TITTLE)==null || jsonRoot.getString(M2_TITTLE)==null ||
					jsonRoot.getString(M3_TITTLE)==null || jsonRoot.getString(M4_TITTLE)==null || jsonRoot.getString(M5_TITTLE)==null ||
					jsonRoot.getString(K1_TITTLE)==null ||jsonRoot.getString(K2_TITTLE)==null || jsonRoot.getString(K3_TITTLE)==null || jsonRoot.getString(K4_TITTLE)==null) {
				MessageDialog.openError(null, Messages.UserKeyUpdateDialog_6, Messages.UserKeyUpdateDialog_7);
				return;
			}
			authKeyVT.setText(jsonRoot.getString(AUTH_KEY_VALUE_TITTLE));
			newKeyVT.setText(jsonRoot.getString(NEW_KEY_VALUE_TITTLE));
			authKeyC.select(authKeyC.indexOf(jsonRoot.getString(AUTH_KEY_NAME_TITTLE)));
			newKeyC.select(newKeyC.indexOf(jsonRoot.getString(NEW_KEY_NAME_TITTLE)));
			CIDT.setText(jsonRoot.getString(CID_TITTLE));
			setFIDButton.setData(FIDConfigDialog.DATA_KEY_FID, jsonRoot.getString(FID_TITTLE));
			uidVT.setText(jsonRoot.getString(UID_TITTLE));
			m1VL.setText(jsonRoot.getString(M1_TITTLE));
			m2VL.setText(jsonRoot.getString(M2_TITTLE));
			m3VL.setText(jsonRoot.getString(M3_TITTLE));
			m4VL.setText(jsonRoot.getString(M4_TITTLE));
			m5VL.setText(jsonRoot.getString(M5_TITTLE));
			k1VL.setText(jsonRoot.getString(K1_TITTLE));
			k2VL.setText(jsonRoot.getString(K2_TITTLE));
			k3VL.setText(jsonRoot.getString(K3_TITTLE));
			k4VL.setText(jsonRoot.getString(K4_TITTLE));
        } catch (IOException e) {
            e.printStackTrace();
        }
		super.importConfigFile();
	}

	private void calcMK() {
		boolean res = checkFormValid();
		if(!res) {
			adaptor.printErrorMsg(Messages.KeyGenerateDialog_CheckFormFailedInfo);
			return;
		}
		res &= checkUID();
		if(!res) {
			adaptor.printErrorMsg("check UID failed."); //$NON-NLS-1$
			return;
		}
		String authKeyId = Key.getKey(authKeyC.getText()).getKeyID();
		String newKeyId =  Key.getKey(newKeyC.getText()).getKeyID();
		String authKey = authKeyVT.getText();
		String newKey = newKeyVT.getText();
		String fidBin = (String)setFIDButton.getData(FIDConfigDialog.DATA_KEY_FID);
		String cid = CIDT.getText();
		String uid = uidVT.getText();

		// calc k1
		short[] k1Arr = new short[16];
		adaptor.KDF(FormatUtil.hexStr2ShortArr(authKey), KEY_UPDATE_ENC_C, k1Arr);
		String K1 = FormatUtil.shortArrToHexStr(k1Arr);
		k1VL.setText(K1);
		// calc K2
		short[] k2Arr = new short[16];
		adaptor.KDF(FormatUtil.hexStr2ShortArr(authKey), KEY_UPDATE_MAC_C, k2Arr);
		String K2 = FormatUtil.shortArrToHexStr(k2Arr);
		k2VL.setText(K2);
		// calc K3
		short[] k3Arr = new short[16];
		adaptor.KDF(FormatUtil.hexStr2ShortArr(newKey), KEY_UPDATE_ENC_C, k3Arr);
		String K3 = FormatUtil.shortArrToHexStr(k3Arr);
		k3VL.setText(K3);
		// calc K4
		short[] k4Arr = new short[16];
		adaptor.KDF(FormatUtil.hexStr2ShortArr(newKey), KEY_UPDATE_MAC_C, k4Arr);
		String K4 = FormatUtil.shortArrToHexStr(k4Arr);
		k4VL.setText(K4);
		// calc M1
		String M1 = uid + newKeyId.substring(newKeyId.length()-1) + authKeyId.substring(authKeyId.length()-1);
		short[] m1Arr = FormatUtil.hexStr2ShortArr(M1);
		m1VL.setText(M1);
		// calc M2
		short[] m2Arr = new short[32];
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append(FormatUtil.hexString2binaryString(cid) + fidBin.substring(fidBin.length()-6));
		for(int i=0;i<94;i++) {
			sBuilder.append("0"); //$NON-NLS-1$
		}
		String m2Plain = FormatUtil.binaryStr2HexStr(sBuilder.toString()) + newKey;
		adaptor.CBC(FormatUtil.hexStr2ShortArr(m2Plain), k1Arr, null, 32, m2Arr);
		m2VL.setText(FormatUtil.shortArrToHexStr(m2Arr));
		// calc M3
		short[] m3PlainArr = concatenateArrays(m1Arr, m2Arr);
		short[] m3Arr = new short[16];
		adaptor.aes_cmac(m3PlainArr, 48, m3Arr, k2Arr);
		m3VL.setText(FormatUtil.shortArrToHexStr(m3Arr));
		// calc M4
		short[] m4ArrLast16 = new short[16];
		sBuilder = new StringBuilder();
		sBuilder.append(FormatUtil.hexString2binaryString(cid));
		sBuilder.append("1"); //$NON-NLS-1$
		for(int i=0;i<99;i++) {
			sBuilder.append("0"); //$NON-NLS-1$
		}
		String m4Plain = FormatUtil.binaryStr2HexStr(sBuilder.toString());
		adaptor.aes_128_encrypt(FormatUtil.hexStr2ShortArr(m4Plain), m4ArrLast16, k3Arr);
		short[] m4Arr = concatenateArrays(FormatUtil.hexStr2ShortArr(M1), m4ArrLast16);
		m4VL.setText(M1 + FormatUtil.shortArrToHexStr(m4Arr));
		// calc M5
		short[] m5PlainArr = m4Arr;
		short[] m5Arr = new short[16];
		adaptor.aes_cmac(m5PlainArr, 32, m5Arr, k4Arr);
		m5VL.setText(FormatUtil.shortArrToHexStr(m5Arr));
		adaptor.printInfoMsg(Messages.UserKeyUpdateDialog_46);
	}
	
	public short[] concatenateArrays(short[] arr1, short[] arr2) {
		short[] result = new short[arr1.length + arr2.length];
	    
	    System.arraycopy(arr1, 0, result, 0, arr1.length);
	    System.arraycopy(arr2, 0, result, arr1.length, arr2.length);
	    
	    return result;
	}

	@Override
	public void modifyText(ModifyEvent e) {
		handleChanged();
	}
	
	@Override
	public void focusGained(FocusEvent e) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void focusLost(FocusEvent e) {
		Widget widget = e.widget;
		if(widget == authKeyVT && !checkKey(authKeyVT)) {
			authKeyVT.setText(""); //$NON-NLS-1$
		} else if (widget == newKeyVT && !checkKey(newKeyVT)) {
			newKeyVT.setText(""); //$NON-NLS-1$
		} else if(widget == CIDT && !checkCID(CIDT)) {
			CIDT.setText(""); //$NON-NLS-1$
		} else if (widget == uidVT && !checkUID()) {
			uidVT.setText(""); //$NON-NLS-1$
		}
		handleChanged();
		
	}
}
