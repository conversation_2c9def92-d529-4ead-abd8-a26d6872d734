package com.chipon32.configtool.adaptor;

import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
//import org.eclipse.core.runtime.Platform;
import org.eclipse.core.runtime.RegistryFactory;

public class AdaptorCache {
	private static final String ADAPTOR_PLUGIN_ID = "com.chipon32.configtooladaptor.point";
	public static AdaptorCache INSTANCE = new AdaptorCache();
	private ProAndIDEAdaptor adaptor;
	
	public ProAndIDEAdaptor getAdaptor() {
		return adaptor;
	}

	public AdaptorCache() {
//		IExtensionRegistry reg = Platform.getExtensionRegistry();
		IExtensionRegistry reg = RegistryFactory.getRegistry();
		IConfigurationElement[] extensions = reg.getConfigurationElementsFor(ADAPTOR_PLUGIN_ID);
		if(extensions.length<1) {
			return;
		}
		IConfigurationElement element = extensions[0];
		try {
			adaptor = (ProAndIDEAdaptor) element.createExecutableExtension("class");
		} catch (CoreException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
}
