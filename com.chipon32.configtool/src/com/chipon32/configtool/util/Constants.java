package com.chipon32.configtool.util;

public class Constants {
	public static final String SAFE_START_STRICT_MODE = "5AA50000";// 0x0000A55A
	public static final String SAFE_START_NORMAL_MODE = "00000000";// 0x00000000
	
	public static final String DEBUG_CONF_ADDR = "0C003000";
	public static final String DEBUGKEY_ADDR = "0C003020";
	public static final String SAFE_START_CONFIG_ADDR = "0C003800";
	public static final String SAFE_START_CONFIG_LEN_ADDR = "0C003808";
	public static final String SAFE_START_CONFIG_MODE_ADDR = "0C003810";
	public static final String MASTER_ECU_KEY_ADDR = "0C004800";
	public static final String BOOT_MAC_KEY_ADDR = "0C004C00";
	public static final String BOOT_MAC_ADDR = "0C005000";
	public static final String KEY_01_ADDR = "0C005400";
	public static final String KEY_02_ADDR = "0C005800";
	public static final String KEY_03_ADDR = "0C005C00";
	public static final String KEY_04_ADDR = "0C006000";
	public static final String KEY_05_ADDR = "0C006400";
	public static final String KEY_06_ADDR = "0C006800";
	public static final String KEY_07_ADDR = "0C006C00";
	public static final String KEY_08_ADDR = "0C007000";
	public static final String KEY_09_ADDR = "0C007400";
	public static final String KEY_10_ADDR = "0C007800";
	public static final String KEY_11_ADDR = "0C007C00";
	
	public static final String VALIDATOR_KEY = "validator";
	
	public static final int ONE_KB_LEN = 1024;
	public static final int DEBUG_CONF_LEN = 4;
	public static final int DEBUG_KEY_LEN = 16;
	public static final int KEY_LEN = 16;
	public static final int CID_LEN = 4;
	public static final int FID_LEN = 4;

	
	static final String EMPTY_DATA_HEX_CHAR = "0";
	
	// key names
	public static final String MASTER_ECU_KEY = "MASTER_ECU_KEY";
	public static final String BOOT_MAC_KEY = "BOOT_MAC_KEY";
	public static final String KEY_01 = "KEY_01";
	public static final String KEY_02 = "KEY_02";
	public static final String KEY_03 = "KEY_03";
	public static final String KEY_04 = "KEY_04";
	public static final String KEY_05 = "KEY_05";
	public static final String KEY_06 = "KEY_06";
	public static final String KEY_07 = "KEY_07";
	public static final String KEY_08 = "KEY_08";
	public static final String KEY_09 = "KEY_09";
	public static final String KEY_10 = "KEY_10";
	public static final String KEY_11 = "KEY_11";

	// debug mode level
	public static final String DEBUG_MODE_NO = "\u4e0d\u52a0\u5bc6(\u53ef\u4ee5\u8bfb\u64e6\u5199FLASH, \u53ef\u4ee5\u63a5\u5165DEBUG)";
	public static final String DEBUG_MODE_A = "\u52a0\u5bc6A(\u5bc6\u94a5\u53ef\u8bfb\u64e6\u5199FLASH,\u53ef\u63a5\u5165DEBUG)";
	public static final String DEBUG_MODE_B = "\u52a0\u5bc6B(\u5bc6\u94a5\u53ef\u8bfb\u64e6\u5199FLASH,\u65e0\u6cd5\u63a5\u5165DEBUG)";
	public static final String DEBUG_MODE_C = "\u52a0\u5bc6C(\u65e0\u6cd5\u8bfb\u64e6\u5199FLASH,\u65e0\u6cd5\u63a5\u5165DEBUG)";
	
	// key support spliter for user input in ConfigTool
	public static final String KEY_SPERATOR_REGEX = "[ _-]";
}
