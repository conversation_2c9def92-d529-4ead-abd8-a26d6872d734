package com.chipon32.util.xmlparse;

public class PathXP2WIN10 {
	
	static public String shell_escape (String strin)
	{
	//win32系统,针对win10的路径，空格和括号需要使用""引起来，处理后在xp上也是认的，即win10要求更高
	//另外直接字符串的处理，汉字系统是不支持的，编码不同，需要先转换为字节
	// 当前未实现通用，该方法仅管道的C语言上实验成功，暂留
	  byte[]  str=strin.replace("/", "\\").getBytes();
	  byte[]  dbuf=new byte[512];
	  int 	  dbufi=0;
	  int	  stri=0;
	  boolean in_quote = false;
	  
	  for(int i=0;i<128;i++)
		  dbuf[i]=0;
	  
	  //=================================
	 for(stri=0;stri<str.length;stri++)
	    {
			      switch (str[stri])
			        {
			        case '\\':
			        case '"':
				          if (in_quote)
				            {				           
				              dbuf[dbufi++]='"';
				              in_quote = false;
				            }
				          dbuf[dbufi++]='\\';
				          dbuf[dbufi++]=str[stri];				         
				          break;

			        case ' ':
			        case '%':
				          if (!in_quote)
				            {				             
				              dbuf[dbufi++]='"';
				              in_quote = true;
				            }
				          dbuf[dbufi++]=str[stri];
				          break;
						  
			        case '(':
				          if (in_quote)
				            {
				        	  dbuf[dbufi++]='"';
				              in_quote = false;
				            }
				          dbuf[dbufi++]='"';
				          dbuf[dbufi++]=str[stri];
				          dbuf[dbufi++]='"';
				          break;

				case ')':	
				          if (in_quote)
				            {
				        	  dbuf[dbufi++]='"';
				              in_quote = false;
				            }
				          dbuf[dbufi++]='"';
				          dbuf[dbufi++]=str[stri];
			       		  dbuf[dbufi++]='"';
				          break;
			        default:
				          if (in_quote)
				            {
				        	  dbuf[dbufi++]='"';
				              in_quote = false;
				            }
				          dbuf[dbufi++]=str[stri];
			        }			
	    }

	  if (in_quote)
		  dbuf[dbufi++]='"';
	  
	  byte[] result=new byte[dbufi];
	  System.arraycopy(dbuf, 0, result, 0, dbufi);
	  return new String(result);
	}

}
