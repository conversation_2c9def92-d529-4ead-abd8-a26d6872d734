package com.chipon32.util.xmlparse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;  
import java.io.FileInputStream;  
import java.io.FileOutputStream;  
import java.io.IOException;  
import java.io.InputStream;  
import java.io.OutputStream;  
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
  
import javax.swing.JOptionPane;  

  
/** 
 * 复制文件或文件夹
 * 
 */  
public  class CopyFileUtil {  
  
    private static String MESSAGE = "";   //$NON-NLS-1$
    
    public CopyFileUtil()
    {
    	
    }
    /** 
     * 复制单个文件 
     *  
     * @param srcFileName 
     *            待复制的文件名 
     * @param descFileName 
     *            目标文件名 
     * @param overlay 
     *            如果目标文件存在，是否覆盖 
     * @return 如果复制成功返回true，否则返回false 
     */  
    public  boolean copyFile(String srcFileName, String destFileName,  
            boolean overlay) {  
        File srcFile = new File(srcFileName);  
  
        // 判断源文件是否存在  
        if (!srcFile.exists()) {  
            MESSAGE = Messages.CopyFileUtil_1 + srcFileName + Messages.CopyFileUtil_2;  
            JOptionPane.showMessageDialog(null, MESSAGE);  
            return false;  
        } else if (!srcFile.isFile()) {  
            MESSAGE = Messages.CopyFileUtil_3 + srcFileName + Messages.CopyFileUtil_4;  
            JOptionPane.showMessageDialog(null, MESSAGE);  
            return false;  
        }  
  
        // 判断目标文件是否存在  
        File destFile = new File(destFileName);  
        if (destFile.exists()) {  
            // 如果目标文件存在并允许覆盖  
            if (overlay) {  
                // 删除已经存在的目标文件，无论目标文件是目录还是单个文件  
                new File(destFileName).delete();  
            }  
        } else 
        {  
            // 如果目标文件所在目录不存在，则创建目录  
            if (!destFile.getParentFile().exists()) {  
                // 目标文件所在目录不存在  
                if (!destFile.getParentFile().mkdirs()) {  
                    // 复制文件失败：创建目标文件所在目录失败  
                    return false;  
                }  
            }  
        }  
        //--------------------------------------------------------
        // 复制文件  
        nioTransferCopy(srcFile,destFile);
        return true;
    }  
  
    /** 
     * 复制整个目录的内容 
     *  
     * @param srcDirName 
     *            待复制目录的目录名 
     * @param destDirName 
     *            目标目录名 
     * @param overlay 
     *            如果目标目录存在，是否覆盖 
     * @return 如果复制成功返回true，否则返回false 
     */  
    public  boolean copyDirectory(String srcDirName, String destDirName,  
            boolean overlay) {  
        // 判断源目录是否存在  
        File srcDir = new File(srcDirName);  
        if (!srcDir.exists()) {  
            MESSAGE = Messages.CopyFileUtil_5 + srcDirName + Messages.CopyFileUtil_6;  
            JOptionPane.showMessageDialog(null, MESSAGE);  
            return false;  
        } else if (!srcDir.isDirectory()) {  
            MESSAGE = Messages.CopyFileUtil_7 + srcDirName + Messages.CopyFileUtil_8;  
            JOptionPane.showMessageDialog(null, MESSAGE);  
            return false;  
        }  
  
        // 如果目标目录名不是以文件分隔符结尾，则加上文件分隔符  ,  \\
        if (!destDirName.endsWith(File.separator)) {  
            destDirName = destDirName + File.separator;  
        }  
        File destDir = new File(destDirName);  
        // 如果目标文件夹存在  
        if (destDir.exists()) {  
            // 如果允许覆盖则删除已存在的目标目录  
//            if (overlay) {  
//                new File(destDirName).delete();  
//            } else {  
//                MESSAGE = "复制目录失败：目的目录" + destDirName + "已存在！";  
//                JOptionPane.showMessageDialog(null, MESSAGE);  
//                return false;  
//            }  
        } else {  
            // 创建目的目录  
//            System.out.println("目的目录不存在，准备创建。。。");  
            if (!destDir.mkdirs()) {  
            	JOptionPane.showMessageDialog(null,Messages.CopyFileUtil_9);  
                return false;  
            }  
        }  
  
        boolean flag = true;  
        File[] files = srcDir.listFiles();  
        for (int i = 0; i < files.length; i++) {  
            // 复制文件  
            if (files[i].isFile()) 
            {  
            	// 文件
                flag = copyFile(files[i].getAbsolutePath(),  
                        destDirName + files[i].getName(), overlay);  
                if (!flag)  
                    break;  
            } else if (files[i].isDirectory()) 
            {  
            	// 目录
                flag = copyDirectory(files[i].getAbsolutePath(),  
                        destDirName + files[i].getName(), overlay);  
                if (!flag)  
                    break;  
            }  
        }  
        if (!flag) 
        {  
            MESSAGE = Messages.CopyFileUtil_10 + srcDirName + Messages.CopyFileUtil_11 + destDirName + Messages.CopyFileUtil_12;  
            JOptionPane.showMessageDialog(null, MESSAGE);  
            return false;  
        } else 
        {  
            return true;  
        }  
    }  
  // ###############################################################################################
	//=============== 文件到文件的 管道传输，文件越大速度越快
    public  void forTransfer(File f1,File f2) throws Exception{

        int length=2097152; // 单次2M
        FileInputStream in=new FileInputStream(f1);
        FileOutputStream out=new FileOutputStream(f2);
        FileChannel inC=in.getChannel();
        FileChannel outC=out.getChannel();
      
        while(true){
            if(inC.position()==inC.size()){
                inC.close();
                outC.close();
            }
            if((inC.size()-inC.position())<20971520)
                length=(int)(inC.size()-inC.position());
            else
                length=20971520;
            inC.transferTo(inC.position(),length,outC);
            inC.position(inC.position()+length);
        }
    }
    
    // 管道传输，效率最高的，文件大才体现
    public  void nioTransferCopy(File source, File target) {  
	    FileChannel in = null;  
	    FileChannel out = null;  
	    FileInputStream inStream = null;  
	    FileOutputStream outStream = null;  
	    try {  
	        inStream = new FileInputStream(source);  
	        outStream = new FileOutputStream(target);  
	        in = inStream.getChannel();  
	        out = outStream.getChannel();  
	        in.transferTo(0, in.size(), out);  
	    } catch (IOException e) {  
	        e.printStackTrace();  
	    } finally {  
	    	try{
	    		inStream.close();  
	    		in.close();  
	    		outStream.close();  
	    		out.close();  
	    	}
	    	catch (Exception e) {  
	        e.printStackTrace();  
	    	} 
	    }  
	} 
    
    
    // 如果需要监测复制进度，可以用第二快的方法
   	public  void nioBufferCopy(File source, File target) {  
	    FileChannel in = null;  
	    FileChannel out = null;  
	    FileInputStream inStream = null;  
	    FileOutputStream outStream = null;  
	    try {  
	        inStream = new FileInputStream(source);  
	        outStream = new FileOutputStream(target);  
	        in = inStream.getChannel();  
	        out = outStream.getChannel();  
	        ByteBuffer buffer = ByteBuffer.allocate(4096);  
	        while (in.read(buffer) != -1) {  
	            buffer.flip();  
	            out.write(buffer);  
	            buffer.clear();  
	        }  
	    } catch (IOException e) {  
	        e.printStackTrace();  
	    } finally {  
	    	try{
		    	inStream.close();  
		    	in.close();  
		    	outStream.close();  
		    	out.close();  
	    	}
	        catch (Exception e) {  
		        e.printStackTrace();  
		    } 
	    }  
	}  
   	
    // 常用的方法1
	public   void customBufferBufferedStreamCopy(File source, File target) {  
	    InputStream fis = null;  
	    OutputStream fos = null;  
	    try {  
	        fis = new BufferedInputStream(new FileInputStream(source));  
	        fos = new BufferedOutputStream(new FileOutputStream(target));  
	        byte[] buf = new byte[4096];  
	        int i;  
	        while ((i = fis.read(buf)) != -1) {  
	        	fos.write(buf, 0, i);  
		    }  
	    }  
	    catch (Exception e) {  
	        e.printStackTrace();  
	    } finally { 
	    	try {
		    	fis.close();  
		    	fos.close();  
	    	}
	    	catch (Exception e) {  
	    		e.printStackTrace();  
	    	} 
	    }  
	}  
	
    // 常用的方法 2
	public   void customBufferStreamCopy(File source, File target) {  
	    InputStream fis = null;  
	    OutputStream fos = null;  
	    try 
	    {  
	        fis = new FileInputStream(source);  
	        fos = new FileOutputStream(target);  
	        byte[] buf = new byte[4096];  
	        int i;  
	        while ((i = fis.read(buf)) != -1) {  
		            fos.write(buf, 0, i);  
		    }  
		}  
	    catch (Exception e) {  
	        e.printStackTrace();  
	    } 
    	finally 
    	{  
    		try{
		    	fis.close();  	    		      
		        fos.close(); 
    		}catch (Exception e) {  
		        e.printStackTrace();  
		    } 
	    }  
	}
	
	
    // 古老方法，效率还是不错的
	public void forJava(File f1,File f2) throws Exception
    {
	  int length=2097152;
	  FileInputStream in=new FileInputStream(f1);
	  FileOutputStream out=new FileOutputStream(f2);
	  byte[] buffer=new byte[length];
	  while(true)
	  {
    	   int ins=in.read(buffer);
    	   if(ins==-1)
    	   {
	    	    in.close();
	    	    out.flush();
	    	    out.close();
    	   	}
    	   else
    	   {
    	    	out.write(buffer,0,ins);
    	   }
	  }
   }
	


}  
