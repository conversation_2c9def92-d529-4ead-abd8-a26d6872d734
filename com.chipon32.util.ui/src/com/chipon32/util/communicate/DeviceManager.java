package com.chipon32.util.communicate;

//import gnu.io.CommPort; 
//import gnu.io.CommPortIdentifier;
//import gnu.io.NoSuchPortException;
//import gnu.io.PortInUseException;
//import gnu.io.SerialPort;
//import gnu.io.UnsupportedCommOperationException;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.core.runtime.Platform;

import com.chipon32.hex.core.util.ByteConvertor;
import com.chipon32.util.frame.Frame;
import com.chipon32.util.frame.IFrame;
import com.fazecast.jSerialComm.SerialPort;

// 注意 这个是串口的操作类，不能建立类对象，全部为静态的直接使用
// 测试使用库时 中间异常了，使用源码下可以获取
public class DeviceManager {
	// 编程模式标识
	public static boolean isProModeICSP = true; //IDE只保留ICSP编程模式
//	public static boolean isProModeSWD=false;
//	public static boolean isProModeISP=false;
	public static boolean Do_bluetooth_Filter=true;
	//###############################################################################
    private static DeviceManager serialTool = null;

    //私有化SerialTool类的构造方法，不允许其他类生成SerialTool对象
    private DeviceManager() {} 
    
	/**  Get the unique instance of this class.	 */
    public  static DeviceManager getUniqueInstance() {
		if (serialTool == null) {
			serialTool = new DeviceManager();
		}
		return serialTool;
    }
	
	@SuppressWarnings("unused")
	private static boolean isWindows() {
		return (Platform.getOS().equals(Platform.OS_WIN32));
	}
	//############################################################################

	public static boolean isFindSetFlag=false;
	//###########################################################################
	public static int portBaut=230400;
//	public static int portDataBits=SerialPort.DATABITS_8;
//	public static int portStopBits=SerialPort.STOPBITS_1;
//	public static int portStopBitsTry=SerialPort.STOPBITS_2;
//	public static int portParity=SerialPort.PARITY_NONE;	
	public static int portDataBits=8;
	public static int portStopBits=SerialPort.ONE_STOP_BIT;
	public static int portStopBitsTry=SerialPort.ONE_POINT_FIVE_STOP_BITS;
	public static int portParity=SerialPort.NO_PARITY;
	
	
	public static long timeoutset=1000;
	public static long ISP_TIME_OUT_VALUE=2000;  ;// 配置的超时时长支持 "1秒","2秒","5秒","8秒","10秒","15秒","20秒","30秒"
	public static int inputStreamSize=4096;
	public static int outputStreamSize=4096;
	//###########################################################################	
	public static String portName = null;	// 获取到设备的名存贮，后续基于名建立SerialPort，否则SerialPort存在下的打开名被占用
	public static List<String> commList = null;			//所有的串口设备列表
	public static List<String> proNameList=null;  		//属于调试编程器的设备列表
	
	private static SerialPort serialPort = null;			//保存串口对象
	public static InputStream in = null;
	public static OutputStream out = null;
	//###########################################################################
	// 获取设备后的设备版本信息
	public static String VarMess1	="00";
	public static String VarMess2	="00";
	public static String VarMess3	="00";
	public static String VarMess4	="00";
	public static String VarMess5	="00";
	public static String VarMess6	="00";
	//###########################################################################
	/**
	 *  是否能真正的串口，否则是KF的编程调试器，但可以切换到串口
	 */
	public static boolean PortSetType=false;  
	//##########################################################################
	
    static {
        //在该类被ClassLoader加载时就初始化一个SerialTool对象
    	try{
	        if (serialTool == null) {
	            serialTool = new DeviceManager();
		        //################    
		        /*getDevice();
		        //################
		        if(isProModeISP) {     	
		            if(commList!=null && commList.size()>0){
		            	portName=commList.get(0);
		            } else {       		
		            	portName="COM1";          	
		            }            
		         } else {
		            if(proNameList!=null && proNameList.size()>0) {
		            	if(proNameList.size() > 1) {
		            		Display.getDefault().syncExec(new Runnable() {
		            			@Override
		            			public void run() {
		            				new PortSelectDialog(Display.getDefault().getActiveShell()).open();
		            					
		            			}
		            		});
		            	}else {
		            		portName=proNameList.get(0);
		            	}
		            }
		         }*/
	        }
	        
    	}catch (Exception e) {
    		e.printStackTrace();
		}
    }
  //##########################################################################
		/** 
		 * 是否应经测试好存在的设备了。有的话直接对象使用	否则要重新获取	 * 
		 */
		public static boolean hasDevice()   
		{			
			if(serialPort!=null)
				return true;
			else
				return false;
		}
//###############################################################################
		public static List<String> getExistALLPortList()		{	
			return commList;
		}
		public static List<String> getExistALLProList()		{	
			return proNameList;
		}
		public static List<String> getALLPortList()		{							
			getDevice();			
			return commList;
		}
		public static List<String> getALLProList()		{							
			//getDevice();		  均	先调用getALLPortList后的不额外调用1次了。
			return proNameList;
		}		
		public static void SelDevice(String name)		{			
			portName=name;
		}
		//###############################################################################		
		/**
		 * 设备信息获取和判断是否为可操作设备	
		 * @return
		 */
		public static String getDeviceVer() {
			//判断是否为可操作设备
			DeviceManager.DriverExitUsart();
			try {
				Thread.sleep(16);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			
			byte[] buffer = {
					ByteConvertor.hexToByte("68"),					ByteConvertor.hexToByte("E4"),
					ByteConvertor.hexToByte("00"),					ByteConvertor.hexToByte("00"),
					ByteConvertor.hexToByte("4C"),					ByteConvertor.hexToByte("B3"),
					ByteConvertor.hexToByte("16"),
			};
			write(buffer);
			byte[] revdate=new byte[13];
			read(revdate);						
			Frame frame = new Frame(revdate);
			if (frame.isSuccess()) 
			{
				VarMess1=frame.getData().get(0);
				VarMess2=frame.getData().get(1);
				VarMess3=frame.getData().get(2);
				VarMess4=frame.getData().get(3);
				VarMess5=frame.getData().get(4);
				VarMess6=frame.getData().get(5);
//				System.out.println("Ver:"+VarMess1+"-"+VarMess2+"-"+VarMess3+"-"+VarMess4+"-"+VarMess5+"-"+VarMess6);
				return "Ver:"
						+VarMess1+"-"
						+VarMess2+"-"
						+VarMess3+"-"
						+VarMess4+"-"
						+VarMess5+"-"
						+VarMess6;
			}else {
				return null;
			}
		}

		//设计的编程调试设备默认无作为工作控制状态	
		public static boolean DriverSetRst()
		{
			// 设备信息获取和判断是否为可操作设备
			byte[] buffer = {
					ByteConvertor.hexToByte("68"),					ByteConvertor.hexToByte("F0"),
					ByteConvertor.hexToByte("00"),					ByteConvertor.hexToByte("00"),
					ByteConvertor.hexToByte("58"),					ByteConvertor.hexToByte("A7"),
					ByteConvertor.hexToByte("16"),
			};
			write(buffer);
			byte[] revdate=new byte[7];
			read(revdate);						
			Frame frame = new Frame(revdate);			
			if (frame.isSuccess()) 
			{
				return  true;
			}
			else {
				return false;
			}
		}	
		
		public static boolean RunONControl(boolean isOpen) {
		
			byte[] buffer = new byte[7];
			// 包头，类型
			buffer[0] = ByteConvertor.hexToByte(IFrame.FRAME_HEAD);
			if(isOpen)
				buffer[1] = ByteConvertor.hexToByte(IFrame.POWER_ON_RUN);
			else
				buffer[1] = ByteConvertor.hexToByte(IFrame.POWER_DOWN_RUN);
			// 数据长度
			buffer[2] = ByteConvertor.hexToByte(Integer.toHexString((buffer.length-7 & 0xFF )));
			buffer[3] = ByteConvertor.hexToByte(Integer.toHexString(((buffer.length-7 & 0xFF00)/256 )));
			// 数据
			// 校验，包尾
			buffer[buffer.length - 3] = ByteConvertor.format(buffer);
			buffer[buffer.length - 2] = (byte) (-buffer[buffer.length - 3] - 1);
			buffer[buffer.length - 1] = ByteConvertor.hexToByte(IFrame.FRAME_TAIL);
			// 帧生成，发送，接收返回帧
			IFrame frame = new Frame(buffer);
			Frame received = frame.sendFrame();
			// 返回帧结果解析
			if (received.isSuccess()) {			
				return true;
			}
			WriteMessage.getDefault().writeErrMessage(received.handleError());
			return false;
		}
		
		public static boolean DriverExitUsart() {
			// 设备信息获取和判断是否为可操作设备
			byte[] buffer = {
					ByteConvertor.hexToByte("AA"),	ByteConvertor.hexToByte("55"),
					ByteConvertor.hexToByte("55"),	ByteConvertor.hexToByte("AA"),
					ByteConvertor.hexToByte("FE"),	ByteConvertor.hexToByte("01"),
			};
			write(buffer);	
			return  true;			
		}	
		
		/**
		 * 测试设备是否工作在脱机状态
		 * @return
		 */
		public static boolean DriverIsOffline()
		{
			// 设备信息获取和判断是否为可操作设备
			byte[] buffer = {
					ByteConvertor.hexToByte("68"),					ByteConvertor.hexToByte("E1"),
					ByteConvertor.hexToByte("00"),					ByteConvertor.hexToByte("00"),
					ByteConvertor.hexToByte("49"),					ByteConvertor.hexToByte("B6"),
					ByteConvertor.hexToByte("16"),
			};
			write(buffer);
			byte[] revdate=new byte[8];
			read(revdate);						
			Frame frame = new Frame(revdate);	
			if (frame.isSuccess()) 
			{
				if(frame.getData().get(0)=="00")
					return false;
				else
					// 脱机模式
					return  true;
			}else {// 可能为设计该功能，不具有脱机功能考虑的返回真
				return false;
			}
		}
		
		/**
		 *  遍历获取设备名称列表，基于列表进行实验是否对
		 *  @return 
		 */
		public static Device getDevice() {		
			SerialPort serialPortOK=null;
			// 更新前处理			
			if(serialPort!=null)
			{					
				try{
					close();
				}
				catch (Exception e) {			
					in=null;out=null;serialPort=null;
				}				
			}		
			setReadTimeout(100);
			portName=null;
			isFindSetFlag=false;			
			proNameList=new ArrayList<>();
			try{
				commList  = findPort();// 操作串口期间拔出设备再次链接，识别不到设备，需要检测一下后再次拔出和链接，测试上位机未检测到com口，上位机退出后，串口软件仍打不开串口，指向：串口设备驱动（ST)时存在异常。
			}catch (Exception e) {			
			}
			//######################################timeout 与 futher 不能解决本地write无应答问题的，外部程序过滤设备
			//过滤蓝牙端口
			List<String> proNameListFilter = new ArrayList<>();
			if(Do_bluetooth_Filter == true ) {
				proNameListFilter = FilterPortLikeBluetooth.filter();
			}
			if(proNameListFilter.size()>0) {
				if(commList != null && commList.size() > 0) { 
					  commList.removeAll(proNameListFilter); 
				} 
			}
			 		
			//######################################
			if(commList != null && commList.size() > 0){
				for(String pname : commList){
					try{						
						serialPort=openPort(pname, 100);
						if(serialPort==null)
							continue;
						// 进程小延时
						try {
							Thread.sleep(20);
						} catch (InterruptedException e) {							
						}
	                    in=serialPort.getInputStream();
	                    out=serialPort.getOutputStream();
						// 设备信息获取和判断是否为可操作设备准备，因为可能化身串口的退出发送,getDeviceVer 方法中集成了退出
//						byte[] buffer = {
//								ByteConvertor.hexToByte("AA"),
//								ByteConvertor.hexToByte("55"),
//								ByteConvertor.hexToByte("55"),
//								ByteConvertor.hexToByte("AA"),
//								ByteConvertor.hexToByte("FE"),
//								ByteConvertor.hexToByte("01")	
//						};								
//						write(buffer);
//						Thread.sleep(16);
						// 设备信息获取和判断是否为可操作设备	
						if (getDeviceVer()!=null) {	
							// 记录首个对象
							if(portName==null)
							{							
								portName=pname; 								
								serialPortOK=serialPort;
							}								
							proNameList.add(pname);
							isFindSetFlag=true;			
							close();
							
						}else {	
							close();
							continue;
						}
						
					}catch (Exception e) {	
						//System.out.println("设备操作失败！" );
						e.printStackTrace();
						continue;
					}
				}
			}
			//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			if(isFindSetFlag==true){				
				serialPort=serialPortOK;  
				//Device.DevicePortName=serialPort.getName();
				Device.DevicePortName=serialPort.getPortDescription();
				Device.isDeviceExist=true;
				return Device.getUniqueInstance();
				
			}else{
				Device.DevicePortName="";
				Device.isDeviceExist=false;				
				return  Device.getUniqueInstance();
			}
			//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		}
//#########################################################################	
		
		/**
		 * 查找所有可用端口
		 * @return 可用端口名称列表
		 */
		@SuppressWarnings("unchecked")
		public static final ArrayList<String> findPort() {
			try{
				//获得当前所有可用串口
//		        Enumeration<CommPortIdentifier> portList = CommPortIdentifier.getPortIdentifiers();	
//		        
//		        ArrayList<String> portNameList = new ArrayList<>();
//	
//		        //将可用串口名添加到List并返回该List
//		        while (portList.hasMoreElements()) {
//		            String portName = portList.nextElement().getName();
//		            portNameList.add(portName);
//		        }
//		        return portNameList;
				SerialPort[] portList = SerialPort.getCommPorts();
				List<String> portNameList = new ArrayList<>();
				for(SerialPort port: portList) {
					 String portName = port.getSystemPortName();
					 portNameList.add(portName);
				}
				portNameList = portNameList.stream().distinct().collect(Collectors.toList());
				
				return (ArrayList<String>) portNameList;
		        
			}catch (Exception e) {
				e.printStackTrace();
				return null;
			}
	    }
		
//#########################################################################			
		public static long getTimeout()
		{
			return timeoutset;
		}
		public static void setReadTimeout(long timeset)
		{
			timeoutset=timeset;
		}
		public static void setTimeout(long timeset)
		{
			timeoutset=timeset;
		}
		
	    /**
	     * 打开串口  测试是否可用的打开
	     * @param portName 端口名称
	     * @param 
	     * @return 串口对象
	     * @throws SerialPortParameterFailure 设置串口参数失败
	     * @throws NotASerialPort 端口指向设备不是串口类型
	     * @throws NoSuchPort 没有该端口对应的串口设备
	     * @throws PortInUse 端口已被占用
	     */
	    public static final SerialPort openPort(String portName, int timeout)/*throws UnsupportedCommOperationException, PortInUseException, NoSuchPortException*/   {
	    	try {      
	    	//通过端口名识别端口
//	            CommPortIdentifier portIdentifier = CommPortIdentifier.getPortIdentifier(portName);
//	            //打开端口，并给端口名字和一个timeout（打开操作的超时时间）
//	            CommPort commPort = portIdentifier.open(portName, timeout);
	            SerialPort commPort = SerialPort.getCommPort(portName);
	            boolean succ = commPort.openPort(100);
	            if(!succ) {
	            	return null;
	            }
	           //判断是不是串口
	            if (commPort instanceof SerialPort) {
	            	
	                SerialPort serialPort = (SerialPort) commPort; 	                
	                //System.out.println("Open " + portName + " sucessfully !");
	                return serialPort;	            
	            }        
	            else {	            	
	            	return null;
	            }
	            
	    	}
			catch (Exception e) {
				// TODO: handle exception
				return null;
			}
	    }
//#########################################################################		
		public static final boolean setparams()
		{
	        try {                    	
	            //设置一下串口的波特率等参数
//	            serialPort.setSerialPortParams(portBaut, portDataBits, portStopBits, portParity);    
	        	serialPort.setComPortParameters(portBaut, portDataBits, portStopBits, portParity);    
			} catch (/* UnsupportedCommOperation */Exception e) {  
	        	return false;
	        }
	        return true;
		}	
		
		public static final boolean setparams(int baudrate,int datelen,int stoplen,int parityvaule)
		{
	        try {                    	
	            //设置一下串口的波特率等参数
//	            serialPort.setSerialPortParams(baudrate, datelen, stoplen, parityvaule);   
	        	serialPort.setComPortParameters(baudrate, datelen, stoplen, parityvaule);  
			} catch (/* UnsupportedCommOperation */Exception e) {  
	        	return false;
	        }	       
	        return true;
		}
		
//#########################################################################		
		public static final boolean open() {
	        try {		        	
	            //通过端口名识别端口
//	            CommPortIdentifier portIdentifier = CommPortIdentifier.getPortIdentifier(portName);
	            //打开端口，并给端口名字和一个timeout（打开操作的超时时间）
//	            CommPort commPort = portIdentifier.open(portName, 100);
	            SerialPort commPort = SerialPort.getCommPort(portName);
	            boolean succ = commPort.openPort(0, outputStreamSize, inputStreamSize);
	            if(!succ) {
	            	return false;
	            }
	            //判断是不是串口
	            if (commPort instanceof SerialPort) {
	            	
	            	serialPort = (SerialPort) commPort;                
	                try {                    	
	                    //设置波特率外的其他参数
//	                    serialPort.setSerialPortParams(portBaut, portDataBits, portStopBits, portParity);  
//	                    serialPort.setDTR(true);
//	                    serialPort.setRTS(true);
//	                    serialPort.setInputBufferSize(inputStreamSize);
//	                    serialPort.setOutputBufferSize(outputStreamSize);
	                    
	                    serialPort.setComPortParameters(portBaut, portDataBits, portStopBits, portParity);
	                    serialPort.setDTR();
	                    serialPort.setRTS();
	                    in=serialPort.getInputStream();
	                    out=serialPort.getOutputStream();
	                    Device.isWorkState=true;		                   
//		                    addListener(new SerialListener());
	                    return true;
					} catch ( /* IOException | UnsupportedCommOperation */Exception e) {  
	                	Device.isWorkState=false;
	                }		                
	                //System.out.println("Open " + portName + " sucessfully !");
	                return false;       
	            }        
	            else {
	            	//不是串口
	            	return false;
	            }
			} catch (/* NoSuchPort */Exception e1) {
	        	//System.out.println("不存在这样一个串口！" );
	        	Device.isWorkState=false;
	        	return false;
	        }/* catch (PortInUseException e2) {
	        	//System.out.println("该串口已经被打开了！" );
	        	Device.isWorkState=false;
	        	return false;
	        }*/
	       
	    }		
//#########################################################################		
		/**
		 * 关闭串口
		 * @param serialport 待关闭的串口对象
		*/
		public static boolean close() {
	    	// 流资源释放
			  try {
				  if(in!=null)	{
					  in.close();
					  in = null;
					}					
				} catch (IOException e1) {						
					in = null;
				}				
              try {
            	  if(out!=null)
            	  {
					out.close();			
					out = null;
            	  }
				} catch (IOException e1) {					
					out = null;
				}
            // 状态更新
	    	Device.isWorkState=false;
			// 串口自身的关闭
			try{
		    	if (serialPort != null) 
		    	{
//					serialPort.removeEventListener();						
//		            serialPort.setDTR(false);      
//		            serialPort.setRTS(false);
//		    		serialPort.close();	
		    		serialPort.clearDTR();      
		    		serialPort.clearRTS();
		    		serialPort.closePort();	
		    		serialPort=null;
		    	}
			 } catch(Exception e1) {
		    		serialPort=null;
			 }				    	
	    	return true;
	    }

//#########################################################################		    
//#########################################################################	
			public static void write(int b)   {

				write(new byte[] { (byte) b });
			}
			
			public static void write(byte[] buffer)   {
			 	
		        try {        	
//		        	out = serialPort.getOutputStream();
		            out.write(buffer);
		            out.flush();  
//		            out.close();         		            
		        } catch (IOException e) {
		        	e.printStackTrace();
		        } 
			}
			
			public static  void write(byte[] buffer, int offset, int length)  {
		    	
		    	byte[] writedates=new byte[length-offset];
		    	for(int i=offset;i<length;i++)
		    	{
		    		writedates[i-offset]=buffer[i];
		    	}
		        try {
//		        	out = serialPort.getOutputStream();
		            out.write(writedates);
		            out.flush();
//		            out.close(); 
		        } catch (IOException e) {
		        	
		        } 
			}
		//#########################################################################		    
		//#########################################################################		
		   public static int read()   {

				byte buffer[] = { -1 }; // if nothing was read -1 will be returned.
				read(buffer);

				return buffer[0];
			}	
		   
			public static int read(byte[] buffer)   {
				return read(buffer, 0, buffer.length);
			}
			
			public static  int read(byte[] buffer, int offset, int length)			  {
				
				int  edgereadneedlen=length-offset;  //还剩余的需求长度
				int  offsetstart=offset;			 // 还需要的数据存贮起始偏移
				
				if(serialPort==null)
					return  -1;
				// 数量不满足时继续进行读取				
				long starttime=System.currentTimeMillis();
				//System.out.print(starttime+"\t 起始时间 \n");
		        byte[] bytes = null;		
//		        in = null;
		        //--------------------------------
		        try {		        	
//		        	in = serialPort.getInputStream();
		        	while(edgereadneedlen>0)
		        	{   		
		        		//=======================================================
				        	int bufflenth = 0;		//获取buffer里的数据长度
				        	while(	(bufflenth = in.available())	==0	)
				        	{	// 超时监控
				        		long watchtime=System.currentTimeMillis();
				        		if(watchtime>starttime+timeoutset)
				        		{						        		
				        			//System.out.print("\t 读取超时了 \n");
				        			in.close();
				        			return -1;
				        		}
				        		// 既然读应该能收到，一个超时失败的卡段就卡顿了，不用休眠让其他进程执行					        		
				        	}
				        //======================================================
				        	if(edgereadneedlen>bufflenth)
				        	{
				        		bytes = new byte[bufflenth];	
				        		in.read(bytes);
				        		for(int b=0;b<bufflenth;b++)
				        		{
				        			buffer[b+offsetstart]=bytes[b];
				        		}
				        		offsetstart+=bufflenth;
				        		edgereadneedlen-=bufflenth;
				        	}
				        	else
				        	{
				        		bytes = new byte[edgereadneedlen];	
				        		in.read(bytes);
				        		for(int b=0;b<edgereadneedlen;b++)
				        		{
				        			buffer[b+offsetstart]=bytes[b];
				        		}				        		
				        		edgereadneedlen=0;
				        	}
		        	}
		        	//
		        } catch (Exception e) {
		        	
		        } finally {
//		            if (in != null) 
//		            {
//		                try {
//							in.close();
//						} catch (IOException e1) {
//							// TODO Auto-generated catch block
//							e1.printStackTrace();
//						}
//		                in = null;
//		            }
		        }
		        //----------------------结果判断
		        if(edgereadneedlen>0)
		        {
		        	return -1;
		        }
		        else
		        {
		        	return 1;
		        }
			}
			//#########################################################################		    
			//#########################################################################	
			public static  void cleanreadbuf()
			{		    	
		        byte[] bytes = null; // 进行读出，但结果丢弃
		        try {
//		        	if(in==null)
//		        		in = serialPort.getInputStream();
		        	int bufflenth = in.available();		//获取buffer里的数据长度
		            
		        	while (bufflenth != 0) {                             
		                bytes = new byte[bufflenth];	//初始化byte数组为buffer中数据的长度
		                in.read(bytes);
		                bufflenth = in.available();
		        	} 
		        } catch (IOException e) {		        	
		        } 				
			}
			
			public static  void cleanwritebuf(){
		      try {
				out.flush();
				} catch (IOException e) {
				}
			}
			
			public static void setDTR(boolean istrue)
			{		
				try{
//					if( serialPort!=null &&  Device.isWorkState)
//						serialPort.setDTR(istrue);
					if( serialPort!=null &&  Device.isWorkState) {
						if(istrue) {
							serialPort.setDTR();
						} else {
							serialPort.clearDTR();
						}
					}
				} catch (Exception e) {
				}
			}
			public static void setRTS(boolean istrue)
			{
				try{
//					if( serialPort!=null && Device.isWorkState)
//						serialPort.setRTS(istrue);
					if( serialPort!=null &&  Device.isWorkState) {
						if(istrue) {
							serialPort.setRTS();
						} else {
							serialPort.clearRTS();
						}
					}
				}catch (Exception e) {
				}
			}	

//#########################################################################		
//			public static void addListener( SerialPortEventListener listener)
//			{
//		        try {
//		        	
//		            //给串口添加监听器
//		        	serialPort.addEventListener(listener);
//		            //设置当有数据到达时唤醒监听接收线程
//		        	//serialPort.notifyOnDataAvailable(true);
//		           //设置当通信中断时唤醒中断线程
//		        	serialPort.notifyOnBreakInterrupt(true);
//		        	serialPort.notifyOnOverrunError(true);
//		        	
//		        } catch (TooManyListenersException e) {
//		        	
//		        }
//			}
//		    public static void addListener(SerialPort port, SerialPortEventListener listener)   {
//
//		        try {
//		        	
//		            //给串口添加监听器
//		            port.addEventListener(listener);
//		            //设置当有数据到达时唤醒监听接收线程
//		            //port.notifyOnDataAvailable(true);
//		            //设置当通信中断时唤醒中断线程
//		            port.notifyOnBreakInterrupt(true);
//
//		        } catch (TooManyListenersException e) {
//		        	
//		        }
//		    }
//#########################################################################		
		    /**
			 * 以内部类形式创建一个串口监听类.
			 * <AUTHOR> 使用方法 //Device.addListener(serialPort, new SerialListener());
			 */
//			@SuppressWarnings("unused")
//			private static  class SerialListener implements SerialPortEventListener {				
//				
//				 //处理监控到的串口事件				 
//			    public void serialEvent(SerialPortEvent serialPortEvent) {
//			    	
//			        switch (serialPortEvent.getEventType()) {
//
//			            case SerialPortEvent.BI: // 10 通讯中断	
//			            	DeviceManager.close();
//			            	serialPort=null;
//			            	JOptionPane.showMessageDialog(null, "与串口设备通讯中断", "错误", JOptionPane.INFORMATION_MESSAGE);
//			            	break;
//			            case SerialPortEvent.OE: // 7 溢位（溢出）错误
//
//			            case SerialPortEvent.FE: // 9 帧错误
//
//			            case SerialPortEvent.PE: // 8 奇偶校验错误
//
//			            case SerialPortEvent.CD: // 6 载波检测
//
//			            case SerialPortEvent.CTS: // 3 清除待发送数据
//
//			            case SerialPortEvent.DSR: // 4 待发送数据准备好了
//
//			            case SerialPortEvent.RI: // 5 振铃指示
//
//			            case SerialPortEvent.OUTPUT_BUFFER_EMPTY: // 2 输出缓冲区已清空
//			            	break;
//			            
//			            case SerialPortEvent.DATA_AVAILABLE: // 1 串口存在可用数据									            
//							break;			
//			        }
//			    }
//			}   
//#########################################################################					

	}
