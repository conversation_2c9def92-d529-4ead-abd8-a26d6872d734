package com.chipon32.util.communicate;

import java.text.SimpleDateFormat;

import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.widgets.Display;

import com.chipon32.util.ui.UiUtilActivator;

public class WriteMessage {
	// 类自身，也就是实现唯一定义变量
	public static WriteMessage wm;
	// 简单日期格式  年月日 时分秒
	public static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS ");	 //$NON-NLS-1$

	// 时间获取当时信息的字符串表示
	public String time=""; //$NON-NLS-1$
	
	// 自身的如果每构建进行构建，否则返回自身
	public static WriteMessage getDefault(){
		if(wm == null){
			wm = new WriteMessage();
		}
		return wm;
		
	}
	//--------------------------------------------------------
	private WriteMessage(){
		// 空的构造	
		
	}
	
	
	// 芯片软id规则基于型号软id解析出型号选择信息
	public  String GuessChipSel(String idIN)
	{
		if(idIN==null || idIN.equalsIgnoreCase("")) //$NON-NLS-1$
		{
			return "UnKnow"; //$NON-NLS-1$
		}
		Long IdNum=Long.parseLong(idIN,16);
		String KF32Name=""; //$NON-NLS-1$
		try{
			Long buf;
			//--------------------------------
			buf=(IdNum&0xF0000000)>>(16+12);
			if(buf==15)
				KF32Name+="KF32F"; //$NON-NLS-1$
			else if(buf==1)
				KF32Name+="KF32L"; //$NON-NLS-1$
			else if(buf==2)
				KF32Name+="KF32LS"; //$NON-NLS-1$
			else if(buf==3)
				KF32Name+="KF32LE"; //$NON-NLS-1$
			else if(buf==13)
				KF32Name+="KF32D"; //$NON-NLS-1$
			else if(buf==11)
				KF32Name+="KF32DL"; //$NON-NLS-1$
			else if(buf==10)
				KF32Name+="KF32A"; //$NON-NLS-1$
			
			else if(buf==9)
				KF32Name+="KF32M"; //$NON-NLS-1$
			else
				return idIN;
			//--------------------------------
			buf=(IdNum&0x0FFF0000)>>(16);
			String namen=Long.toHexString(buf);
			while(namen.length()<3)
				namen="0"+namen; //$NON-NLS-1$
			KF32Name+=namen;
			//--------------------------------
			buf=(IdNum&0xFC00)>>(8);
			
			if(buf==0x10)
				KF32Name+="A"; //$NON-NLS-1$
			else if(buf==0x20)
				KF32Name+="B"; //$NON-NLS-1$
			else if(buf==0x30)
				KF32Name+="C"; //$NON-NLS-1$
			else if(buf==0x40)
				KF32Name+="D"; //$NON-NLS-1$
			else if(buf==0x50)
				KF32Name+="E"; //$NON-NLS-1$
			else if(buf==0x60)
				KF32Name+="F"; //$NON-NLS-1$
			else if(buf==0x70)
				KF32Name+="P"; //$NON-NLS-1$
			else if(buf==0x80)
				KF32Name+="G"; //$NON-NLS-1$
			else if(buf==0x90)
				KF32Name+="H"; //$NON-NLS-1$
			else if(buf==0xA0)
				KF32Name+="I"; //$NON-NLS-1$
			else if(buf==0xB0)
				KF32Name+="J"; //$NON-NLS-1$
			else if(buf==0xC0)
				KF32Name+="K"; //$NON-NLS-1$
			else if(buf==0xD0)
				KF32Name+="L"; //$NON-NLS-1$
			else if(buf==0xE0)
				KF32Name+="M"; //$NON-NLS-1$
			
			else if(buf==0x14)
				KF32Name+="O"; //$NON-NLS-1$			
			else if(buf==0xF0)
				KF32Name+="N"; //$NON-NLS-1$
			else if(buf==0xF4)
				KF32Name+="R"; //$NON-NLS-1$
			else if(buf==0xF8)
				KF32Name+="S"; //$NON-NLS-1$
			
			else
				return idIN;
			//--------------------------------
			buf=(IdNum&0x000F)>>(0);
			if(buf==0)
				KF32Name+="S"; //$NON-NLS-1$
			else if(buf==1)
				KF32Name+="M"; //$NON-NLS-1$
			else if(buf==2)
				KF32Name+="N"; //$NON-NLS-1$
			else if(buf==3)
				KF32Name+="Q"; //$NON-NLS-1$
			else if(buf==4)
				KF32Name+="D"; //$NON-NLS-1$
			else if(buf==5)
				KF32Name+="K"; //$NON-NLS-1$
			else if(buf==6)
				KF32Name+="O"; //$NON-NLS-1$
			else if(buf==7)
				KF32Name+="T"; //$NON-NLS-1$
			else if(buf==8)
				KF32Name+="U"; //$NON-NLS-1$
			else if(buf==9)
				KF32Name+="B"; //$NON-NLS-1$
			else 
				return idIN;
			//--------------------------------
			buf=(IdNum&0x03F0)>>(4);
			if(buf==0x00)
				KF32Name+="A"; //$NON-NLS-1$
			else if(buf==0x01)
				KF32Name+="B"; //$NON-NLS-1$
			else if(buf==0x02)
				KF32Name+="C"; //$NON-NLS-1$
			else if(buf==0x03)
				KF32Name+="D"; //$NON-NLS-1$
			else if(buf==0x04)
				KF32Name+="E"; //$NON-NLS-1$
			else if(buf==0x05)
				KF32Name+="F"; //$NON-NLS-1$
			else if(buf==0x06)
				KF32Name+="G"; //$NON-NLS-1$
			else if(buf==0x07)
				KF32Name+="M"; //$NON-NLS-1$
			else if(buf==0x08)
				KF32Name+="N"; //$NON-NLS-1$
			else if(buf==0x09)
				KF32Name+="P"; //$NON-NLS-1$
			else if(buf==0x0A)
				KF32Name+="R"; //$NON-NLS-1$
			else if(buf==0x0B)
				KF32Name+="S"; //$NON-NLS-1$
			else if(buf==0x0C)
				KF32Name+="K"; //$NON-NLS-1$
			else if(buf==0x0D)
				KF32Name+="T"; //$NON-NLS-1$
			else if(buf==0x0E)
				KF32Name+="U"; //$NON-NLS-1$
			else if(buf==0x0F)
				KF32Name+="V"; //$NON-NLS-1$
			else if(buf==0x10)
				KF32Name+="W"; //$NON-NLS-1$
			else if(buf==0x11)
				KF32Name+="X"; //$NON-NLS-1$
			
			else if(buf==0x12)
				KF32Name+="Y"; //$NON-NLS-1$
			else
				return idIN;
			//--------------------------------
		}catch (Exception e) {
			// TODO: handle exception
		}
		return KF32Name;
	}
	//---一般输出信息函数-----------------------------------------------------
	public void writeMessage(final String message)
	{
		time = sf.format(System.currentTimeMillis()); // 获取当时时间
		System.out.println(time + message);
		// 显示的运行时内容添加
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {   //UI任务
	        	UiUtilActivator.getDefault().writeMessage(time+":"+message); //$NON-NLS-1$
	        }
	    });
	}
	//--输出内容标记为错误的输出信息函数-------------------------------------
	public  void writeErrMessage(final String message)
	{
		time = sf.format(System.currentTimeMillis());// 获取当时时间
		if(message=="") //$NON-NLS-1$
		{
			return;
		}
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {  //UI任务
	        	UiUtilActivator.getDefault().writeErrMessage(time+" "+message); //$NON-NLS-1$
	        }
	    });
	}
	//-- 输出内容标记为错误同时打开错误对话框1的输出信息函数
	public  void writeErrMessageAndOpenErrorDialog(final String message)
	{
		time = sf.format(System.currentTimeMillis());// 获取当时时间
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {   //UI任务
	        	UiUtilActivator.getDefault().writeErrMessage(time+" "+message); //$NON-NLS-1$
	        	MessageDialog.openError(null, Messages.WriteMessage_60, message);
	        }
	    });
	}
	public  void writeErrMessageAndOpenErrorDialog3(final String message)
	{
		time = sf.format(System.currentTimeMillis());// 获取当时时间
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {   //UI任务
	        	UiUtilActivator.getDefault().writeErrMessage(time+" "+message); //$NON-NLS-1$
	        	MessageDialog.openError(null, Messages.IMessages_42_2, message);
	        }
	    });
	}
	//-- 输出内容标记为错误同时打开错误对话框2的输出信息函数
	public  void writeErrMessageAndOpenErrorDialog2(final String message)
	{
		time = sf.format(System.currentTimeMillis());
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {   //UI任务
	        	MessageDialog.openInformation(null, Messages.WriteMessage_61, message);
	        }
	    });
	}
	// --  蓝色文字的输出，同一般输出信息，但字体颜色修改
	public void writeBLUEMessage(final String message)
	{
		System.out.println(WriteMessage.sf.format(System.currentTimeMillis()) + message);
		time = sf.format(System.currentTimeMillis());
		Display.getDefault().asyncExec(new Runnable() {                        
	        @Override
			public void run() 
	        {   //UI任务
	        	UiUtilActivator.getDefault().writeBLUEMessage(time+" "+message); //$NON-NLS-1$
	        }
	    });
	}
}
