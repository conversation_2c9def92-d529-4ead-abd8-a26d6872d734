package com.chipon32.util.communicate;

import java.util.List;


import com.chipon32.hex.core.util.ByteConvertor;

public class CRC_calculate {
	
	
	public static long Crc32Table[]={
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0,0,0,0,	0,0,0,0,	0,0,0,0,	0,0,0,0,
		0
	};

	//################################################################
	public static void outtable32(String initCrcMask)
	{
		//实验代码，生成CRC32表
		Long value;
		Long CRCMASK=Long.parseLong(initCrcMask,16);  // 非 0xedb88320 "edb88320"  "04C11DB7"   ,不然后变为-，并后面也计算错误的
		int i,j;
		for(i=0;i<256;i++)
		{
			value=(long) i;
			for(j=0;j<8;j++)
			{
				if(value%2==1)
				{
					value>>=1;
					value ^= CRCMASK;
				}
				else
				{
					value>>=1;
				}
			}
			//if(i%32==0)
			//	System.out.print("\n");
			String  outv=Long.toHexString(value);				
			outv="000000000"+outv;
			outv=outv.substring(outv.length()-8);
			//System.out.print("0x"+outv+", ");
			//if((i+1)%4==0)
			//	System.out.print("\n");
			
			Crc32Table[i]=Long.parseLong(outv,16);
			//-------------------
		}
	}
	//################################################################
	public static long Crc32Value;
	//查表法的计算crc32的结果
	public static long  getCrcTable(long preCrc32value,List<String>datas)
	{	
		long XORVALUE0XFFFFFFFF=Long.parseLong("FFFFFFFF",16);
		if(Crc32Table[1]==0)
			outtable32("edb88320");
		
//		preCrc32value=preCrc32value^XORVALUE0XFFFFFFFF; //因结果异或，如果数组分多次传入时的过程还原
		int length=datas.size();
	    for (int i = 0; i < length; ++i)
	    {
	    	byte buf= ByteConvertor.hexToByte(datas.get(i));
	    	preCrc32value = (preCrc32value >> 8) ^ Crc32Table[	(int)((preCrc32value ^ buf)& 0xFF)	];
	    	
//	    	System.out.print(i+"-"+buf+":");
//	    	System.out.print(Long.toHexString(preCrc32value));
//	    	System.out.print("\n");
	    	
	    }
	    preCrc32value &=XORVALUE0XFFFFFFFF;
	    return preCrc32value^XORVALUE0XFFFFFFFF;
		
	}
	public static long  getCrcCal(long preCrc32value,List<String>datas)
	{
	
		int length=datas.size();
	    for (int i = 0; i < length; ++i)
	    {
	    	byte buf= ByteConvertor.hexToByte(datas.get(i));
	    	preCrc32value ^= buf;                // crc ^= *data; data++;
	        for (i = 0; i < 8; ++i)
	        {
	            if ((preCrc32value & 1)>0)
	            	preCrc32value = (preCrc32value >> 1) ^ 0xEDB88320;// 0xEDB88320= reverse 0x04C11DB7
	            else
	            	preCrc32value = (preCrc32value >> 1);
	        }
	    }
	    preCrc32value &=Long.parseLong("FFFFFFFF",16);
	    return preCrc32value^Long.parseLong("FFFFFFFF",16);
	
	}
	//################################################################
//uint32_t crc32f(uint8_t *data, uint_len length){
//    uint8_t i;
////    uint32_t crc32 = 0xffffffff;        // Initial value
//    while(length--)
//    {
//    	crc32 ^= *data++;                // crc ^= *data; data++;
//        for (i = 0; i < 8; ++i)
//        {
//            if (crc32 & 1)
//            	crc32 = (crc32 >> 1) ^ 0xEDB88320;// 0xEDB88320= reverse 0x04C11DB7
//            else
//            	crc32 = (crc32 >> 1);
//        }
//    }
//    return ~crc32;}
//
//void GetCRC32Table(uint32_t pCrc32Table[])
//{
//	uint32_t Crc;
//	uint16_t i,j;
//    for(i = 0;i < 256; i++)
//    {
//		Crc = i;
//		for (j = 8; j > 0; j--)
//		{
//			if ((Crc & 1) == 1)
//				Crc = (Crc >> 1) ^ 0xEDB88320;  //0x4C11DB7翻转的结果
//			else
//				Crc >>= 1;
//		}
//		pCrc32Table[i] = Crc;
//   }
//}
//
//uint32_t crc32t(uint8_t *data, uint_len length,uint32_t pCrc32Table[]){
//    uint16_t i;
////    uint32_t crc32 = 0xffffffff;        // Initial value
//    for (i = 0; i < length; ++i)
//    {
//    	crc32 = (crc32 >> 8) ^ pCrc32Table[	(crc32 ^ data[i])& 0xFF	];
//    }
//    return ~crc32;}
// /******************************************************************************
// * Name:    CRC-32/MPEG-2  x32+x26+x23+x22+x16+x12+x11+x10+x8+x7+x5+x4+x2+x+1
// * Poly:    0x4C11DB7
// * Init:    0xffffffff
// * Refin:   False
// * Refout:  False
// * Xorout:  0x0000000
// * Note:
// *****************************************************************************/
//uint32_t crc32f_mpeg_2(uint8_t *data, uint_len length){
//    uint8_t i;
////    uint32_t crc32 = 0xffffffff;  // Initial value
//    while(length--)
//    {
//    	crc32 ^= (uint32_t)(*data++) << 24;// crc ^=(uint32_t)(*data)<<24; data++;
//        for (i = 0; i < 8; ++i)
//        {
//            if ( crc32 & 0x80000000 )
//            	crc32 = (crc32 << 1) ^ 0x04C11DB7;
//            else
//            	crc32 <<= 1;
//        }
//    }
//    return crc32;}	
	
	
	//################################################################
	
	//################################################################

}
