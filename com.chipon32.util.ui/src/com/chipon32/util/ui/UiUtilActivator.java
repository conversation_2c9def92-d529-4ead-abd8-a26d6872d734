package com.chipon32.util.ui;

import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Enumeration;

import org.eclipse.core.runtime.FileLocator;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Path;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.console.ConsolePlugin;
import org.eclipse.ui.console.IConsole;
import org.eclipse.ui.console.IConsoleConstants;
import org.eclipse.ui.console.IConsoleManager;
import org.eclipse.ui.console.IConsoleView;
import org.eclipse.ui.console.MessageConsole;
import org.eclipse.ui.console.MessageConsoleStream;
import org.eclipse.ui.internal.misc.StatusUtil;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.BundleContext;

import com.chipon32.util.communicate.Device;
import com.chipon32.util.communicate.IMessages;


/**
 * The activator class controls the plug-in life cycle 控制插件声明周期
 */
@SuppressWarnings("restriction")
public class UiUtilActivator extends AbstractUIPlugin {

	// The plug-in ID  绑定的项目
	public static final String PLUGIN_ID = "com.chipon32.util.ui"; //$NON-NLS-1$

	// 该类唯一的示例结果
	private static UiUtilActivator plugin;
	// 默认控制台的名字
	private static final String CONSOLE_NAME = IMessages.MESSAGE_CONSOLE_NAME;
	public static boolean isNeedShowConsoleFlag=true;  // UiUtilActivator.isNeedShowConsoleFlag=
	/**
	 * The constructor 空的构造
	 */
	public UiUtilActivator() {
	}

	/*
	 * 启动绑定
	 */
	@Override
	public void start(BundleContext context) throws Exception {
		super.start(context);
		plugin = this;
	}

	/*
	 * 停止注销变量
	 */
	@Override
	public void stop(BundleContext context) throws Exception {
		plugin = null;
		super.stop(context);
	}

	/**
	 * Returns the shared instance 获取自身
	 *
	 * @return the shared instance
	 */
	public static UiUtilActivator getDefault() {
		if(plugin==null)
		{
			plugin=new UiUtilActivator();
		}
		return plugin;
	}
	
	// 调试台信息输出函数
    public static void log(String message) {
        getDefault().getLog().log(StatusUtil.newStatus(IStatus.ERROR, message, null));
    }
    
    // 获取配置文件的芯片名称所在路径
    public URL getChipConfigFile() {
		return getRelativeFilePathFromPlugin("xml","ChipList.xml",false);
	}
	
	
	/**
	   * Returns the absolut path of a entrie from the plugin's directory.
	   * 
	   * @param entrie a file or directory (don't use "dir1\dir2" or "dir1\file1")
	   * @return Returns the path from the plugin.
	   * @see com.chipon32.util.ui.UiUtilActivator.getRelativeFilePathFromPlugin(String)
	   */
	public static String getFilePathFromPlugin(String entrie)
	{
		 URL url = null;
		 IPath path = null;
		 String result = "";

		 Enumeration<URL> enu = getDefault().getBundle().findEntries("/", entrie, true);
		 if(enu == null){
		    result = "";
		 }else{
				if (enu.hasMoreElements()) {
					url = enu.nextElement();
				}
				try {
					path = new Path(FileLocator.toFileURL(url).getPath());
					result = path.makeAbsolute().toOSString();
				} catch (Exception e) {
					result = "";
				}
		  }	   
		    return result;
	  }
	
	/**
	   * Returns the absolut path of a entrie from the plugin's directory.
	   * 
	   * @return Returns the Relative path from the plugin.
	   */
	public static URL getRelativeFilePathFromPlugin(String path, String filePatten, boolean recurse)
	{
		 URL url = null;
		 Enumeration<URL> enu = UiUtilActivator.getDefault().getBundle().findEntries(path, filePatten, recurse);
		 if(enu == null){
		   return null;
		 }else{
			if (enu.hasMoreElements()) {
				url = enu.nextElement();
			}
		  }	
		 
		  return url;
	  }
	
	/**
	 * 
	 * @param pathStr
	 * @param filePatten
	 * @return Returns the Relative path from the plugin.
	 */
//	public static String getRelativeFilePathFromPlugin(String pathStr, String filePatten)
//	{
//		 URL url = null;
//		 String resultPath = "";
//		 Enumeration<URL> enu = UiUtilActivator.getDefault().getBundle().findEntries(pathStr, filePatten, true);
//		 if(enu == null){
//		   return resultPath;
//		 }else{
//			if (enu.hasMoreElements()) {
//				url = enu.nextElement();
//			}
//			
//			try {
//				IPath path = new Path(FileLocator.toFileURL(url).getPath());
//				resultPath = path.makeAbsolute().toOSString();
//			} catch (Exception e) {
//				resultPath = "";
//			}
//		  }	   
//		 
//		  return resultPath;
//	  }
	
	/**
	 * 获取系统当前时间
	 * @return
	 */
	public String getCurrentTime(){
		SimpleDateFormat sf = new SimpleDateFormat("YYYY-MM-dd HH:mm:ss");
		String time = sf.format(System.currentTimeMillis());
		return time;
	}
	
	
	public MessageConsole getDefaultConsole() {
	  return findConsole(CONSOLE_NAME);
    }
	  
	public void writeMessage(String message) {
	 
		  MessageConsole myConsole = findConsole(CONSOLE_NAME);
		  MessageConsoleStream out = myConsole.newMessageStream();
		  
		  try {
			IConsoleView view;
			if(isNeedShowConsoleFlag)
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().showView(IConsoleConstants.ID_CONSOLE_VIEW);
			else
			{
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().findView(IConsoleConstants.ID_CONSOLE_VIEW);
				if(view==null)
					return;
			}
			view.display(getDefault().getDefaultConsole());
			out.println(message);
			
		  } catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		  }
	  }
	  
	  public void writeErrMessage(String message)
	  {
		  MessageConsole myConsole = findConsole(CONSOLE_NAME);
		  MessageConsoleStream out = myConsole.newMessageStream();
		  
		  try {
			IConsoleView view;
			if(isNeedShowConsoleFlag)
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().showView(IConsoleConstants.ID_CONSOLE_VIEW);
			else
			{
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().findView(IConsoleConstants.ID_CONSOLE_VIEW);
				if(view==null)
					return;
			}
			view.display(getDefault().getDefaultConsole());
			Display display = Display.getCurrent();
			out.setColor(display.getSystemColor(SWT.COLOR_RED));
			out.println(message);
			
		  } catch (Exception e) {
			e.printStackTrace();
		  }		  
	  }
	  
	  public void writeBLUEMessage(String message) {
		  
		  MessageConsole myConsole = findConsole(CONSOLE_NAME);
		  MessageConsoleStream out = myConsole.newMessageStream();
		  
		  try {
			IConsoleView view;
			if(isNeedShowConsoleFlag)
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().showView(IConsoleConstants.ID_CONSOLE_VIEW);
			else
			{
				view = (IConsoleView)PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().findView(IConsoleConstants.ID_CONSOLE_VIEW);
				if(view==null)
					return;
			}
			view.display(getDefault().getDefaultConsole());
			Display display = Display.getCurrent();
			out.setColor(display.getSystemColor(SWT.COLOR_BLUE));
			out.println(message);
		  } catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		  }
		  
	  }
	  
	  private MessageConsole findConsole(String name) {
	      ConsolePlugin plugin = ConsolePlugin.getDefault();
	      IConsoleManager conMan = plugin.getConsoleManager();
	      IConsole[] existing = conMan.getConsoles();
	      for (int i = 0; i < existing.length; i++)
	         if (name.equals(existing[i].getName()))
	            return (MessageConsole) existing[i];
	      //no console found, so create a new one
	      MessageConsole myConsole = new MessageConsole(name, null);
	      conMan.addConsoles(new IConsole[]{myConsole});
	      return myConsole;
	   }

	public Device getDevice() {
		return Device.getUniqueInstance();
	}

	public void setDevice(Device device) {
		
	}
	  
	  
}
