package com.chipon32.util.ui;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.core.runtime.jobs.Job;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerFilter;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.progress.WorkbenchJob;

public class FilteredTable extends Composite {

	private TableViewer tableViewer;
	private Table table;
	private Text text;
	
	private Job refreshJob;
	
	/**
	 * Create the composite
	 * @param parent
	 * @param style
	 */
	public FilteredTable(Composite parent, int style) {
		super(parent, SWT.NONE);
		
		final GridLayout gridLayout = new GridLayout();
		gridLayout.marginHeight = 3;
		setLayout(gridLayout);

		text = new Text(this, SWT.BORDER);
		text.setLayoutData(new GridData(SWT.FILL, SWT.BEGINNING, true, false));

		tableViewer = new TableViewer(this, style);
		table = tableViewer.getTable();
		table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
		//
		initialize();
	}
	
	public TableViewer getTableViewer() {
		return tableViewer;
	}
	
	public Table geTable() {
		return table;
	}
	
	private void initialize() {
		tableViewer.addFilter(new MyViewerFilter());
		refreshJob = new WorkbenchJob("Refresh") {
			@Override
			public IStatus runInUIThread(IProgressMonitor monitor) {
				if(table.isDisposed()) {
					return Status.CANCEL_STATUS;
				}
				tableViewer.refresh();
				 return Status.OK_STATUS;
			}
		};
		refreshJob.setSystem(true);
	}
	
	private class MyViewerFilter extends ViewerFilter {
		private StringMatcher matcher;
		
		public MyViewerFilter() {
			text.addModifyListener(new ModifyListener() {
				@Override
				public void modifyText(ModifyEvent e) {
					String str = text.getText();
					if (str.length() == 0) {
						matcher = null;
					} else {
						matcher = new StringMatcher("*" + str + "*", true, false);
					}
					refreshJob.cancel();
			    	refreshJob.schedule(400);
				}
			});
		}
		
		@Override
		public boolean select(Viewer viewer, Object parentElement, Object element) {
			return FilteredUtil.isMatch(viewer, element, matcher);
		}
	}

}
