package com.chipon32.util.model;

import java.util.ArrayList;
import java.util.List;

public class DataModel {
	
	String address = "00000000";
	String addressLength = "0000";
	String blockSize = "0000";
	String elementSize;
	String kind;
	String show;
	List<String> value = new ArrayList<>(); 
	
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getAddressLength() {
		return addressLength;
	}
	public void setAddressLength(String addressLength) {
		this.addressLength = addressLength;
	}
	public String getBlockSize() {
		return blockSize;
	}
	public void setBlockSize(String blockSize) {
		this.blockSize = blockSize;
	}
	public String getKind() {
		return kind;
	}
	public void setKind(String kind) {
		this.kind = kind;
	}
	public String getShow() {
		return show;
	}
	public void setShow(String show) {
		this.show = show;
	}
	public String getElementSize() {
		return elementSize;
	}
	public void setElementSize(String elementSize) {
		this.elementSize = elementSize;
	}
	public List<String> getValue() {
		return value;
	}
	public void setValue(List<String> value) {
		this.value = value;
	}

}
