 package com.chipon32.util.provider;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.xmlparse.ParseXML;

/**
 * 对IDE和编程器的统一提供配置文件的工厂类
 * <AUTHOR>	
 */
public class ConfigurationFactory {
	//#############################################################
	public ConfigurationFactory(){
		chipElementMap = new HashMap<>();
	}
	//############################################################# 芯片信息句柄	
	/**
	 * 针对每个芯片型号的缓存map，此缓存为了使每个型号的配置文件只解析一遍
	 */
	public static Map<String,IConfigurationProvider> chipElementMap = new HashMap<>();
	
	/**
	 * @param type 芯片型号
	 * @return 芯片元数据
	 */
	public static IConfigurationProvider getProvider(String name){
		if(chipElementMap.get(name) == null){
			IConfigurationProvider provider = new ConfigurationProvider();
			provider.setConfigurationMap(new ParseXML().parseTypeXML(name));
			if(provider!=null)
			{
				try{
					// provider new 出来的，但型号错误或当前版本不支持后续型号项目时的空map的失败推送不存在型号信息
					if(provider.getChipName()!=null)
					{
						chipElementMap.put(name, provider);
					}					
				}
				catch (Exception e) {
					// TODO: handle exception
				}
				
			}
		}
		
		return chipElementMap.get(name);
		
	}
	//############################################################# 芯片信息		
	/**
	 * 所有芯片型号的缓存list，为了使型号列表文件只解析一遍
	 */
	public static List<String> chipNameList = null;
	/**
	 * @return 芯片型号列表
	 */
	public static List<String> getchipNameList(){
		if(chipNameList == null){
			chipNameList = ParseXML.getNameList();
		}
		return chipNameList;
	}
	

	//@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@  型号分类
	private static List<String> alltypeList;
	private static Map<String, List<String>> alltypenamemapMap;
	private static Map<String, String> ToolnamemapMap;
	/**
	 * 
	 */
	public static List<String> getchipTypeList(){
		if(alltypeList == null){
			alltypeList = ParseXML.getTypeList();
		}
		return alltypeList;
	}
	
	public static Map<String, List<String>> getTypeAndNameMap()
	{
		if(alltypenamemapMap == null){
			alltypenamemapMap = ParseXML.getTypeAndNameMap();
		}
		return alltypenamemapMap;
	}
	
	public static Map<String, String> getNameAndToolMap()
	{
		if(ToolnamemapMap ==null  )
		{
			ToolnamemapMap=	ParseXML.getNameAndToolMap();
		}
		return ToolnamemapMap;
	}
	
}
