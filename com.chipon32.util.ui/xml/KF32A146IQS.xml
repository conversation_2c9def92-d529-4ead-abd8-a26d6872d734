<?xml version="1.0" encoding="GBK"?>
<ChipMessageConfig>
<!--  注意：这里所有数值配置均按照16进制配置，即获取字符串，按16进制解析到内int  -->
<!--  注意：这里所有大小均按照字节计算有效值  -->
<!--  版本信息  -->
		<seriescode>		32	</seriescode>    <!--    系列 KF8 或 KF32 ，16进制    -->
		<chiprange>			03	</chiprange>    <!--    芯片类型编码，16进制    -->
		<debugflag>			01	</debugflag>    <!--    芯片调试类型编码 ，16进制    -->
		<kernel>			02	</kernel>    <!--    内核版本，确定调用库资源，16进制    -->
		<version>			01	</version>    <!--    命令版本号 区别 编程版本，16进制    -->
		<issupprotR16TR31> 00	</issupprotR16TR31>    <!--    是否支持R16-R31的寄存器    -->
		<issupprotDSPACC>	00	</issupprotDSPACC>    <!--    是否支持DSP指令使能了ACC    -->
<!--  编程数据区 RAM信息 十六进制  -->
		<ramstartaddr>	10000000	</ramstartaddr>  
		<ramsize>			00008000	</ramsize>  
		<ramoffset>			00000000	</ramoffset>  
<!--  编程数据区 ROM信息 十六进制  -->
		<romstartaddr>	1FFF0000	</romstartaddr>  
		<romsize>			00000000	</romsize>  
		<romoffset>			00000000	</romoffset>  
<!--  编程程序区Flash信息  -->
		<flashstartaddr>	00000000	</flashstartaddr>  
		<flashsize>			00020000	</flashsize>  
		<flashoffset>		00000000	</flashoffset>  

<!--  编程User 信息 十六进制  -->
		<userstartaddr>	0C001C00	</userstartaddr>  
		<usersize>			00000400	</usersize>  
		<useroffset>		00000000	</useroffset>  
<!--  编程EEprom信息  -->
		<eepromstartaddr>	30000000	</eepromstartaddr>  
		<eepromsize>		00000000	</eepromsize>  
		<eepromoffset>	00000000	</eepromoffset>  
<!--  芯片加密信息  -->
		<debugprogramflagstartaddr> 0C001800	</debugprogramflagstartaddr>  
		<debugprogramflaglen> 4	</debugprogramflaglen>  

		<protectstartaddr> 0C001000	</protectstartaddr>  
		<protectlen>		4	</protectlen>  

<!--  编程配置字信息  -->
		<configsetstartaddr> 0C001500	</configsetstartaddr>  
		<configsetlen>	10	</configsetlen>  
		<defaultconfigvaule> 00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00	</defaultconfigvaule>  
<!--  芯片码识别信息  -->
		<devicesoftidaddr> 0C000408	</devicesoftidaddr>  
		<devicesoftidaddrlen> 4	</devicesoftidaddrlen>  
		<devicesoftid>	A146A0B3	</devicesoftid>  

		<devicehardidaddr> 0C000400	</devicehardidaddr>  
		<devicehardidaddrlen> 4	</devicehardidaddrlen>  
		<devicehardid>	01700000	</devicehardid>  

		<devicebootidaddr> 0C000404	</devicebootidaddr>  
		<devicebootidaddrlen> 4	</devicebootidaddrlen>  
		<devicebootid>	FFFFFFFF	</devicebootid>  

		<deviceicodeidaddr> 0C000404	</deviceicodeidaddr>    <!--    调试获取的DP寄存器内容，这里地址随意关联到boot    -->
		<deviceicodeidaddrlen> 4	</deviceicodeidaddrlen>  
		<deviceicodeid>	0bb11477	</deviceicodeid>  
<!--  芯片校准值  -->
		<devicecalstartaddr> 0C000F00	</devicecalstartaddr>  
		<devicecallenr>	00000080	</devicecallenr>  
<!--  芯片唯一值  -->
		<deviceonlyidrtaddr> 0C000430	</deviceonlyidrtaddr>  
		<deviceonlyidlen>	00000024	</deviceonlyidlen>  
<!--  IP校准值1，仅内部使用  -->
		<ip1calstartaddr>	0C000000	</ip1calstartaddr>  
		<ip1callenr>		00000040	</ip1callenr>  
<!--  IP校准值2，仅内部使用  -->
		<ip2calstartaddr>	0C800000	</ip2calstartaddr>  
		<ip2callenr>		00000040	</ip2callenr>  

<!--  编程参数  -->
		<memoryelementsize> 00000400	</memoryelementsize>    <!--    多对象组成，考虑页，和swd编程快大小    -->
		<icspwritesize>	00000200	</icspwritesize>    <!--    半页的单次操作量    -->
		<ispwritesize>	00000400	</ispwritesize>  
		<swdwritesize>	00000400	</swdwritesize>    <!--    RAM够，芯片实现支持循环的，半页整数倍数量，元素变量法的不超过元素数量    -->

<!--  ISP信息集  -->

<!--  SWD信息集  -->
		<bootloaderfile>	kf32dlxxloader.bin	</bootloaderfile>    <!--    swd编程引导程序代码文件    -->
		<bootloaderstartRamAddr> 10000200	</bootloaderstartRamAddr>    <!--    bootloader写RAM起始地址    -->
		<bootloaderInitFunAddr> 10000268	</bootloaderInitFunAddr>    <!--    配置芯片时钟等特性    -->
		<bootloaderRunFunAddr> 10000224	</bootloaderRunFunAddr>    <!--    功能执行入口    -->
		<bootloadIAPEnter> 1fff0b0c	</bootloadIAPEnter>    <!--    芯片IAP的入口地址，原1FFF0AC8后改变到1fff0b40但使用前级入口，会切换映射    -->
		<bootloaderParameterRamAddr> 100007E0	</bootloaderParameterRamAddr>    <!--    参数RAM起始地址    -->
		<swdcontrolstateReg> E000EDF0	</swdcontrolstateReg>    <!--    调试控制及状态寄存器地址    -->
		<swdwatchcontrolReg> E000EDFC	</swdwatchcontrolReg>    <!--    调试监控控制寄存器地址    -->
		<swdeventstateReg> E000ED30	</swdeventstateReg>    <!--    调试状态寄存器    -->
		<appresetReg>		402000B8	</appresetReg>    <!--    应用复位控制寄存器地址    -->

<!--  ICSP编程命令集  -->
		<ICSP_LC>			1C	</ICSP_LC>    <!--    1 加载到配置空间     -->
		<ICSP_LDFPM>		1E	</ICSP_LDFPM>    <!--    2 向程序区写入数据     -->
		<ICSP_LDFDM>		1F	</ICSP_LDFDM>    <!--    3 向数据区写入数据     -->
		<ICSP_RDFPM>		18	</ICSP_RDFPM>    <!--    4 从程序区读取数据     -->
		<ICSP_RDFDM>		19	</ICSP_RDFDM>    <!--    5 从数据区读取数据     -->
		<ICSP_IA>			02	</ICSP_IA>    <!--    6 地址自增     -->
		<ICSP_BPIT>			14	</ICSP_BPIT>    <!--    7 开启内部编程计时     -->
		<ICSP_BPET>			04	</ICSP_BPET>    <!--    8 结束编程     -->
		<ICSP_BEPM>			05	</ICSP_BEPM>    <!--    9 片擦程序区     -->
		<ICSP_BEDM>			17	</ICSP_BEDM>    <!--    A 片擦数据区     -->
		<ICSP_REPM>			0D	</ICSP_REPM>    <!--    B 行擦程序区     -->
		<ICSP_RADD>			1B	</ICSP_RADD>    <!--    C 读地址     -->
		<ICSP_WADD>			1D	</ICSP_WADD>    <!--    D 置地址     -->
		<ICSP_UUSN>			01	</ICSP_UUSN>    <!--    E 解锁指令     -->
		<ICSP_LDSFPM>		03	</ICSP_LDSFPM>    <!--    F 多数据载入指令     -->
		<ICSP_RUNTEST>	19	</ICSP_RUNTEST>    <!--    10　执行TEST命令    -->
		<ICSP_CONDEN>		10	</ICSP_CONDEN>    <!--    11　配置CONFEN寄存器    -->
		<ICSP_SFR>			1A	</ICSP_SFR>    <!--    12　配置ICSP功能寄存器    -->
<!--  无配置字的样例存在但无实际作用  -->
<!--  无配置字的样例存在但无实际作用  -->
<!--  无配置字的样例存在但无实际作用  -->
<!--  无配置字的样例存在但无实际作用  -->
        <configsetconfig>
			<name>				配置功能1	</name>    <!--    配置功能名称    -->
			<addr>				0C001500	</addr>    <!--    起始的配置地址     -->
			<bytelen>			1	</bytelen>    <!--    该配置占用的字节数 ，应该最多2个字节，支持3个字节设计    -->
			<description>		这是功能配置字1	</description>    <!--    功能指示文字描述    -->
			<element>			A1,A2,A3,A4	</element>    <!--    直观文字功能描述选项    -->
			<elementvaule>	00,08,10,18	</elementvaule>    <!--    和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值    -->
			<elementSel>		2	</elementSel>    <!--    默认的功能选择    -->
			<elementSelvaule>	A3	</elementSelvaule>    <!--    默认的功能选项描述    -->
			<vaulemask>			E7	</vaulemask>    <!--    自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割    -->
        </configsetconfig>
<!-- ||||||||||||||||||||||||||||||||||||||||||||  -->
        <configsetconfig>
			<name>				配置功能2	</name>    <!--    配置功能名称    -->
			<addr>				0C001500	</addr>    <!--    起始的配置地址     -->
			<bytelen>			2	</bytelen>    <!--    该配置占用的字节数 ，应该最多2个字节，支持3个字节设计    -->
			<description>		这是功能配置字2	</description>    <!--    功能指示文字描述    -->
			<element>			B1,B2,B3,B4	</element>    <!--    直观文字功能描述选项    -->
			<elementvaule>	0000,0080,0100,0180	</elementvaule>    <!--    和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值    -->
			<elementSel>		1	</elementSel>    <!--    默认的功能选择    -->
			<elementSelvaule>	B2	</elementSelvaule>    <!--    默认的功能选项描述    -->
			<vaulemask>			FE,7F	</vaulemask>    <!--    自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割    -->
        </configsetconfig>
<!-- ||||||||||||||||||||||||||||||||||||||||||||  -->
        <configsetconfig>
			<name>				配置功能3	</name>    <!--    配置功能名称    -->
			<addr>				0C001501	</addr>    <!--    起始的配置地址     -->
			<bytelen>			3	</bytelen>    <!--    该配置占用的字节数 ，应该最多2个字节，支持3个字节设计    -->
			<description>		这是功能配置字3	</description>    <!--    功能指示文字描述    -->
			<element>			C1,C2,C3,C4	</element>    <!--    直观文字功能描述选项    -->
			<elementvaule>	000000,0007E0,01F800,01FFE0	</elementvaule>    <!--    和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值    -->
			<elementSel>		0	</elementSel>    <!--    默认的功能选择    -->
			<elementSelvaule>	C1	</elementSelvaule>    <!--    默认的功能选项描述    -->
			<vaulemask>			E0,3F,1F	</vaulemask>    <!--    自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割    -->
        </configsetconfig>
<!-- ||||||||||||||||||||||||||||||||||||||||||||  -->
        <configsetconfig>
			<name>				配置功能4	</name>    <!--    配置功能名称    -->
			<addr>				0C001501	</addr>    <!--    起始的配置地址     -->
			<bytelen>			1	</bytelen>    <!--    该配置占用的字节数 ，应该最多2个字节，支持3个字节设计    -->
			<description>		这是功能配置字4	</description>    <!--    功能指示文字描述    -->
			<element>			D1,D2,D3,D4	</element>    <!--    直观文字功能描述选项    -->
			<elementvaule>	00,04,08,0C	</elementvaule>    <!--    和选择对应的后台数组,值应该按照字节长度的整体对应bit上的数值    -->
			<elementSel>		3	</elementSel>    <!--    默认的功能选择    -->
			<elementSelvaule>	D4	</elementSelvaule>    <!--    默认的功能选项描述    -->
			<vaulemask>			F3	</vaulemask>    <!--    自身位掩码，用于对应位清空，字节16进制配置，字节间隔使用","分割    -->
        </configsetconfig>
<!-- ||||||||||||||||||||||||||||||||||||||||||||  -->
<!--  芯片电压操作特性  -->
		<classhighpowerlevel> 32	</classhighpowerlevel>    <!--    21 3.3V  32 5.0V    -->
		<firewareneedconfig1> B01V02S18	</firewareneedconfig1>    <!--    0无要求 stm103版本    -->
		<firewareneedconfig2> B02V01S2	</firewareneedconfig2>    <!--    0无要求 kungfu版本    -->
		<firewareneedconfig3> B02V02S1	</firewareneedconfig3>    <!--    rev    -->
		<firewareneedconfig4> B03V01S1	</firewareneedconfig4>    <!--    rev    -->
</ChipMessageConfig>
