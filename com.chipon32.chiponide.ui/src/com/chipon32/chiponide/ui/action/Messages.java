package com.chipon32.chiponide.ui.action;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.chiponide.ui.action.messages"; //$NON-NLS-1$
	public static String ChiponUpdateFirmwareAction_2;
	public static String ChiponUpdateFirmwareAction_3;
	public static String ChiponUpdateFirmwareAction_5;
	public static String ChiponUpdateFirmwareAction_6;
	public static String ChiponUpdateFirmwareAction_7;
	public static String ChiponUpdateFirmwareAction_8;

	
	public static String OpenDeclarationInASMEditorAction_1;
	
	public static String OpenDriveSetupAction_2;
	public static String OpenDriveSetupAction_3;
	public static String OpenDriveSetupAction_5;
	public static String OpenDriveSetupAction_6;
	public static String OpenDriveSetupAction_7;
	public static String OpenDriveSetupAction_8;
	
	public static String ReBuildActiveConfigMenuAction_buildConfigTooltip;
	public static String ReBuildActiveConfigMenuAction_defaultTooltip;
	
	public static String TestLanguage;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
