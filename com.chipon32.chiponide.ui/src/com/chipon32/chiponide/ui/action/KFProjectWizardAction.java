package com.chipon32.chiponide.ui.action;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.jface.wizard.WizardDialog;
import org.eclipse.ui.IWorkbench;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.IWorkbenchWindowActionDelegate;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.internal.ide.IIDEHelpContextIds;

import com.chipon32.chiponide.ui.wizards.ChiponProjectWizard;

public class KFProjectWizardAction  implements IWorkbenchWindowActionDelegate {

	private IWorkbenchWindow window;
	public KFProjectWizardAction() {
		window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
	}

	@Override
	public void run(IAction action) {
		IWorkbench workbench = PlatformUI.getWorkbench();
		ChiponProjectWizard wizard = new ChiponProjectWizard();
		ISelection selection = window.getSelectionService().getSelection();
		IStructuredSelection selectionToPass = StructuredSelection.EMPTY;
		if (selection instanceof IStructuredSelection) {
			selectionToPass = (IStructuredSelection) selection;
		}
		wizard.init(workbench, selectionToPass);
		
		// Create wizard dialog.
		WizardDialog dialog = new WizardDialog(null, wizard);
		dialog.create();
		dialog.getShell().setSize(
				Math.max(500, dialog.getShell().getSize().x),
				650);
		PlatformUI.getWorkbench().getHelpSystem().setHelp(dialog.getShell(),
				IIDEHelpContextIds.NEW_PROJECT_WIZARD);

		// Open wizard.
		dialog.open();
		
	}

	@Override
	public void selectionChanged(IAction action, ISelection selection) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void dispose() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void init(IWorkbenchWindow window) {
	}
	
	

}
