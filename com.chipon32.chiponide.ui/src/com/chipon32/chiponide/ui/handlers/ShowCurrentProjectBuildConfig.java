package com.chipon32.chiponide.ui.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.core.resources.IProject;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.ui.internal.dialogs.PropertyDialog;

import com.chipon32.chiponide.ui.UiActivator;

public class ShowCurrentProjectBuildConfig extends AbstractHandler {

	@Override
	public Object execute(ExecutionEvent event) throws ExecutionException {
		IProject currentProj = UiActivator.getDefault().getCurrentProject();
		if(currentProj!=null) {
			PropertyDialog.createDialogOn(null, "org.eclipse.cdt.managedbuilder.ui.properties.Page_BuildSettings", currentProj).open(); //$NON-NLS-1$
			
		}else {
			MessageDialog.openInformation(null, Messages.ShowCurrentProjectBuildConfig_1, Messages.ShowCurrentProjectBuildConfig_2);
		}
		
		return null;
	}

}
