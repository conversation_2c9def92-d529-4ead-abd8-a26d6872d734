LinkProjectScriptFile_0=Confirm
LinkProjectScriptFile_1=Is the added ld file used for building?
LinkProjectScriptFile_2=Prompt
LinkProjectScriptFile_3=Script file not found\!
LinkProjectScriptFile_4=Error
LinkProjectScriptFile_5=Generate linker description file error\!

PowerOffHandler_0=Set Use Occupy Or Other, Like Wrong State Or Not Find!
PowerOffHandler_1=Set Use Occupy Or Other, Like Wrong State Or Not Find!
PowerOffHandler_2=Power Off!

PowerOnHandler_0=Set Use Occupy Or Other, Like Wrong State Or Not Find!
PowerOnHandler_1=Set Use Occupy Or Other, Like Wrong State Or Not Find!
PowerOnHandler_2=Power On!

ReadDeviceHandler_0=No Find Chip Config Exist, Exit Deal of Course
ReadDeviceHandler_1=Info
ReadDeviceHandler_2=Target File Nonexistent, Please Build Project.
ResetDeviceHandler_3=Affirm 
ResetDeviceHandler_4=Discover Debug Target Running, The Process Maybe occupy Program Debug Set
ResetDeviceHandler_5=.\n\nNeed Exit Debug From Workbench After Action End\n\nForce Close?

RestDriver_0=\ NULL\ 
RestDriver_1=series port{
RestDriver_10=\ Mode:ICSP
RestDriver_11=\ Mode:DPI
RestDriver_17=Firmware:
RestDriver_18=, Software Currently:
RestDriver_19=, you can upgrade from help menu...
RestDriver_20=\ Mode:ISP
RestDriver_21=\ Mode:ISP
RestDriver_24=\ Mode:ISP
RestDriver_25=\ Mode:ISP
RestDriver_28=\ Mode:ISP
RestDriver_29=Communicate Error,Please Check Set Link  Or Upgrade Firmware!
RestDriver_3=} Set:{
RestDriver_30=Error:Please Check Set Link Or Restart Soft, And then retry!
RestDriver_5=} Select:
RestDriver_8=\ Mode:ICSP
RestDriver_9=\ Mode:DPI

UploadHexFileHandler_0=Download Info
UploadHexFileHandler_1=Selected Project  Different From Project Of Editor File，Continue will Use Selected Project
UploadHexFileHandler_2=.Are You Sure To Download?
UploadHexFileHandler_3=No Find Chip Config Exist, Exit Download of Course
UploadHexFileHandler_4=Prompt
UploadHexFileHandler_5=Target File Nonexistent, Please Build Project.
UploadHexFileHandler_7=Currently Download Project:
UploadHexFileHandler_8=Code Checksum:
UploadHexFileHandler_9=The selected project was not found! Please select the project before downloading.

ShowCurrentProjectBuildConfig_1=Prompt
ShowCurrentProjectBuildConfig_2=Please select a project and then open the project build settings
