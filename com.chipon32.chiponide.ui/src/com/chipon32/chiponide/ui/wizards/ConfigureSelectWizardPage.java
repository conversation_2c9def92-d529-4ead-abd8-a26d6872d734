package com.chipon32.chiponide.ui.wizards;


import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.IManagedProject;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.ui.wizards.MBSCustomPage;
import org.eclipse.cdt.managedbuilder.ui.wizards.MBSCustomPageData;
import org.eclipse.cdt.managedbuilder.ui.wizards.MBSCustomPageManager;
import org.eclipse.cdt.ui.wizards.CDTCommonProjectWizard;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Path;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.dialogs.ErrorDialog;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.wizard.IWizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.IViewPart;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.ide.IDE;
import org.osgi.service.prefs.BackingStoreException;

import com.chipon32.chiponide.core.CoreActivator;
import com.chipon32.chiponide.core.natures.ChipOnProjectNature;
import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.chiponide.ui.views.ProgramUtilizationView;
import com.chipon32.configbit.ui.configProvider.ConfigProviderRegister;
import com.chipon32.configbit.ui.configProvider.IConfigBase;
import com.chipon32.configbit.ui.configProvider.IConfigProvider;
import com.chipon32.util.ui.UiUtilActivator;
import com.chipon32.util.xmlparse.CopyFileUtil;
/*
 * 建立项目时的配置位设置向导页
 * （当前屏蔽了）
 * */
public class ConfigureSelectWizardPage extends MBSCustomPage implements Runnable {

	public static final String PAGE_ID = "com.chipon32.chiponide.core.configureSelectPage";	// 页提供者对应
	
	private  final String PROPERTY_CHIP_TYPE = "configureType";	
	private  final String PROPERTY_CONFIG_PROVIDER = "configProvider";	
//	private  final String PROPERTY_CONFIG_INCODE = "configincode";
	private  final String pROPERTY_CONFIG_LibFILE = "is Load Std Lib Files";	 //$NON-NLS-1$
	

	public static boolean isInit=false;	
	public static boolean isCreate=false;
	// 顶层的绘图对象，即父类
	private Composite top;	
	// 项目属性对象
	private ChipOnProjectProperties	fProperties;	
	
	private String chipName = null;	
	
	public static boolean complete = false;

	// 项目信息提供者
	private IConfigProvider configProvider;
	// 项目的参考电压
	private String chipPower;
	
	private Composite cplComposite;


	// 项目建立选项
//	private Group option;
	private Button isCopyStdLibbutton;
	//==============================================================================================
	public ConfigureSelectWizardPage() {
		this.pageID = PAGE_ID;
		
		fProperties = ProjectPropertyManager.getDefaultProperties();
		
		chipName = fProperties.getChipName();
		MBSCustomPageManager.addPageProperty(PAGE_ID, PROPERTY_CHIP_TYPE, chipName);
		complete=false;
	}
	// 配置位设置页
	@Override
	public String getName() {
		return Messages.ConfigureSelectWizardPage_4;
	}
	
	/**
	 *  界面内容生成
	 */
	@Override
	public void createControl(Composite parent) {
		
		// 上一个向导页中的 芯片型号获取
		chipName = (String) MBSCustomPageManager.getPageProperty(HardwareSelectWizardPage.PAGE_ID, HardwareSelectWizardPage.PROPERTY_CHIP_NAME);
		// 网格
		GridData gridData = new GridData(GridData.FILL_BOTH);
		gridData.horizontalAlignment = GridData.END; // 水平方式
		//######################################################################################################
		// 上部的控件  管理配置，和配置 release或debug选择,内外部按钮
		top = new Composite(parent, SWT.NONE);
		top.setLayout(new GridLayout(1, false));
		top.setLayoutData(new GridData(GridData.FILL_BOTH));
		//  内部配置按钮

		// 芯片信息提供者
		configProvider = ConfigProviderRegister.getProvider(chipName);// 通过芯片型号来获取与之匹配的配置位提供器（provider）
		if (configProvider == null) {
			 MessageDialog.openInformation(top.getShell(), "ChipON KF32 IDE", Messages.ConfigureSelectWizardPage_6); //$NON-NLS-1$
			return;
		}
		//######################################################################################################
		// 管理对象添加，后续需要
		MBSCustomPageManager.addPageProperty(PAGE_ID, PROPERTY_CONFIG_PROVIDER, configProvider);
		// 芯片型号的配置字信息绘图实现
//		configProvider.createPartControl(top);		 	// 内容的视图实现
		
		//选择编译器栏目
		cplComposite = new Composite(top, SWT.NONE);
		cplComposite.setLayout(new GridLayout(2, false));
		cplComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
	
		//
		complete=true;
		isInit=false;
		isCreate=true;
	}

	/**
	 * 完成后的工作、(选择上一步)型号变更后下一步按钮进入页面时
	 * 把之前的视图关闭再重新生成视图
	 */
	public void updateControl() {
		///  内容基本完全相同构建者，
		if(top==null){
			return;
		}
		// 获取当前项目的芯片型号
		chipName = (String) MBSCustomPageManager.getPageProperty(HardwareSelectWizardPage.PAGE_ID, HardwareSelectWizardPage.PROPERTY_CHIP_NAME);
		// 内容全注销
		Control[] topControl=top.getChildren();
		for(int i=0;i<topControl.length;i++){
			topControl[i].dispose();
		}
		// 内容重新添加
		GridData gridData = new GridData(GridData.FILL_BOTH);
		gridData.horizontalAlignment = GridData.END;
		// 1列模式的设定
		top.setLayout(new GridLayout(1, false));
		top.setLayoutData(new GridData(GridData.FILL_BOTH));
		// 通过芯片型号来获取与之匹配的配置位提供器（provider）
		configProvider = ConfigProviderRegister.getProvider(chipName);
		if (configProvider == null) {
			 MessageDialog.openInformation(top.getShell(), "ChipON IDE KF32", Messages.ConfigureSelectWizardPage_6); //$NON-NLS-1$
			return;
		}
		//############################################################################################
		// 型号变化的型号信息对象更新
		MBSCustomPageManager.addPageProperty(PAGE_ID, PROPERTY_CONFIG_PROVIDER, configProvider);
		// 芯片配置的绘制结果
//		configProvider.createPartControl(top);
		
		//选择编译器栏目
		cplComposite = new Composite(top, SWT.NONE);
			GridData layData = new GridData(GridData.FILL_BOTH);
			layData.horizontalAlignment = GridData.END; 
		cplComposite.setLayout(new GridLayout(2, false));
		cplComposite.setLayoutData(new GridData(GridData.FILL_BOTH));		
		

		// 暂无法完成信息传递的 期望增加选择
//		option=new Group(cplComposite, SWT.NONE);
//		option.setText("附加选项");
//		option.setLayout(new GridLayout(1, false));
//		option.setLayoutData(new GridData(SWT.FILL,SWT.FILL,true,true,2,1));
//		
//		isCopyStdLibbutton=new Button(option, SWT.CHECK);
//		isCopyStdLibbutton.setText("勾选导入外设库文件(C项目可用)");
//		isCopyStdLibbutton.setSelection(true);			
//		//是否导入库向导
//		MBSCustomPageManager.addPageProperty(PAGE_ID, pROPERTY_CONFIG_LibFILE, true);
//		isCopyStdLibbutton.addSelectionListener(new SelectionListener() {
//			
//			@Override
//			public void widgetSelected(SelectionEvent e) {
//				// TODO Auto-generated method stub
//				MBSCustomPageManager.addPageProperty(PAGE_ID, pROPERTY_CONFIG_LibFILE, isCopyStdLibbutton.getSelection());
//			}
//			
//			@Override
//			public void widgetDefaultSelected(SelectionEvent e) {
//				// TODO Auto-generated method stub
//				
//			}
//		});
		//############################################################################################
		// 视图显示
		top.layout();
		complete=true;
		isInit=true;
	}
	//########### 运行过程#############################################################################################
	@Override
	public void run() {		
		// 当前页数据点击完成后的结果处理实现
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		MBSCustomPageData pagedata = MBSCustomPageManager.getPageData(HardwareSelectWizardPage.PAGE_ID);
		// 工程向导
		CDTCommonProjectWizard wizz = (CDTCommonProjectWizard) pagedata.getWizardPage().getWizard();
		// 获取芯片名和项目
		chipName = (String) MBSCustomPageManager.getPageProperty(HardwareSelectWizardPage.PAGE_ID, HardwareSelectWizardPage.PROPERTY_CHIP_NAME);
		IProject project = wizz.getLastProject();
		// 获取项目的管理者对象
		ProjectPropertyManager projpropsmanager = ProjectPropertyManager.getPropertyManager(project);
		// 由管理者对象获取芯片的项目属性
		ChipOnProjectProperties props = projpropsmanager.getProjectProperties();
		
			
		// 获取芯片信息和电压
		IConfigProvider configProvider=(IConfigProvider)MBSCustomPageManager.getPageProperty(PAGE_ID, PROPERTY_CONFIG_PROVIDER);				
		chipPower = configProvider.getChipPower();
		
        if(chipPower == null){//如果配置位界面未初始化，则根据配置文件
            chipPower = props.getfChipPower();
        }
        props.setfChipPower(chipPower);
      
	    // 执行项目属性的存贮
		try {
			props.save();
		} catch (BackingStoreException e) {
			IStatus status = new Status(IStatus.ERROR, CoreActivator.PLUGIN_ID,
					Messages.HardwareSelectWizardPage_6, e);

			ErrorDialog.openError(PlatformUI.getWorkbench().getActiveWorkbenchWindow().getShell(),
					Messages.HardwareSelectWizardPage_7, null, status);
		}

		// kf项目属性添加该项目的内容
		try {
			ChipOnProjectNature.addChipOnNature(project);
		} catch (CoreException ce) {
			// addAVRNature() should not cause an Exception, but just in case we log it.
			IStatus status = new Status(IStatus.ERROR, CoreActivator.PLUGIN_ID,
					Messages.HardwareSelectWizardPage_8 + project.toString() + "]", ce);
			CoreActivator.getDefault().log(status);
		}	
		// 芯片信息获取者
//		IConfigurationProvider co =ConfigurationFactory.getProvider(chipName);

		//对应模板文件的打开状态控制
		IFile file = null;
		IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(project);

		if (bi != null) 
		{			
			complete = false;
			IManagedProject buildcfg = bi.getManagedProject();
			String type = buildcfg.getProjectType().getId();
			IWorkbenchPage page = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage();

			// 如果是汇编项目，打开main.asm文件
			if (type.equals(IConfigBase.CHIPON_ASM_ID)) 
			{					
				try {
					file = project.getFile(new Path("/main.asm")); //$NON-NLS-1$
					IDE.openEditor(page, file); //默认打开main.asm文件
				} catch (PartInitException e) {
					
				}
			}
			// 如何是C项目，打开main.c文件
			if (type.equals(IConfigBase.CHIPON_C_ID)) 
			{			
				try {
					file = project.getFile(new Path("/main.c"));	 //$NON-NLS-1$
					IDE.openEditor(page, file); //默认打开main.c文件
				} catch (PartInitException e) {
					
				}
			}
			// 如何是C++项目，打开main.cpp文件
			if (type.equals(IConfigBase.CHIPON_Cplus_ID)) 
			{			
				try {
					file = project.getFile(new Path("/main.cpp"));	 //$NON-NLS-1$
					IDE.openEditor(page, file); //默认打开main.cpp文件
				} catch (PartInitException e) {
					
				}
			}
			// 导入外设库
			if(isCopyStdLibbutton!=null)
			{
				boolean isture = (boolean) MBSCustomPageManager.getPageProperty(PAGE_ID, pROPERTY_CONFIG_LibFILE);
				if(isture)
				{
				   final CopyFileUtil copyfile=new CopyFileUtil();
                   String soursefilepath=UiUtilActivator.getFilePathFromPlugin("KF32DL52XX");  //$NON-NLS-1$
                   copyfile.copyFile(soursefilepath, project.getLocation().toString(), false);
					
				}
			}		
			
			// 从创建处结果到 资源使用率下的内容
			IViewPart viewPart=page.findView("com.chipon32.chiponide.ui.views.ProgramUtilization"); //$NON-NLS-1$
			if(viewPart!=null && viewPart instanceof ProgramUtilizationView){
				ProgramUtilizationView pview = (ProgramUtilizationView) viewPart;
				pview.refreshModel(project);
			}
		}		
		
	}
	//########### #############################################################################################
	@Override
	public void dispose() {
		top.dispose();
	}

	@Override
	public IWizardPage getNextPage() {

		return super.getNextPage();
	}


	@Override
	public Control getControl() {
		// TODO Auto-generated method stub
		return top;
	}

	@Override
	public String getDescription() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getErrorMessage() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Image getImage() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getMessage() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getTitle() {
		// TODO Auto-generated method stub
		return Messages.ConfigureSelectWizardPage_7;
	}

	@Override
	public void performHelp() {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDescription(String arg0) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setImageDescriptor(ImageDescriptor arg0) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setTitle(String arg0) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setVisible(boolean arg0) {
		top.setVisible(arg0);
	}
	// 页面完成
	@Override
	protected boolean isCustomPageComplete() {

		if(isInit && isCreate){
			updateControl();
		}
		return complete;
	}
	
	
	
}
