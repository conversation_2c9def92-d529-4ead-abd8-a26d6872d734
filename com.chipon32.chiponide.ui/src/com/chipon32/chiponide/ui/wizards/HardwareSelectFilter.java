package com.chipon32.chiponide.ui.wizards;

import java.util.Arrays;

import org.eclipse.cdt.managedbuilder.core.IToolChain;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerFilter;

import com.chipon32.chiponide.ui.util.ChipNameModel;

/**
 * <AUTHOR> Wu
 */
public class HardwareSelectFilter extends ViewerFilter {

	public static final String CLANG_TOOLCHAIN_ID = "com.chipon32.chiponide.core.toolchain.llvm";
	public static final String ccd1_issue = "ccd1_issue";

	private final IToolChain toolchain;
	private final boolean isMultiCoreChip;

	public HardwareSelectFilter(IToolChain toolchain) {
		this.toolchain = toolchain;
		isMultiCoreChip = toolchain.getSuperClass().getId().equals(CLANG_TOOLCHAIN_ID);
	}

	@Override
	public boolean select(Viewer viewer, Object parentElement, Object element) {
		if (element instanceof ChipNameModel chipName) {
			if (chipName.getElement() == null && !chipName.getChildList().isEmpty()) {
				boolean ccdTool=false;
				for (ChipNameModel childModel : chipName.getChildList()) {
					if (Arrays.asList(childModel.getElement()).contains(ccd1_issue)) {
						ccdTool=true;
						break;
					}
				}

				if (isMultiCoreChip) {
					return ccdTool;
				} else {
					return !ccdTool;
				}
			}
		}
		return true;
	}
}
