package com.chipon32.chiponide.ui.wizards;

import org.eclipse.cdt.core.CCorePlugin;
import org.eclipse.cdt.core.CProjectNature;
import org.eclipse.cdt.ui.wizards.CDTCommonProjectWizard;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.NullProgressMonitor;
import org.eclipse.core.runtime.SubMonitor;
/**
 * 重写CDT的此方法以改变Title和描述信息，即新建KungFu8 项目向导页的Title和描述信息
 * <AUTHOR>
 */
public class ChiponProjectWizard extends CDTCommonProjectWizard {
	// 标题和 提示
	public ChiponProjectWizard(){
		super(Messages.ChiponProjectWizard_0, Messages.ChiponProjectWizard_1);
	}
	
	@Override
	public String[] getNatures() {
		return new String[] { CProjectNature.C_NATURE_ID };
	}
	/**
	 * 创建过程的  C、asm工程向导字样   后面
	 */
	@Override
	protected IProject continueCreation(IProject prj) {
		// 进度条类
		if (continueCreationMonitor == null) {
			continueCreationMonitor = new NullProgressMonitor();
		}
		// 添加C,asm项目特性
		try {
			continueCreationMonitor.beginTask(Messages.CProjectWizard_0, 1);
			CProjectNature.addCNature(prj, SubMonitor.convert(continueCreationMonitor, 1));
		} catch (CoreException e) {

		}
		finally {continueCreationMonitor.done();}
		return prj;
	}

	@Override
	public String[] getContentTypeIDs() {
		return new String[] {CCorePlugin.CONTENT_TYPE_ASMSOURCE,  CCorePlugin.CONTENT_TYPE_CSOURCE, CCorePlugin.CONTENT_TYPE_CHEADER };
	}

	@Override
	public boolean performCancel() {
		// TODO Auto-generated method stub
		HardwareSelectWizardPage.complete = false;
		ConfigureSelectWizardPage.complete = false;
		return super.performCancel();
	}
	
	
	
}
