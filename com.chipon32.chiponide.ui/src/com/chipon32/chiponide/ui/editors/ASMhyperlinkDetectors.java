package com.chipon32.chiponide.ui.editors;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IRegion;
import org.eclipse.jface.text.ITextViewer;
import org.eclipse.jface.text.hyperlink.AbstractHyperlinkDetector;
import org.eclipse.jface.text.hyperlink.IHyperlink;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchSite;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.editors.text.TextEditor;

import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.chiponide.ui.util.ASMWordFinder;
import com.chipon32.chiponide.ui.util.ContentProvider;

public class ASMhyperlinkDetectors extends AbstractHyperlinkDetector {

	protected TextEditor fEditor;
	protected IWorkbenchSite fSite;

	public ASMhyperlinkDetectors() {
	}

	@Override
	public IHyperlink[] detectHyperlinks(ITextViewer textViewer, IRegion region, boolean canShowMultipleHyperlinks) {
		IHyperlink hyperlink = null;

		IWorkbenchPage workbenchPage = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage();

		if (workbenchPage != null) {
			if (region != null && workbenchPage.getActiveEditor() instanceof ASMEditor asmEditor) {
				fEditor = asmEditor;
				fSite = fEditor.getSite();
				IDocument document = fEditor.getDocumentProvider().getDocument(fEditor.getEditorInput());
				if (region.getOffset() > 0 && region.getLength() == 0) {
					region = ASMWordFinder.findWord(document, region.getOffset());
				}
				String str = computeSelectedWord(region);
				if (str == null || str.length() == 0)
					return null;

				ContentProvider cp = new ContentProvider((ASMEditor) fEditor);
				cp.parse();

				TreeObject labels = cp.getLabels();
				TreeObject segments = cp.getSegments();

				boolean isLabels = false;
				Object[] childs = labels.getChildren();
				for (Object child : childs) {
					if (child instanceof TreeObject) {
						TreeObject node = (TreeObject) child;

						if (str.equals(node.toString().trim())) {
							isLabels = true;
							hyperlink = new ASMElementHyperlink(region);
							break;
						}
					}
				}

				if (!isLabels) {
					childs = segments.getChildren();
					for (Object child : childs) {
						if (child instanceof TreeObject) {
							TreeObject node = (TreeObject) child;
							String source = node.toString().trim();
							String[] strs = source.split("\\s+");
							if (strs.length > 1 && strs[1].equals(str)) {
								isLabels = true;
								hyperlink = new ASMElementHyperlink(region);
								break;
							}
						}
					}
				}
				if (hyperlink != null) {
					IHyperlink[] hyperlinks = new IHyperlink[] { hyperlink };
					return hyperlinks;
				}

			} else {
				return null;
			}
		}
		return null;
	}

	private String computeSelectedWord(IRegion region) {
		String text = null;
		IDocument document = fEditor.getDocumentProvider().getDocument(fEditor.getEditorInput());
		try {
			text = document.get(region.getOffset(), region.getLength());
		} catch (BadLocationException e) {
			UiActivator.getDefault().getLog().log(new Status(IStatus.ERROR, UiActivator.PLUGIN_ID, IStatus.ERROR, "ERROR", e));
		}
		return text;
	}
}
