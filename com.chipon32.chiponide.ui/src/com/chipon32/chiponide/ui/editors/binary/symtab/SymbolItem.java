package com.chipon32.chiponide.ui.editors.binary.symtab;

import org.eclipse.cdt.utils.elf.Elf;

import com.google.common.base.MoreObjects;

/**
 * Represents a symbol table entry in the binary file viewer. This class
 * encapsulates all the properties of a symbol that can be displayed in the UI.
 */
public class SymbolItem {

	/**
	 * The symbol labels an uninitialized common block.
	 */
	public static final byte STT_COMMON = 5;
	/**
	 * The symbol specifies a Thread-Local Storage entity.
	 */
	public static final byte STT_TLS = 6;

	/**
	 * Lower bound for range reserved for operating system-specific semantics.
	 */
	public static final byte STT_LOOS = 10;
	/**
	 * Upper bound for range reserved for operating system-specific semantics.
	 */
	public static final byte STT_HIOS = 12;
	/**
	 * Lower bound for range reserved for processor-specific semantics.
	 */
	public static final byte STT_LOPROC = 13;
	/**
	 * Upper bound for range reserved for processor-specific semantics.
	 */
	public static final byte STT_HIPROC = 15;

	public final int number;
	public final String address;
	public final String name;
	public final int binding;
	public final int type;
	public final short section;
	public final short visibility;
	public final long size;

	public SymbolItem(int number, String address, String name, int binding, int type, short section, short visibility, long size) {
		this.number = number;
		this.address = address;
		this.name = name;
		this.binding = binding;
		this.type = type;
		this.section = section;
		this.visibility = visibility;
		this.size = size;
	}

	public String getSymbolBinding() {
		return switch (binding) {
			case Elf.Symbol.STB_LOCAL -> "STB_LOCAL";
			case Elf.Symbol.STB_GLOBAL -> "STB_GLOBAL";
			case Elf.Symbol.STB_WEAK -> "STB_WEAK";
			default -> Integer.toString(binding);
		};
	}

	public String getSymbolType() {
		return switch (type) {
			case Elf.Symbol.STT_NOTYPE -> "STT_NOTYPE";
			case Elf.Symbol.STT_OBJECT -> "STT_OBJECT";
			case Elf.Symbol.STT_FUNC -> "STT_FUNC";
			case Elf.Symbol.STT_SECTION -> "STT_SECTION";
			case Elf.Symbol.STT_FILE -> "STT_FILE";
			case STT_COMMON -> "STT_COMMON";
			case STT_TLS -> "STT_TLS";
			case STT_LOOS -> "STT_LOOS";
			case STT_HIOS -> "STT_HIOS";
			case STT_LOPROC -> "STT_LOPROC";
			case STT_HIPROC -> "STT_HIPROC";
			default -> Integer.toString(type);
		};
	}

	public String getSection() {
		return switch (section) {
			case Elf.Symbol.SHN_UNDEF -> "SHN_UNDEF";
			case Elf.Symbol.SHN_ABS -> "SHN_ABS";
			case Elf.Symbol.SHN_COMMON -> "SHN_COMMON";
			default -> Integer.toString(section);
		};
	}

	public String getSymbolVisibility() {
		return switch (visibility) {
			case 0 -> "STV_DEFAULT";
			case 1 -> "STV_INTERNAL";
			case 2 -> "STV_HIDDEN";
			case 3 -> "STV_PROTECTED";
			default -> "Unknown (" + visibility + ")";
		};
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
			.add("number", number)
			.add("address", address)
			.add("name", name)
			.add("binding", binding)
			.add("type", type)
			.add("section", section)
			.add("visibility", visibility)
			.add("size", size)
			.toString();
	}
}
