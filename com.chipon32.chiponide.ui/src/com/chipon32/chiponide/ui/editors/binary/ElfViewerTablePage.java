
package com.chipon32.chiponide.ui.editors.binary;

import java.util.Iterator;

import org.eclipse.cdt.utils.elf.ElfHelper;
import org.eclipse.core.databinding.Binding;
import org.eclipse.core.databinding.DataBindingContext;
import org.eclipse.core.databinding.observable.value.IObservableValue;
import org.eclipse.core.databinding.observable.value.WritableValue;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.databinding.swt.DisplayRealm;
import org.eclipse.jface.layout.TableColumnLayout;
import org.eclipse.jface.viewers.ColumnWeightData;
import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.IContentProvider;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.SWT;
import org.eclipse.swt.dnd.Clipboard;
import org.eclipse.swt.dnd.TextTransfer;
import org.eclipse.swt.dnd.Transfer;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableColumn;
import org.eclipse.ui.actions.ActionFactory;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.chipon32.chiponide.core.utils.parser.ChipOnElf;

public abstract class ElfViewerTablePage extends ElfViewerPage {
	protected TableViewer viewer;
	private IObservableValue<ElfHelper> elfModelObserver;
	private Binding binding;

	protected ElfViewerTablePage(BinEditor editor, String id, String title, String description) {
		super(editor, id, title, description);
	}

	public Form createForm(Composite parent, FormToolkit toolkit) {
		super.createForm(parent, toolkit);
		Composite contents = form.getBody();
		toolkit.adapt(contents, false, false);
		GridData data = new GridData(4, 4, true, true);
		contents.setLayoutData(data);
		contents.setMenu(form.getBody().getMenu());
		TableColumnLayout tableLayout = new TableColumnLayout();
		contents.setLayout(tableLayout);
		elfModelObserver = new WritableValue<>() {
			private ElfHelper model;

			public void doSetValue(ElfHelper o) {
				model = o;
				super.doSetValue(o);
			}

			public Object getValueType() {
				return ElfHelper.class;
			}

			public ElfHelper doGetValue() {
				return model;
			}
		};
		createTableArea(toolkit, contents, tableLayout);
		form.layout(true);
		update();
		return form;
	}

	protected void createTableArea(FormToolkit toolkit, Composite tableParent, TableColumnLayout tableLayout) {
		Table table = toolkit.createTable(tableParent, getTableFlags() | SWT.BORDER | SWT.FULL_SELECTION);
		GridData data = new GridData(SWT.FILL, SWT.FILL, true, true);
		table.setLayoutData(data);
		viewer = new TableViewer(table);
		viewer.setContentProvider(getContentProvider());
		viewer.setLabelProvider(getLabelProvider());
		ViewerComparator comparator = getComparator();
		if (comparator != null) {
			viewer.setComparator(comparator);
		}

		addColumns(viewer, tableLayout);
		table.setHeaderVisible(true);
		IObservableValue<ElfHelper> tableObserver = new WritableValue<>() {
			public void doSetValue(ElfHelper value) {
				viewer.setInput(value);
				Object atZero = viewer.getElementAt(0);
				if (atZero != null) {
					viewer.setSelection(new StructuredSelection(atZero));
				}

				super.doSetValue(value);
			}
		};
		DataBindingContext dbc = new DataBindingContext(DisplayRealm.getRealm(Display.getDefault()));
		binding = dbc.bindValue(tableObserver, elfModelObserver, null, null);
	}

	protected int getTableFlags() {
		return SWT.MULTI;
	}

	protected abstract IContentProvider getContentProvider();

	protected abstract IBaseLabelProvider getLabelProvider();

	protected abstract void addColumns(TableViewer var1, TableColumnLayout var2);

	protected abstract ViewerComparator getComparator();

	protected abstract SelectionListener getSelectionAdapter(TableViewer var1, int var2);

	public void dispose() {
		DataBindingContext dbc = new DataBindingContext(DisplayRealm.getRealm(Display.getDefault()));
		dbc.removeBinding(binding);
	}

	protected void addTableColumn(TableViewer viewer, int index, String name, String tooltip, int weight) {
		Table table = viewer.getTable();
		TableColumn column = new TableColumn(table, SWT.NONE, index);
		column.setToolTipText(tooltip);
		SelectionListener selectionListener = getSelectionAdapter(viewer, index);
		if (selectionListener != null) {
			column.addSelectionListener(selectionListener);
		}

		column.setText(name);
		column.pack();
		int nameWidth = column.getWidth();
		TableColumnLayout layout = (TableColumnLayout) table.getParent().getLayout();
		layout.setColumnData(column, new ColumnWeightData(weight, nameWidth));
	}

	protected void update() {
		try {
			ChipOnElf chipOnElf = new ChipOnElf(getFile());
			ElfHelper helper = new ElfHelper(chipOnElf);
			elfModelObserver.setValue(helper);
			super.writeError(null);
		} catch (Exception e) {
			super.writeError(e);
		}
	}

	public IAction[] getActions() {
		Action copyAction = new Action(Messages.getString("BinEditorContributorClass.Copy")) {
			public String getId() {
				return ActionFactory.COPY.getId();
			}

			public void run() {
				Display.getDefault().asyncExec(() -> {
					String text = "";
					ISelection selection = viewer.getSelection();
					if (selection instanceof IStructuredSelection) {
						Iterator<?> it = ((IStructuredSelection) selection).iterator();

						StringBuilder b = new StringBuilder();
                        for(String sep = ""; it.hasNext(); sep = "\n") {
                            b.append(sep);
                            b.append(it.next().toString());
                        }

						text = b.toString();
					}

					if (!text.isEmpty()) {
						Clipboard clipboard = new Clipboard(Display.getDefault());
						TextTransfer textTransfer = TextTransfer.getInstance();
						Transfer[] transfers = new Transfer[] { textTransfer };
						Object[] data = new Object[] { text };
						clipboard.setContents(data, transfers);
						clipboard.dispose();
					}

				});
			}
		};
		return new Action[] { copyAction };
	}
}
