
package com.chipon32.chiponide.ui.editors.binary;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IURIEditorInput;
import org.eclipse.ui.forms.widgets.Form;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.chipon32.chiponide.ui.UiActivator;

public abstract class ElfViewerPage {
	private final String title;
	private final ImageDescriptor refreshIconImageDescriptor = UiActivator.getImageDescriptor("icons/refresh.png");
	private final BinEditor editor;
	protected Form form;

	protected ElfViewerPage(BinEditor editor, String id, String title, String description) {
		this.editor = editor;
		this.title = title;
	}

	public abstract void dispose();

	public boolean isActive() {
		return this.form != null;
	}

	public String getTitle() {
		return this.title;
	}

	public Form createForm(Composite parent, FormToolkit toolkit) {
		this.form = toolkit.createForm(parent);
		this.form.setText(this.title);
		Action refreshAction = new Action(Messages.getString("FromElfPage.Refresh"), 1) {
			public void runWithEvent(Event event) {
				ElfViewerPage.this.update();
			}

			public ImageDescriptor getImageDescriptor() {
				return refreshIconImageDescriptor;
			}

			public int getStyle() {
				return super.getStyle();
			}
		};
		this.form.getToolBarManager().add(refreshAction);
		this.form.getToolBarManager().update(true);
		toolkit.decorateFormHeading(this.form);
		this.form.setLayoutData(new GridData(16777216, 128, true, true));
		return this.form;
	}

	public void writeError(Exception value) {
		if (this.form != null) {
			if (value == null) {
				this.form.setMessage("");
			} else {
				this.form.setMessage(Messages.getString("FromElfPage.Error") + value.getMessage(), 3);
			}
		}

	}

	protected abstract void update();

	public abstract IAction[] getActions();

	protected String getFile() {
		IEditorInput ie = this.editor.getEditorInput();
		if (ie instanceof IURIEditorInput uie) {
			return uie.getURI().getPath();
		} else {
			return null;
		}
	}
}
