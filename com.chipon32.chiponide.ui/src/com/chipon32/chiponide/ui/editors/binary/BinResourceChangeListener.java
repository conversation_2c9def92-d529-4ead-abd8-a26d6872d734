package com.chipon32.chiponide.ui.editors.binary;

import org.eclipse.core.resources.IResourceChangeEvent;
import org.eclipse.core.resources.IResourceChangeListener;
import org.eclipse.core.resources.IResourceDelta;
import org.eclipse.core.resources.IResourceDeltaVisitor;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IPath;

public class BinResourceChangeListener implements IResourceChangeListener, IResourceDeltaVisitor {

	private final IPath myListeningPath;
	private final BinEditor listeningEditor;

	public BinResourceChangeListener(IPath resourcePath, BinEditor editor) {
		this.myListeningPath = resourcePath.makeAbsolute();
		this.listeningEditor = editor;
	}

	public void resourceChanged(IResourceChangeEvent event) {
		try {
			IResourceDelta delta = event.getDelta();
			if (delta != null) {
				delta.accept(this);
			}
		} catch (CoreException ignored) {
		}

	}

	public boolean visit(IResourceDelta delta) {
		IResourceDelta interestingDelta = delta.findMember(this.myListeningPath);
		if (interestingDelta == null) {
			return false;
		} else {
			IPath path = interestingDelta.getFullPath().makeAbsolute();
			if (path.equals(this.myListeningPath)) {
				switch (interestingDelta.getKind()) {
				case IResourceDelta.ADDED:
				case IResourceDelta.CHANGED:
				case IResourceDelta.REMOVED:
					this.listeningEditor.setClosed();
				default:
					this.listeningEditor.setDirty();
					break;
				}
			}

			return false;
		}
	}
}