
package com.chipon32.chiponide.ui.editors.binary.segments;

import org.eclipse.cdt.utils.elf.Elf;
import org.eclipse.jface.layout.TableColumnLayout;
import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.IContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Table;

import com.chipon32.chiponide.ui.editors.binary.BinEditor;
import com.chipon32.chiponide.ui.editors.binary.ElfViewerTablePage;
import com.chipon32.chiponide.ui.editors.binary.Messages;

public class SegmentsPage extends ElfViewerTablePage {
	private static final String TITLE = Messages.getString("SegmentsPage.Title");
	private static final String NUMBER = Messages.getString("SegmentsPage.Number");
	private static final String NUMBER_TOOLTIP = Messages.getString("SegmentsPage.NumberTooltip");
	private static final String VIRTUAL_ADDRESS = Messages.getString("SegmentsPage.VirtualAddress");
	private static final String VIRTUAL_ADDRESS_TOOLTIP = Messages.getString("SegmentsPage.VirtualAddressTooltip");
	private static final String PHYSICAL_ADDRESS = Messages.getString("SegmentsPage.PhysicalAddress");
	private static final String PHYSICAL_ADDRESS_TOOLTIP = Messages.getString("SegmentsPage.PhysicalAddressTooltip");
	private static final String MEM_SIZE = Messages.getString("SegmentsPage.MemSize");
	private static final String MEM_SIZE_TOOLTIP = Messages.getString("SegmentsPage.MemSizeTooltip");
	private static final String FILE_SIZE = Messages.getString("SegmentsPage.FileSize");
	private static final String FILE_SIZE_TOOLTIP = Messages.getString("SegmentsPage.FileSizeTooltip");
	private static final String TYPE = Messages.getString("SegmentsPage.Type");
	private static final String TYPE_TOOLTIP = Messages.getString("SegmentsPage.TypeTooltip");
	private static final String FLAGS = Messages.getString("SegmentsPage.Flags");
	private static final String FLAGS_TOOLTIP = Messages.getString("SegmentsPage.FlagsTooltip");
	private final SegmentComparator comparator = new SegmentComparator();

	public SegmentsPage(BinEditor editor, String id) {
		super(editor, id, TITLE, null);
	}

	protected SelectionAdapter getSelectionAdapter(final TableViewer viewer, final int index) {
		return new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				SegmentsPage.this.comparator.setColumn(index);
				int dir = SegmentsPage.this.comparator.getDirection();
				Table table = viewer.getTable();
				table.setSortColumn(table.getColumn(index));
				table.setSortDirection(dir);
				viewer.refresh();
			}
		};
	}

	protected IContentProvider getContentProvider() {
		return new ElfSegmentContentProvider();
	}

	protected IBaseLabelProvider getLabelProvider() {
		return new SegmentLabelProvider();
	}

	protected void addColumns(TableViewer viewer, TableColumnLayout layout) {
		this.addTableColumn(viewer, 0, NUMBER, NUMBER_TOOLTIP, 0);
		this.addTableColumn(viewer, 1, TYPE, TYPE_TOOLTIP, 20);
		this.addTableColumn(viewer, 2, VIRTUAL_ADDRESS, VIRTUAL_ADDRESS_TOOLTIP, 20);
		this.addTableColumn(viewer, 3, PHYSICAL_ADDRESS, PHYSICAL_ADDRESS_TOOLTIP, 20);
		this.addTableColumn(viewer, 4, MEM_SIZE, MEM_SIZE_TOOLTIP, 20);
		this.addTableColumn(viewer, 5, FILE_SIZE, FILE_SIZE_TOOLTIP, 20);
		this.addTableColumn(viewer, 6, FLAGS, FLAGS_TOOLTIP, 20);
	}

	protected ViewerComparator getComparator() {
		return this.comparator;
	}

	public static class SegmentComparator extends ViewerComparator {
		private int currentSortColumn = 0;
		private int direction = SWT.UP;

		public int getDirection() {
			return this.direction;
		}

		public void setColumn(int column) {
			if (column == this.currentSortColumn) {
				this.direction = this.direction == SWT.UP ? SWT.DOWN : SWT.UP;
			} else {
				this.currentSortColumn = column;
				this.direction = SWT.UP;
			}
		}

		public int compare(Viewer viewer, Object e1, Object e2) {
			SegmentItem segment1 = (SegmentItem) e1;
			Elf.PHdr original1 = segment1.original;
			SegmentItem segment2 = (SegmentItem) e2;
			Elf.PHdr original2 = segment2.original;
			long la;
			long lb;
			if (this.currentSortColumn == 0) {
				la = segment1.number;
				lb = segment2.number;
			} else if (this.currentSortColumn == 1) {
				la = original1.p_type;
				lb = original2.p_type;
			} else if (this.currentSortColumn == 2) {
				la = original1.p_vaddr.getValue().longValue();
				lb = original2.p_vaddr.getValue().longValue();
			} else if (this.currentSortColumn == 3) {
				la = original1.p_paddr.getValue().longValue();
				lb = original2.p_paddr.getValue().longValue();
			} else if (this.currentSortColumn == 4) {
				la = original1.p_memsz;
				lb = original2.p_memsz;
			} else if (this.currentSortColumn == 5) {
				la = original1.p_filesz;
				lb = original2.p_filesz;
			} else if (this.currentSortColumn == 6) {
				la = original1.p_flags;
				lb = original2.p_flags;
			} else {
				la = 0L;
				lb = 0L;
			}

			int result = Long.signum(la - lb);
			if (this.direction == SWT.DOWN) {
				result = -result;
			}

			return result;
		}
	}
}
