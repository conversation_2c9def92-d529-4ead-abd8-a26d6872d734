package com.chipon32.chiponide.ui.editors.binary.overview;

import org.eclipse.jface.layout.TableColumnLayout;
import org.eclipse.jface.viewers.IBaseLabelProvider;
import org.eclipse.jface.viewers.IContentProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.ViewerComparator;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.widgets.TableColumn;

import com.chipon32.chiponide.ui.editors.binary.BinEditor;
import com.chipon32.chiponide.ui.editors.binary.ElfViewerTablePage;
import com.chipon32.chiponide.ui.editors.binary.Messages;

public class BinOverviewPage extends ElfViewerTablePage {

	private static final String title = Messages.getString("BinOverviewPage.Title");

	public BinOverviewPage(BinEditor editor, String id) {
		super(editor, id, title, null);
	}

	protected IContentProvider getContentProvider() {
		return new ElfHeaderContentProvider();
	}

	protected IBaseLabelProvider getLabelProvider() {
		return new ElfHeaderLabelProvider();
	}

	protected ViewerComparator getComparator() {
		return null;
	}

	protected SelectionListener getSelectionAdapter(TableViewer viewer, int index) {
		return null;
	}

	protected void addColumns(TableViewer viewer, TableColumnLayout layout) {
		this.addTableColumn(viewer, 0, "", "", 0);
		this.addTableColumn(viewer, 0, "", "", 0);
	}

	protected void update() {
		super.update();

		for (TableColumn column : viewer.getTable().getColumns()) {
			column.pack();
		}
	}

}
