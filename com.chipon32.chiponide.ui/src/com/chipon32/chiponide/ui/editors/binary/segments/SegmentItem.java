package com.chipon32.chiponide.ui.editors.binary.segments;

import com.google.common.base.MoreObjects;

import org.eclipse.cdt.utils.elf.Elf;

public class SegmentItem {

	public final Elf.PHdr original;
	public final int number;
	public final String type;
	public final String virtualAddress;
	public final String physicalAddress;
	public final String memSize;
	public final String fileSize;
	public final String flags;

	public SegmentItem(Elf.PHdr original, int number, String type, String virtualAddress, String physicalAddress, String memSize, String fileSize, String flags) {
		this.original = original;
		this.number = number;
		this.type = type;
		this.virtualAddress = virtualAddress;
		this.physicalAddress = physicalAddress;
		this.memSize = memSize;
		this.fileSize = fileSize;
		this.flags = flags;
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
			.add("number", number)
			.add("type", type)
			.add("virtualAddress", virtualAddress)
			.add("physicalAddress", physicalAddress)
			.add("memSize", memSize)
			.add("fileSize", fileSize)
			.add("flags", flags)
			.toString();
	}
}
