package com.chipon32.chiponide.ui.editors;

import java.util.ArrayList;

import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.text.ITextViewer;
import org.eclipse.jface.text.Region;
import org.eclipse.jface.text.contentassist.CompletionProposal;
import org.eclipse.jface.text.contentassist.ICompletionProposal;
import org.eclipse.jface.text.contentassist.IContentAssistProcessor;
import org.eclipse.jface.text.contentassist.IContextInformation;
import org.eclipse.jface.text.contentassist.IContextInformationValidator;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.PlatformUI;
import org.eclipse.wb.swt.ResourceManager;

import com.chipon32.chiponide.ui.UiActivator;
import com.chipon32.chiponide.ui.preferences.ChiponPreferenceAssitant;
import com.chipon32.chiponide.ui.util.ContentProvider;


/**
 * Class for content assist. Create the content assist list.
 * 
 * <AUTHOR>
 * @since 25.02.2012
 */
public class ASMCompletionProcessor implements IContentAssistProcessor {

  //¹Ø¼ü×ÖÌáÊ¾
  static final char[] TRIGGER_TOKENS = new char[]{'.','a','b',
	   	'c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B',
	   	'C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'} ;

  /**
   * {@inheritDoc}
   */
  @Override
public ICompletionProposal[] computeCompletionProposals(ITextViewer viewer, int offset) {
    ITextSelection selection = (ITextSelection) viewer.getSelectionProvider().getSelection();
   
    int selectionOffset = offset;

    if (selection.getOffset() != offset) {
      selectionOffset = selection.getOffset();
    }

    String prefix = getPrefix(viewer, selectionOffset);
    Region region = new Region(selectionOffset - prefix.length(), prefix.length() + selection.getLength());
    
    ICompletionProposal[] keyWordsProposals = computeWordProposals(viewer, region, prefix, offset);
    return keyWordsProposals;
  }

  /**
   * Computes the word proposals.
   * 
   * @param viewer The viewer.
   * @param region The region.
   * @param prefix The prefix.
   * @param cursorOffset The offset of the cursor.
   * 
   * @return A list of the word proposal.
   */
  private ICompletionProposal[] computeWordProposals(ITextViewer viewer, Region region, String prefix, int cursorOffset) {
	if(!ChiponPreferenceAssitant.neenConstontAssitant()){
		return null;
	}
    ArrayList<ICompletionProposal> proposalList = new ArrayList<ICompletionProposal>();

    int offset = region.getOffset();
    int count = 0;
    char lastchar = (prefix.length() < 1) ? ' ' : prefix.charAt(prefix.length() - 1);
    boolean small = ((lastchar < 'a') || (lastchar > 'z')) ? false : true;
    String smprefix = prefix.toLowerCase();
    String item = "";
    int i = 0;
    
    
    
    ASMEditor editor = null ;
    String[] lablesString = null;
    String[] segmentString = null;
    IEditorPart editorPart = PlatformUI.getWorkbench().getActiveWorkbenchWindow().getActivePage().getActiveEditor();
    if(editorPart instanceof ASMEditor){
    	editor = (ASMEditor) editorPart;
    }
    if(editor != null){
    	ContentProvider cp = new ContentProvider(editor);
    	cp.parse();
    	TreeObject labels = cp.getLabels();
		TreeObject segments = cp.getSegments();
		Object[] obj =labels.getChildren();
		lablesString = new String[obj.length];
		Object[] segmentObj = segments.getChildren();
		segmentString = new String[segmentObj.length];
		for(int j=0;j<obj.length;j++){
			String string = obj[j].toString().trim();
			lablesString[j] = string;
		}
		for(int k=0;k<segmentObj.length;k++){
			String string = segmentObj[k].toString().trim();
			String[] strings = string.split("\\s+");
			if(strings != null && strings.length> 1){
				if(!"".equals(strings[1]) &&  strings[1] != null){
					segmentString[k] = strings[1];
				}else{
					segmentString[k] = string;
				}
			}else{
				segmentString[k] = string;
			}
			
			
		}
    } 
    
    
    if(lablesString.length > 0){
    	for(i=0;i<lablesString.length;i++){
    		if(lablesString[i].equals(prefix)){
    			continue;
    		}
    		if (lablesString[i].startsWith(prefix)) {
	    		item = lablesString[i];
	    		proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
			    				ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/16/comp_asm.gif"),
			                    item, null, null));
	    		count++;
    	    }
    	}
    }
    
    if(segmentString!= null && segmentString.length > 0){
    	for(i=0;i<segmentString.length;i++){
    		
    		if (segmentString[i].startsWith(prefix)) {
    		item = segmentString[i];
    		proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
    												ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/16/comp_asm.gif"),
								                    item, null, null));
    		count++;
    	 }
       }
    }
    
    

    String[][] instructions = ASMInstructionSet.getInstructionArray();

    if (instructions != null) {
      for (i = 0; i < instructions.length; i++) {
        if (instructions[i][1].startsWith(smprefix)) {
          item = small ? instructions[i][1] : instructions[i][0];
          proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
        		  								  ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/16/comp_asm.gif"),
        		  								  item + " - " + instructions[i][2], null, null));
          count++;
        }
      }
    }
    
    
    String[][] psduInstructions = ASMInstructionSet.getPseudoInstructionArray();

    if (psduInstructions != null) {
      for (i = 0; i < psduInstructions.length; i++) {
    	  if (psduInstructions[i][1].startsWith(smprefix)) {
          item = small ? psduInstructions[i][1] : psduInstructions[i][0];
          proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
        		  								  ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/16/comp_asm.gif"),
                                                  item + " - " + psduInstructions[i][2], null, null));
          count++;
        }
      }
    }
    
    

    String[][] segments = ASMInstructionSet.getSegmentArray();

    if (segments != null) {
      for (i = 0; i < segments.length; i++) {
    	  if (segments[i][1].startsWith(smprefix)) {
          item = small ? segments[i][1] : segments[i][0];
          proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
        		  								  ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/bit.gif"),
                                                  item + " - " + segments[i][2], null, null));
          count++;
        }
      }
    }
    
    String[][] registers = ASMInstructionSet.getRegisterArray();

    if (registers != null) {
      for (i = 0; i < registers.length; i++) {
    	  if (registers[i][1].startsWith(smprefix)) {
          item = small ? registers[i][1] : registers[i][0];
          proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
        		  								  ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/register_view.gif"),
                                                  item + " - " + registers[i][2], null, null));
          count++;
        }
      }
    }
    
    
    String[][] specialRegisters = ASMInstructionSet.getSortedSpecialRegisterArray();

    if (specialRegisters != null) {
      for (i = 0; i < specialRegisters.length; i++) {
    	  if (specialRegisters[i][1].startsWith(smprefix)) {
          item = small ? specialRegisters[i][1] : specialRegisters[i][0];
          proposalList.add(new CompletionProposal(item, offset, region.getLength(), item.length(), 
        		  								  ResourceManager.getPluginImage(UiActivator.PLUGIN_ID, "/icons/register_view.gif"),
                                                  item + " - " + specialRegisters[i][2], null, null));
          count++;
        }
      }
    }
        
    if (count < 1) {
      return null;
    }

   
    return proposalList.toArray(new ICompletionProposal[0]);
  }
  
  

  /**
   * Resturn the prefix.
   * 
   * @param viewer The viewer.
   * @param offset The offset.
   * 
   * @return The prefix.
   */
  private String getPrefix(ITextViewer viewer, int offset) {
    int i = offset;
    IDocument document = viewer.getDocument();

    if (i > document.getLength()) {
      return "";
    }

    try {
      while (i > 0) {
        char ch = document.getChar(i - 1);

        if (ch <= ' ' || ch == ',') {
          break;
        }

        i--;
      }

      return document.get(i, offset - i);
    } catch (BadLocationException e) {
      return "";
    }
  }

  /**
   * {@inheritDoc}
   */
  @Override
public IContextInformation[] computeContextInformation(ITextViewer viewer, int offset) {
    return null;
  }

  /**
   * {@inheritDoc}
   */
  @Override
public char[] getCompletionProposalAutoActivationCharacters() {
    return TRIGGER_TOKENS;
  }

  /**
   * {@inheritDoc}
   */
  @Override
public char[] getContextInformationAutoActivationCharacters() {
    return null;
  }

  /**
   * {@inheritDoc}
   */
  @Override
public String getErrorMessage() {
    return null;
  }

  /**
   * {@inheritDoc}
   */
  @Override
public IContextInformationValidator getContextInformationValidator() {
    return null;
  }
}
