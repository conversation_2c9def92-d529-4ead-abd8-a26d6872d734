package com.chipon32.chiponide.ui.util;

import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.graphics.RGB;
import org.eclipse.swt.graphics.Rectangle;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.Monitor;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.PlatformUI;
import org.eclipse.wb.swt.ResourceManager;

public class UIUtil {
	
	public static MenuManager createContextMenu(Control control) {
		MenuManager menuManager = new MenuManager("#PopupMenu");
		Menu menu = menuManager.createContextMenu(control);
		control.setMenu(menu);
		return menuManager;
	}
	
	public static Point getPrimaryMonitorSize() {
		Monitor monitor = Display.getDefault().getPrimaryMonitor();
		Rectangle bound = monitor.getBounds();
		return new Point(bound.width, bound.height);
	}
	
	public static void decorateTableRows(Table table, RGB evenRow, RGB oddRow) {
		for (int i = 0; i < table.getItemCount(); ++i) {
			TableItem tableItem  = table.getItem(i);
			if (i % 2 == 0) {
				tableItem.setBackground(ResourceManager.getColor(evenRow));
			} else {
				tableItem.setBackground(ResourceManager.getColor(oddRow));
			}
		}
	}
	
	public static void decorateTableColumns(Table table, RGB evenColumn, RGB oddColumn) {
		for (TableItem tableItem : table.getItems()) {
			for (int i = 0; i < table.getColumnCount(); ++i) {
				if (i % 2 == 0) {
					tableItem.setBackground(i,ResourceManager.getColor(evenColumn));
				} else {
					tableItem.setBackground(i, ResourceManager.getColor(oddColumn));
				}
			}
		}
	}
	
	public static void setDefaultProperties(TableViewer tableViewer) {
		int count = tableViewer.getTable().getColumnCount();
		String[] properties = getDefaultProperties(count);
		tableViewer.setColumnProperties(properties);
	}
	
	public static void setDefaultProperties(TreeViewer treeViewer) {
		int count = treeViewer.getTree().getColumnCount();
		String[] properties = getDefaultProperties(count);
		treeViewer.setColumnProperties(properties);
	}
	
	private static String[] getDefaultProperties(int count) {
		String[] properties = new String[count];
		for (int i = 0; i < count; ++i) {
			properties[i] = "column_" + i;
		}
		return properties;
	}
	
	public static int getPropertyColumnIndex(TableViewer tableViewer, String property) {
		Object[] properties = tableViewer.getColumnProperties();
		return getPropertyColumnIndex(properties, property);
	}
	
	public static int getPropertyColumnIndex(TreeViewer treeViewer, String property) {
		Object[] properties = treeViewer.getColumnProperties();
		return getPropertyColumnIndex(properties, property);
	}
	
	private static int getPropertyColumnIndex(Object[] properties, String property) {
		for (int i = 0; i < properties.length; ++i) {
			if (properties[i].equals(property)) {
				return i;
			}
		}
		return -1;
	}
	
	public static void openEditor(IEditorInput input, String editorId) throws PartInitException {
		IWorkbenchPage page = PlatformUI.getWorkbench()
				.getActiveWorkbenchWindow().getActivePage();
		page.openEditor(input, editorId);
	}
}
