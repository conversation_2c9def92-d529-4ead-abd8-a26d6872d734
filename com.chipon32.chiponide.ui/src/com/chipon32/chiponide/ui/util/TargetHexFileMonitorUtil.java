package com.chipon32.chiponide.ui.util;

import org.eclipse.cdt.internal.core.model.CContainer;
import org.eclipse.cdt.managedbuilder.core.IConfiguration;
import org.eclipse.cdt.managedbuilder.core.IManagedBuildInfo;
import org.eclipse.cdt.managedbuilder.core.ManagedBuildManager;
import org.eclipse.cdt.managedbuilder.macros.BuildMacroException;
import org.eclipse.cdt.managedbuilder.macros.IBuildMacroProvider;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IFolder;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.IResource;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;

/**
 * 目标机平台 hex文件 监控 龙套
 */
public class TargetHexFileMonitorUtil {

	/**
	 * 基于焦点 获取当前的工程
	 */
	public IProject getProjectFromSelection(ISelection selection) {
		IProject lProject = null;
		if (selection instanceof IStructuredSelection sel) {
			if (!sel.isEmpty()) {
				if (sel.getFirstElement() instanceof IResource res) {
					lProject = res.getProject();
				} else if (sel.getFirstElement() instanceof IFile file) {
					lProject = file.getProject();
				} else if (sel.getFirstElement() instanceof IFolder file) {
					lProject = file.getProject();
				} else if (sel.getFirstElement() instanceof CContainer container) {
					lProject = container.getResource()
						.getProject();
				} else if (sel.getFirstElement() instanceof IProject) {
					lProject = (IProject) sel.getFirstElement();
				}
			}
		}
		return lProject;
	}

	// 基于当前工程 获取对应的文件
	public static IFile getBuildTargetFileFromProject(IProject vProject) {
		IFile lFile = null;
		if (vProject != null && vProject.exists()) {
			IManagedBuildInfo bi = ManagedBuildManager.getBuildInfo(vProject);
			if (bi != null) {
				IConfiguration buildcfg = bi.getDefaultConfiguration();
				String cfgName = buildcfg.getName();
				IBuildMacroProvider provider = ManagedBuildManager.getBuildMacroProvider();
				try {
					String buildArtifactName = provider.getMacro("BuildArtifactFileBaseName", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg, true)
						.getStringValue();
					buildArtifactName = provider.resolveValue(buildArtifactName, "", " ", IBuildMacroProvider.CONTEXT_CONFIGURATION, buildcfg);
					String fileName = buildArtifactName + "." + "hex"; // buildcfg.getArtifactExtension()
					String fileName2 = buildArtifactName + "." + "s19";
					IFile tmpFile = vProject.getFile(cfgName + "/" + fileName);
					if (tmpFile != null && tmpFile.exists()) {
						lFile = tmpFile;

					} else {
						tmpFile = vProject.getFile(cfgName + "/" + fileName2);
						if (tmpFile != null && tmpFile.exists()) {
							lFile = tmpFile;
						}
					}
				} catch (BuildMacroException e) {
					e.printStackTrace();
				}
			}
		} else {
			return null;
		}
		return lFile;
	}

}
