package com.chipon32.chiponide.ui.util;

import org.eclipse.osgi.util.NLS;

public class Messages extends NLS {
	private static final String BUNDLE_NAME = "com.chipon32.chiponide.ui.util.messages"; //$NON-NLS-1$
	public static String ChiponUpdateFirmware_11;
	public static String ChiponUpdateFirmware_13;
	public static String ChiponUpdateFirmware_14;
	public static String ChiponUpdateFirmware_6;
	public static String ChiponUpdateFirmware_9;
	public static String FilteredTable_0;
	public static String FilteredTree_0;
	static {
		// initialize resource bundle
		NLS.initializeMessages(BUNDLE_NAME, Messages.class);
	}

	private Messages() {
	}
}
