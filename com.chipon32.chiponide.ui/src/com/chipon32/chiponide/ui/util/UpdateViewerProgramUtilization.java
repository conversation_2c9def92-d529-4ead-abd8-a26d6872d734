package com.chipon32.chiponide.ui.util;

import java.util.List;
import java.util.Map;

//import org.eclipse.ui.IViewPart;
//import org.eclipse.ui.IWorkbenchPage;
//import org.eclipse.ui.IWorkbenchWindow;
//import org.eclipse.ui.PlatformUI;
//import com.chipon32.chiponide.ui.views.ProgramUtilization;
import com.chipon32.hex.core.util.IConfigurationProvider;
//  侧边关键信息的显示  
public class UpdateViewerProgramUtilization {
	//  更新查看器的方法，参数： MAP，信息提供者
	public static void UpdateViewer(Map<String, List<?>> readMap,IConfigurationProvider provider) 
	{
		
//		ProgramUtilization pu = null;
//		IViewPart view = null;
//		IWorkbenchPage page = null;
		// 活动工作台
//		IWorkbenchWindow window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
		// 活动页
//		if(window != null){
//			page = window.getActivePage();
//		}
		// 查看器，找到资源显示器
//		if(page != null){
//			view = page.findView("com.chipon32.chiponide.ui.views.ProgramUtilization");
//		}
		//  设计的特定编程需要信息
//		if(null != view && view instanceof ProgramUtilization){
//			pu = (ProgramUtilization) view;
//		}
//		//  具体看最后如何设计，如直接传递进ProgramUtilization，设计ProgramUtilization的方法完成更新
//		List<?> deviceIDList = readMap.get("deviceid");
//		
//		if(deviceIDList != null && deviceIDList.size()>1 && pu != null){
//			pu.getComponent().setDeviceID_Label_VALUE(deviceIDList.get(1).toString()+deviceIDList.get(0).toString());
//		}
	}
}
