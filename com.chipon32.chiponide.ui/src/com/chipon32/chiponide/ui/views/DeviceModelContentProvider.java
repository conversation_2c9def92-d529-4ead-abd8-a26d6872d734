/*******************************************************************************
 * 
 * Copyright (c) 2007 <PERSON> (<EMAIL>) and others
 * 
 * This program and the accompanying materials are made
 * available under the terms of the GNU Public License v3
 * which accompanies this distribution, and is available at
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Contributors:
 *     Thomas Holland - initial API and implementation
 *     
 * $Id: DeviceModelContentProvider.java,v 1.1 2013/11/21 06:41:09 zhangji Exp $
 *     
 *******************************************************************************/
package com.chipon32.chiponide.ui.views;

import org.eclipse.core.runtime.Assert;
import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.Viewer;

import com.chipon32.chiponide.core.chipondescription.ICategory;
import com.chipon32.chiponide.core.chipondescription.IEntry;

public class DeviceModelContentProvider implements ITreeContentProvider {

	private ICategory	fCategory	= null;

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.eclipse.jface.viewers.IContentProvider#inputChanged(org.eclipse.jface.viewers.Viewer,
	 *      java.lang.Object, java.lang.Object)
	 */
	@Override
	public void inputChanged(Viewer viewer, Object oldInput, Object newInput) {
		fCategory = (ICategory) newInput;
	}

	@Override
	public Object[] getElements(Object inputElement) {
		return fCategory.getChildren().toArray();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.eclipse.jface.viewers.ITreeContentProvider#getChildren(java.lang.Object)
	 */
	@Override
	public Object[] getChildren(Object parentElement) {
		Assert.isTrue(parentElement instanceof IEntry);
		return ((IEntry) parentElement).getChildren().toArray();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.eclipse.jface.viewers.ITreeContentProvider#getParent(java.lang.Object)
	 */
	@Override
	public Object getParent(Object element) {
		Assert.isTrue(element instanceof IEntry);
		return ((IEntry) element).getParent();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.eclipse.jface.viewers.ITreeContentProvider#hasChildren(java.lang.Object)
	 */
	@Override
	public boolean hasChildren(Object element) {
		Assert.isTrue(element instanceof IEntry);
		return ((IEntry) element).hasChildren();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.eclipse.jface.viewers.IContentProvider#dispose()
	 */
	@Override
	public void dispose() {
		// Nothing to dispose.
	}

}
