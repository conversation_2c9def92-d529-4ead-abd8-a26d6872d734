package com.chipon32.chiponide.ui.views;

public class UtilizationViewerSizeGetParse {

	
	//__Logic_Stack_End__ : 10000000	1f800
	public String __Logic_Stack_End__;
	public long   __Logic_Stack_End__Where=-1;
	public long   __Logic_Stack_End__Value=-1;
	
	//__Logic_Stack_Start__ : 10000000   20000
	public String __Logic_Stack_Start__;
	public long   __Logic_Stack_Start__Where=-1;
	public long   __Logic_Stack_Start__Value=-1;

	//__MAX_Stack_LIMITS__ : 10000000	108
	public String __MAX_Stack_LIMITS__;
	public long   __MAX_Stack_LIMITS__Where=-1;
	public long   __MAX_Stack_LIMITS__Value=-1;
	
	
	//__initial_sp : 10000000	20000
	public String __initial_sp;
	public long   __initial_spWhere=-1;
	public long   __initial_spValue=-1;
	
	//__Heap_Start__ : 10000000	 8
	//__Heap_End__ : 10000000   108
	public String __Heap_Start__;
	public long   __Heap_Start__Where=-1;
	public long   __Heap_Start__Value=-1;
	
	public String __Heap_End__;
	public long   __Heap_End__Where=-1;
	public long   __Heap_End__Value=-1;
	
	//__data_start__ : 10000000   0
	//__data_end__ : 10000000	 4
	public String __data_start__;
	public long   __data_start__Where=-1;
	public long   __data_start__Value=-1;
	
	public String __data_end__;
	public long   __data_end__Where=-1;
	public long   __data_end__Value=-1;
	
	//__bss_start__ : 10000000   4
	//__bss_end__ : 10000000	8
	public String __bss_start__;
	public long   __bss_start__Where=-1;
	public long   __bss_start__Value=-1;
	
	public String __bss_end__;
	public long   __bss_end__Where=-1;
	public long   __bss_end__Value=-1;
		
	//__text_info__ : 0-0-708
	//__text_end__ : 0	2c4
	public String __text_info__;
	public long   __text_info__Where=-1;
	public long   __text_info__Value=-1;
	public long   __text_info__size=-1;
	
	public String __text_end__;
	public long   __text_end__Where=-1;
	public long   __text_end__Value=-1;
	//__data_info__ : 268435456-268435456-131072
	public String __data_info__;
	public long   __data_info__Where=-1;
	public long   __data_info__Value=-1;
	public long   __data_info__size=-1;

	
	//__bss_info__ : 268435456-268435456-0
	public String __bss_info__;
	public long   __bss_info__Where=-1;
	public long   __bss_info__Value=-1;
	public long   __bss_info__size=-1;

	
	public void ParseInputString(String line)
	{
		//13k项目空值情况处理
		if(line.isEmpty()) {
			return;
		}
		line = line.replace("--", "-");  //1879048192--1610611840-0   13K项目
		
		String bufferd;
		if(line.contains("__Heap_Start__"))			{
			//__Heap_Start__ : 10000000	 8
			__Heap_Start__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__Heap_Start__Where =Long.parseLong(datas[0],16);
				__Heap_Start__Value =Long.parseLong(datas[1],16);
			}								
		}
		else if(line.contains("__Heap_End__"))		{
			__Heap_End__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__Heap_End__Where =Long.parseLong(datas[0],16);
				__Heap_End__Value =Long.parseLong(datas[1],16);
			}	
		}
		else if(line.contains("__MAX_Stack_LIMITS__"))		{
			__MAX_Stack_LIMITS__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__MAX_Stack_LIMITS__Where =Long.parseLong(datas[0],16);
				__MAX_Stack_LIMITS__Value =Long.parseLong(datas[1],16);
			}
		}							
		else if(line.contains("__Logic_Stack_End__"))		{
			__Logic_Stack_End__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__Logic_Stack_End__Where =Long.parseLong(datas[0],16);
				__Logic_Stack_End__Value =Long.parseLong(datas[1],16);
			}
		}							
		else if(line.contains("__Logic_Stack_Start__"))		{
			__Logic_Stack_Start__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__Logic_Stack_Start__Where =Long.parseLong(datas[0],16);
				__Logic_Stack_Start__Value =Long.parseLong(datas[1],16);
			}
		}							
		else if(line.contains("__data_start__"))		{
			__data_start__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__data_start__Where =Long.parseLong(datas[0],16);
				__data_start__Value =Long.parseLong(datas[1],16);
			}
		}	
		else if(line.contains("__data_end__"))		{
			__data_end__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__data_end__Where =Long.parseLong(datas[0],16);
				__data_end__Value =Long.parseLong(datas[1],16);
			}
		}	
		else if(line.contains("__bss_start__"))		{
			__bss_start__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__bss_start__Where =Long.parseLong(datas[0],16);
				__bss_start__Value =Long.parseLong(datas[1],16);
			}
		}	
		else if(line.contains("__bss_end__"))		{
			__bss_end__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__bss_end__Where =Long.parseLong(datas[0],16);
				__bss_end__Value =Long.parseLong(datas[1],16);
			}
		}								
		else if(line.contains("__initial_sp"))		{
			__initial_sp=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__initial_spWhere =Long.parseLong(datas[0],16);
				__initial_spValue =Long.parseLong(datas[1],16);
			}
		}	
		else if(line.contains("__text_end__ "))		{
			__text_end__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("\\s+");
			if(datas.length>1)
			{
				__text_end__Where =Long.parseLong(datas[0],16);
				__text_end__Value =Long.parseLong(datas[1],16);
			}
		}
		//----------------------------
		else if(line.contains("__text_info__ "))		{
			//__text_info__ : 0-0-708
			__text_info__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			if(bufferd.startsWith("-")) {
				bufferd = bufferd.substring(1);
			}
			
			String datas[]=bufferd.split("-");
			if(datas.length>2)
			{
				__text_info__Where =Long.parseLong(datas[0],10);
				__text_info__Value =Long.parseLong(datas[1],10);
				__text_info__size  =Long.parseLong(datas[2],10);
			}
		}	
		else if(line.contains("__data_info__"))		{
			//__data_info__ : 268435456-268435456-131072
			__data_info__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("-");
			if(datas.length>2)
			{
				__data_info__Where =Long.parseLong(datas[0],10);
				__data_info__Value =Long.parseLong(datas[1],10);
				__data_info__size  =Long.parseLong(datas[2],10);
			}
		}	
		else if(line.contains("__bss_info__"))		{
			__bss_info__=line;
			bufferd=line.substring(line.indexOf(":")+1).trim();
			String datas[]=bufferd.split("-");
			if(datas.length>2)
			{
				__bss_info__Where =Long.parseLong(datas[0],10);
				__bss_info__Value =Long.parseLong(datas[1],10);
				__bss_info__size  =Long.parseLong(datas[2],10);
			}
		}	
	}
	
	int getRamUsed()
	{
		long buf=0;
		if(__data_start__Where != -1 && __data_end__Where !=1 )
		{
			buf += (__data_end__Value-__data_start__Value);
		}
		if(__bss_start__Where != -1 && __bss_end__Where !=1 )
		{
			buf += (__bss_end__Value-__bss_start__Value);
		}
		if(__Heap_Start__Where != -1 && __Heap_End__Where !=1 )
		{
			buf += (__Heap_End__Value-__Heap_Start__Value);
		}	
		if(__data_info__Where != -1)
		{
			
		}
		//+++++++++++++++++++++++++++++++++++++++++++++++
		if(__data_start__Where != -1 && __data_end__Where !=1 )
			return (int) buf;
		//+++++++++++++++++++++++++++++++++++++++++++++++
		return -1;
	}

	int getFlashUsed()
	{
		long buf=0;
		// flash size org
		if(__text_end__Where != -1)
			buf += __text_end__Value;
		// add data
		if(__data_start__Where != -1 && __data_end__Where !=1 )
		{
			buf += (__data_end__Value-__data_start__Value);
		}			
		// 
		if(__text_info__Where != -1)
		{
			
		}	
		//+++++++++++++++++++++++++++++++++++++++++++++++
		if(__text_end__Where != -1)
			return (int) buf;
		//+++++++++++++++++++++++++++++++++++++++++++++++		
		return -1;
	}
}
