package com.chipon32.debug.core.protocol;

import java.util.List;

/**
 * 
 * <AUTHOR>	2017-4-24 	Ìí¼Ó¼à¿Øµã
 *
 */

public class ChiponSetWatchpointCommand extends ChiponCommand {
	
	public ChiponSetWatchpointCommand(String command) {
		super(command);
	}

	@Override
	public ChiponSetWatchpointCommandResult createResult(List<String> resultText) {
		return new ChiponSetWatchpointCommandResult(resultText);
	}

}
