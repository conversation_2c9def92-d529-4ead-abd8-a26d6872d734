package com.chipon32.debug.core.protocol;


import java.util.List;

/***
 *<AUTHOR>
 *2013-6-20обнГ2:21:46
 ***/
public class ChiponPrintCurrencyRegisterCommand extends ChiponCommand {

	/**/
	 
	public ChiponPrintCurrencyRegisterCommand(String string) {
		
		super(string);
		
	//	 super("printee$3952$128");
	//	super("print$ram$");
	//	super("print$local$");
	}

	@Override
	public ChiponCommandResult createResult(List<String> resultText) {
		return new ChiponPrintCurrencyRegisterCommandResult(resultText);
	}

}
