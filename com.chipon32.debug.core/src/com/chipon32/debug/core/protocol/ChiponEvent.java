package com.chipon32.debug.core.protocol;


import org.eclipse.jface.action.IStatusLineManager;
import org.eclipse.jface.action.StatusLineContributionItem;
import org.eclipse.ui.IActionBars;
import org.eclipse.ui.internal.WorkbenchWindow;

import com.chipon32.debug.core.DebugCoreActivator;

@SuppressWarnings("restriction")
public class ChiponEvent {

	public final String fMessage;
    public final String fName;
    
    public ChiponEvent(String message) {
        fMessage = message;
        fName = getName(message);
    } 
     
    protected String getName(String message) {
        int nameEnd = message.indexOf(' ');  //返回字符串中第一次出现空格的位置
        nameEnd = nameEnd == -1 ? message.length() : nameEnd;
        return message.substring(0, nameEnd);
    } 
     
    /**
     * @param message
     * @return
     */
    public static ChiponEvent parseEvent(final String message) {
    	//##################################################### 
    	if(ChiponRunEvent.isEventMessage(message)){  // run    		
    		return new ChiponRunEvent(message);
    	}
    	//##################################################### 
        else if (ChiponExitedEvent.isEventMessage(message)) {//exited
            return new ChiponExitedEvent(message);
        } 
    	//##################################################### 
        else if (ChiponStartedEvent.isEventMessage(message)) {//started
            return new ChiponStartedEvent(message);
        }
    	//#######################################################
        else if(ChiponBreakpointEvent.isEventMessage(message)) {//suspended$breadpoint
        	return new ChiponBreakpointEvent(message);
        }
    	//##################################################### 
        else if (ChiponSuspendedEvent.isEventMessage(message)) {//suspended
        	
        	DebugCoreActivator.getDefault().getActiveDisplay().asyncExec(new Runnable() {
				@Override
				public void run() {
//					List<String> list = ChiponMessageParse.parseEventMessage(message);   //以$符分割字符串
//					String power = "";
//					if(list.size() > 2){
//						power = list.get(2);
//					}
					WorkbenchWindow workbenchWindow = (WorkbenchWindow) DebugCoreActivator.getDefault().getWorkbench().getActiveWorkbenchWindow();
					workbenchWindow.setStatusLineVisible(true);
					IActionBars bars = workbenchWindow.getActionBars();
					IStatusLineManager lineManager = bars.getStatusLineManager();    //状态栏的数据显示
					lineManager.remove("ChiponPowerStatusItem");  
					StatusLineContributionItem statusItem = new StatusLineContributionItem("ChiponPowerStatusItem", 50);
//					statusItem.setText("调试实时电压检测："+power+"V");
					statusItem.setText("");
					lineManager.add(statusItem);
					lineManager.update(true);
				}
			});
            return new ChiponSuspendedEvent(message);
        }
    	//##################################################### 
        else if (ChiponTerminatedEvent.isEventMessage(message)) {// stoped$client, terminate debug
        	
        	    DebugCoreActivator.getDefault().getActiveDisplay().asyncExec(new Runnable() {
				
				@Override
				public void run() {
					WorkbenchWindow workbenchWindow = (WorkbenchWindow) DebugCoreActivator.getDefault().getWorkbench().getActiveWorkbenchWindow();
					workbenchWindow.setStatusLineVisible(true);
					IActionBars bars = workbenchWindow.getActionBars();
					IStatusLineManager lineManager = bars.getStatusLineManager();
					lineManager.remove("ChiponPowerStatusItem");
					lineManager.update(true);
				}
			});
            return new ChiponTerminatedEvent(message);
        }
    	//##################################################### 
        else if(ChiponDebuggerRunEvent.isEventMessage(message)){//debuggerrun
        	return new ChiponDebuggerRunEvent(message);
        }
    	//##################################################### 
        else if (ChiponDebuggerStartedEvent.isEventMessage(message)) {//debuggerStarted
            return new ChiponDebuggerStartedEvent(message);
        } 
    	//##################################################### 
        else if (ChiponDebuggerSuspendedEvent.isEventMessage(message)) {//debuggersuspended
            return new ChiponDebuggerSuspendedEvent(message);
        }
    	//##################################################### 
        else {
            return new ChiponEvent(message);
        } 
    	//##################################################### 
    }
}
