package com.chipon32.debug.core.protocol;


import java.util.List;

/**
 * 发送修改变量的命令
 * <AUTHOR> @since 2013-7-3 下午2:49:38
 */
public class ChiponSetVariableCommand extends ChiponCommand {

	public ChiponSetVariableCommand(String variable, String expression) {
		
		super("set var "+ variable+"="+expression);
	}

	@Override
	public ChiponSetVariableCommandResult createResult(List<String> resultText) {
		return new ChiponSetVariableCommandResult(resultText);
	}

}
