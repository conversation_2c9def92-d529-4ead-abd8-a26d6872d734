package com.chipon32.debug.core.protocol;

import java.util.List;

import com.chipon32.debug.core.util.ChiponMessageParse;

public class ChiponRunControlEvent extends ChiponEvent{

	 public final String fReason;
	
	public ChiponRunControlEvent(String message) {
		super(message);
		//fThreadId = getThreadId(message);
        fReason = getStateChangeReason(message);
	}
	
	/*protected int getThreadId(String message) {
        int nameEnd = getName(message).length();
        if ( Character.isDigit(message.charAt(nameEnd + 1)) ) {
            int threadIdEnd = message.indexOf(' ', nameEnd + 1);
            threadIdEnd = threadIdEnd == -1 ? message.length() : threadIdEnd;
            try {
                return Integer.parseInt(message.substring(nameEnd + 1, threadIdEnd));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid event: " + message);                
            }
        } else {
            return -1;
        }
    }*/

    protected String getStateChangeReason(String message) {
    	List<String> list = ChiponMessageParse.parseEventMessage(message);
    	if(list.size()>1){
    		return (String) list.toArray()[1];
    	}
        return null;
    }
    
    @Override
	protected String getName(String message) {
        int nameEnd = message.indexOf(' ');
        nameEnd = nameEnd == -1 ? message.length() : nameEnd;
        return message.substring(0, nameEnd);
    }

    public static boolean isEventMessage(String message) {
        return message.startsWith("started");
    }
}
