package com.chipon32.debug.core.protocol;


import java.util.List;

/***
 *<AUTHOR>
 ***/
public class ChiponPrintResetCommand extends ChiponCommand {

	public ChiponPrintResetCommand() {
		super("kfreset");
		// TODO Auto-generated constructor stub
	}

	@Override
	public ChiponStopLocationCommandResult createResult(List<String> resultText) {
		// TODO Auto-generated method stub
		return new ChiponStopLocationCommandResult(resultText);
	}

}
