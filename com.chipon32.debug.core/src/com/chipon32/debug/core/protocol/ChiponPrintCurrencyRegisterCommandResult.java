package com.chipon32.debug.core.protocol;

import java.util.ArrayList;
import java.util.List;

import com.chipon32.debug.core.model.ChiponCurrencyRegisterData;

/***
 *<AUTHOR>
public class ChiponPrintCurrencyRegisterCommandResult extends ChiponCommandResult {
	
	final public List<ChiponCurrencyRegisterData> dataList;

	public ChiponPrintCurrencyRegisterCommandResult(List<String> result) {
		// 解析通信对错
		super(result);
		List<ChiponCurrencyRegisterData> eeDatas=new ArrayList<ChiponCurrencyRegisterData>();
		for(String str: result){ // 通用寄存器结果
			if(str.contains("0x")){		
				String[] datalist = str.split(":?[\\s]+"); // 任意字符的空白分割，空白包括空格和制表符
//				if(datalist[0].equals("PSW")){
//					continue;
//				}
				ChiponCurrencyRegisterData eeData = new ChiponCurrencyRegisterData(datalist[0], datalist[1], datalist[2]);
				eeDatas.add(eeData);
			}
		}
		dataList = eeDatas;
	}
}
