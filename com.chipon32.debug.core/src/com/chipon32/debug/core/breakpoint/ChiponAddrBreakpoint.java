package com.chipon32.debug.core.breakpoint;

import java.util.HashMap;

import org.eclipse.core.resources.IMarker;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.resources.IWorkspaceRunnable;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.Breakpoint;
import org.eclipse.debug.core.model.DebugElement;
import org.eclipse.debug.core.model.IBreakpoint;
import org.eclipse.swt.graphics.Image;
import org.eclipse.wb.swt.ResourceManager;

import com.chipon32.debug.core.DebugCoreActivator;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.debug.core.model.IChiponEventListener;
import com.chipon32.debug.core.protocol.ChipOnClearBreakpointCommand;
import com.chipon32.debug.core.protocol.ChiponBreakpointCommand;
import com.chipon32.debug.core.protocol.ChiponBreakpointCommandResult;
import com.chipon32.debug.core.protocol.ChiponDebuggerSuspendedEvent;
import com.chipon32.debug.core.protocol.ChiponEvent;
import com.chipon32.debug.core.protocol.ChiponRunControlEvent;
import com.chipon32.debug.core.protocol.ChiponSuspendedEvent;
import com.chipon32.util.communicate.HandleError;

public class ChiponAddrBreakpoint extends Breakpoint implements IChiponEventListener, ChiponBreakPoint {
	private ChiponDebugTarget fTarget;
	
	private String number = "";      //断点编号，删除断点时需要用到
	
	protected IResource fSourceFile;
	
	private boolean isSuccess = false;	//设置断点是否成功标识
	
	private String address;
	private String condition;

	public boolean isSuccess() {
		return isSuccess;
	}

	public void setSucess(boolean isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getAddress() {
		if(address == null){
			IMarker m = getMarker();
			if (m != null) {
				return m.getAttribute(IMarker.LOCATION, null);
			}
			return null;
		}
		return address;
	}
	
	public String getCondition(){
		if(condition==null) {
			IMarker m = getMarker();
			if (m != null) {
				return m.getAttribute("condition", "");
			}else {
				return "";
			}
		}
		
		return condition;
	}
	
	public ChiponAddrBreakpoint() {
		
	}
	
	public ChiponAddrBreakpoint(final IResource resource, final int lineNumber, final String addr, final String breakpointCondition) throws DebugException{
		final HashMap<String, Object> attributes = new HashMap<String, Object>(10);	
		//添加断点的基础信息，保存
		attributes.put(IBreakpoint.ENABLED, Boolean.TRUE);
		attributes.put(IBreakpoint.ID, getModelIdentifier());
		attributes.put(IMarker.LOCATION, addr);
		attributes.put(IMarker.LINE_NUMBER, lineNumber);
		attributes.put(IMarker.CHAR_START, -1);
		attributes.put(IMarker.CHAR_END, -1);
		attributes.put("condition", breakpointCondition);
		
		IWorkspaceRunnable runnable = new IWorkspaceRunnable() {
			@Override
			public void run(IProgressMonitor monitor) throws CoreException {
				// create the marker
				IMarker marker = resource.createMarker("com.chipon32.debug.core.addrMarker");
				setMarker(marker);
				//set attributes
				ensureMarker().setAttributes(attributes); 
				//set marker message
				marker.setAttribute(IMarker.MESSAGE, "Address: " + addr);
//				Integer lineNumber = ChiponThread.locationData.get(addr);
//				if(lineNumber != null){
//					marker.setAttribute(IMarker.LINE_NUMBER, lineNumber.intValue());
//				}
				fSourceFile = resource;
				address = addr;
				condition = breakpointCondition;
			}
		};
		
		run(runnable);
	}
	
	public IResource getfSourceFile() {
		if(fSourceFile == null) {
			IMarker m = getMarker();
			if (m != null) {
				return m.getResource();
			}
		}
		
		return fSourceFile;
	}
	
	public boolean isRunToLineBreakpoint() {
		return false;
	}
	
	protected void run( IWorkspaceRunnable wr ) throws DebugException {
		try {
			ResourcesPlugin.getWorkspace().run( wr, null );
		}
		catch( CoreException e ) {
			throw new DebugException( e.getStatus() );
		}
	}

	@Override
	public String getModelIdentifier() {
		return DebugCoreActivator.ID_CHIP_DEBUG_MODEL;
	}

	@Override
	public void handleEvent(ChiponEvent event) {
		if (event instanceof ChiponSuspendedEvent || event instanceof ChiponDebuggerSuspendedEvent) {
			ChiponRunControlEvent rcEvent = (ChiponRunControlEvent) event;
			if (rcEvent.fReason.equals("breakpoint")) {
				handleHit(rcEvent);
			}
		}
		
	}
	
	private void handleHit(ChiponRunControlEvent event) {
		
	}
	
	protected void notifyThread() {

		if (fTarget != null) {
			ChiponThread thread = fTarget.getCurrentThread();
			if (thread != null) {
				thread.suspendedBy(this);
			}
		}

	}
	
	/**
	 * 执行断点添加
	 */
	@Override
	public String install(DebugElement target) throws Exception {
		
		fTarget = (ChiponDebugTarget) target;
		if(!IsChiponBreakPointCanInstall.testIsCaninstall(fTarget))
		{
			isSuccess = false;
			return "kf32command fail!";
		}
		//############################################################################		
		ChiponDebugTarget.addEventListener(this);
		//############################################################################
		ChiponBreakpointCommandResult result = createRequest(fTarget);
		if (!result.resultText.equalsIgnoreCase("success")) {
			setSucess(false);
//			String message = getMarker().getResource().getName() + ":" + getAddress() + " ";
			result.resultout.clear();
			result.resultout.add(getAddress() + " ");
			HandleError.getDefault().handleMessage(result.errorCode, result.resultout);
		} else {
//			WriteMessage.getDefault().writeBLUEMessage("添加断点成功");
			setSucess(true);
		}
		
		return result.resultText;
	}
	
	/**
	 * 执行添加地址断点操作
	 * @param target
	 * @return
	 * @throws Exception
	 */
	private ChiponBreakpointCommandResult createRequest(ChiponDebugTarget target) throws Exception {
		//	System.out.println(getMarker().getResource().getName());
		//如果正在运行中则先暂停运行
		boolean isSuspendFlag = false;
		target.getCurrentThread().setRunningBreakpointFlag(false);
		if(target.isBusy()){
			target.getCurrentThread().setRunningBreakpointFlag(true);
			isSuspendFlag = true;					
			target.getCurrentThread().KillGdbforPause(); 
			Thread.sleep(50);  //延时
//			result.errorCode="0xA1";
//			result.resultText="failed";		
//			return null;
		}

		//执行断点添加操作
		ChiponBreakpointCommandResult result = new ChiponBreakpointCommandResult(null);
		ChiponBreakpointCommand breakpointCommand;
		if(getCondition()!=null && !getCondition().isEmpty()){
			breakpointCommand = new ChiponBreakpointCommand("*" +getAddress(), getCondition());
		}else{
			breakpointCommand = new ChiponBreakpointCommand("*" +getAddress());
		}
		result =(ChiponBreakpointCommandResult) target.sendCommand(breakpointCommand); //发送添加断点命令
		setNumber(result.pointNo+"");
		//============================================================================================

		//恢复运行状态
		if(isSuspendFlag) {
			Thread.sleep(50);//延时
			//恢复调试运行状态
			Thread resumeThread = new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						target.getCurrentThread().resume();
					} catch (DebugException e) {
						e.printStackTrace();
					}
				}
			});
			resumeThread.start();
		}	
		
		
		return result;
	}
	
	@Override
	public void setEnabled(boolean enabled)  {
		try{
			if (enabled != isEnabled()) {
				if(fTarget==null || (fTarget!=null && fTarget.isTerminated())) {//未启动调试器时可以设置
					setAttribute(ENABLED, enabled);
					return;
				}
				
				if(enabled)
				{
					IsChiponBreakPointCanInstall.harddebugcountimit=4;
				    String result =	install(getDebugTarget());
					IsChiponBreakPointCanInstall.harddebugcountimit=5;
					if(result.contains("success"))
						setAttribute(ENABLED, enabled);
				}else{
					if(clearRequest(getDebugTarget()))
						setAttribute(ENABLED, enabled);
				}
			}
		}catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
	}
	
	protected ChiponDebugTarget getDebugTarget() {
		return fTarget;
	}
	
	/**
	 * 地址断点的删除	
	 * @param target
	 * @throws CoreException
	 */
	public void remove(ChiponDebugTarget target) throws Exception {
		
		if(clearRequest(target))
		{
//			ChiponDebugTarget.removeEventListener(null);		
			ChiponDebugTarget.removeEventListener(this);		
			fTarget = null;
		}
	}

	private boolean clearRequest(ChiponDebugTarget target) throws Exception {
		// target.sendCommand(new ChipOnClearBreakpointCommand((getLineNumber() - 1)));
//		if(target.isBusy()){
//			return false;
//		}
		
		if(!getNumber().equals("") && !getNumber().equals("0")){
			//如果正在运行中则先暂停运行
			boolean isSuspendFlag = false;
			target.getCurrentThread().setRunningBreakpointFlag(false);
			if(target.isBusy()){
				target.getCurrentThread().setRunningBreakpointFlag(true);
				isSuspendFlag = true;					
				target.getCurrentThread().KillGdbforPause(); 
				Thread.sleep(50);//延时
				//return false;
			}
			
			//发送断点移除命令
			target.sendCommand(new ChipOnClearBreakpointCommand(getNumber())); //发送删除断点命令
			setNumber("");
			//=================================================================
			
			//恢复运行状态
			if(isSuspendFlag) {
				Thread.sleep(50);//延时
				//恢复调试运行状态
				Thread resumeThread = new Thread(new Runnable() {
					@Override
					public void run() {
						try {
							target.getCurrentThread().resume();
						} catch (DebugException e) {
							e.printStackTrace();
						}
					}
				});
				resumeThread.start();
			}
		}
		
		return true;
	}
	
	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public ChiponDebugTarget getfTarget() {
		return fTarget;
	}

	public void setfTarget(ChiponDebugTarget fTarget) {
		this.fTarget = fTarget;
	}

	public void setfSourceFile(IResource fSourceFile) {
		this.fSourceFile = fSourceFile;
	}

	public void setSuccess(boolean isSuccess) {
		this.isSuccess = isSuccess;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Image getImage(){
		try {
			boolean isConditional = !getMarker().getAttribute("condition", "").isEmpty();
			boolean isEnabled = isEnabled();
			
			if(isEnabled){
				if(isConditional){
					return ResourceManager.getPluginImage(DebugCoreActivator.PLUGIN_ID, "icons/dbg16/cnd_br_e.png"); 
				}else{
					return ResourceManager.getPluginImage(DebugCoreActivator.PLUGIN_ID, "icons/dbg16/ln_br_e.png"); 
				}
			}else{
				if(isConditional){
					return ResourceManager.getPluginImage(DebugCoreActivator.PLUGIN_ID, "icons/dbg16/cnd_br_d.png"); 

				}else{
					return ResourceManager.getPluginImage(DebugCoreActivator.PLUGIN_ID, "icons/dbg16/ln_br_d.png"); 
				}
			}
		} catch (CoreException e) {
			e.printStackTrace();
		}
		
		return null;
	}

	@Override
	public void setDebugTarget(DebugElement target) {
		fTarget = (ChiponDebugTarget) target;	
	}


}
