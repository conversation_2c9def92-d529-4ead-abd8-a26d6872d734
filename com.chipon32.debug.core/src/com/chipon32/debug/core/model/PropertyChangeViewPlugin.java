package com.chipon32.debug.core.model;

import java.util.ArrayList;
import java.util.Iterator;

import org.eclipse.jface.util.IPropertyChangeListener;
import org.eclipse.jface.util.PropertyChangeEvent;

/***
 *<AUTHOR>
 *2013-7-8上午10:53:47
 ***/
public class PropertyChangeViewPlugin  {
	
	private static PropertyChangeViewPlugin instant;
	private String perspectiveName;
	
	public static PropertyChangeViewPlugin getInstant(){
		if(instant==null)
			instant=new PropertyChangeViewPlugin();
			return instant;
	}

	ArrayList<IPropertyChangeListener> myListeners=new ArrayList<IPropertyChangeListener>();
	//将监听添加到监听列表中
	public void addPropertyChangeListener(IPropertyChangeListener listener){
		if(!myListeners.contains(listener)){
			myListeners.add(listener);
		}
	}
	//将监听从监听列表中删除
	public void removePropertyChangeListener(IPropertyChangeListener listener){
		myListeners.remove(listener);
	}
	//传播到各个监听中
	public void initAndInvoke(ArrayList<IPropertyChangeListener> myListeners,Object obj){
		for(Iterator<IPropertyChangeListener> iterator=myListeners.iterator();iterator.hasNext();){
			IPropertyChangeListener element=iterator.next();
			element.propertyChange(new PropertyChangeEvent(this, "PropertyChangeView", null, obj));
		}
	}
	public String getPerspectiveName() {
		return perspectiveName;
	}
	public void setPerspectiveName(String perspectiveName) {
		this.perspectiveName = perspectiveName;
	}

}
