package com.chipon32.debug.core.model;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.model.IIndexedValue;
import org.eclipse.debug.core.model.IVariable;

/**
 * <AUTHOR> Wu
 */
public class ChiponArrayVariable extends ChiponValue implements IIndexedValue {

	public ChiponArrayVariable(ChiponValue value) {
		super(value.getChiponDebugTarget(), value.getfVariableTree(), value.getVariable());
	}

	@Override
	public IVariable getVariable(int offset) throws DebugException {
		if (offset >= getSize()) {
			throw new DebugException(new Status(IStatus.ERROR, DebugPlugin.getUniqueIdentifier(),
					DebugException.TARGET_REQUEST_FAILED, "Invalid array index: " + offset, null));
		}
		return new ChiponArrayEntryVariable(getChiponDebugTarget(), getfVariableTree().getChildren().get(offset));
	}

	@Override
	public IVariable[] getVariables(int offset, int length) throws DebugException {
		if (offset >= getSize()) {
			requestFailed("Index out of bounds.", new IndexOutOfBoundsException(Integer.toString(offset)));
		}

		if (offset + length - 1 >= getSize()) {
			requestFailed("Specified range out of bounds.", new IndexOutOfBoundsException(Integer.toString(offset + length - 1)));
		}

		IVariable[] variables = new IVariable[length];

		for (int i = 0; i < length; i++) {
			variables[i] = new ChiponArrayEntryVariable(getChiponDebugTarget(), fVariableTree.getChildren().get(offset + i));
		}

		return variables;
	}

	@Override
	public int getSize() {
		return getfVariableTree().getChildren().size();
	}

	@Override
	public int getInitialOffset() {
		return 0;
	}

}
