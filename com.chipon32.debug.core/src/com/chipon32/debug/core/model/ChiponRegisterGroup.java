/**
 * 
 */
package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.model.IRegister;
import org.eclipse.debug.core.model.IRegisterGroup;

/**
 * <AUTHOR>
 *
 */
public class ChiponRegisterGroup extends ChiponDebugElement implements IRegisterGroup {

	private ChiponRegister fRegister;
	public ChiponRegisterGroup(ChiponRegister register) {
		super(register.getChiponDebugTarget());
		fRegister = register ;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IDebugElement#getModelIdentifier()
	 */
	@Override
	public String getModelIdentifier() {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IDebugElement#getLaunch()
	 */
	@Override
	public ILaunch getLaunch() {
		// TODO Auto-generated method stub
		return fRegister.getLaunch();
	}

	/* (non-Javadoc)
	 * @see org.eclipse.core.runtime.IAdaptable#getAdapter(java.lang.Class)
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public Object getAdapter(Class adapter) {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IRegisterGroup#getName()
	 */
	@Override
	public String getName() throws DebugException {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IRegisterGroup#getRegisters()
	 */
	@Override
	public IRegister[] getRegisters() throws DebugException {
		// TODO Auto-generated method stub
		return new  ChiponRegister[]{fRegister} ;
	}

	/* (non-Javadoc)
	 * @see org.eclipse.debug.core.model.IRegisterGroup#hasRegisters()
	 */
	@Override
	public boolean hasRegisters() throws DebugException {
		// TODO Auto-generated method stub
		return getRegisters().length > 0;
	}

}
