package com.chipon32.debug.core.model;

import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.model.IDebugTarget;
import org.eclipse.debug.core.model.IValue;
import org.eclipse.debug.core.model.IVariable;

/**
 * when watch command return not right value,return object this value
 * <AUTHOR> @
 */
public class ChiponNullValue implements IValue {

	
	private String expression;
	//flag,like user define watch point
	private String id;
	
	ChiponNullValue(String expression){
		this.expression = expression;
	}
	
	@Override
	public String getModelIdentifier() {
		return null;
	}

	@Override
	public IDebugTarget getDebugTarget() {
		return null;
	}

	@Override
	public ILaunch getLaunch() {
		return null;
	}

	@Override
	public Object getAdapter(@SuppressWarnings("rawtypes") Class adapter) {
		return null;
	}

	@Override
	public String getReferenceTypeName() throws DebugException {
		return null;
	}

	@Override
	public String getValueString() throws DebugException {
		return expression+Messages.ChiponNullValue_0;
	}

	@Override
	public boolean isAllocated() throws DebugException {
		return false;
	}

	@Override
	public IVariable[] getVariables() throws DebugException {
		return null;
	}

	@Override
	public boolean hasVariables() throws DebugException {
		return false;
	}

	public String getExpression() {
		return expression;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	/*
	 * @Override public boolean isCanSetInputExpression1() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public boolean isCanSetInputExpression2() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public boolean isCanSetInputExpression3() { // TODO Auto-generated
	 * method stub return false; }
	 * 
	 * @Override public void setCanSetInputExpression1(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 * 
	 * @Override public void setCanSetInputExpression2(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 * 
	 * @Override public void setCanSetInputExpression3(boolean istrue) { // TODO
	 * Auto-generated method stub
	 * 
	 * }
	 */

	
	
}
