package com.chipon32.debug.core.model;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;

/***
 *<AUTHOR>
 *2013-6-20обнГ1:14:06
 ***/
public class ChiponGroupEEData {

	private List<ChiponEEData> eeDatas;
	
	private String columnNum;

	public ChiponGroupEEData() {
		eeDatas = new ArrayList<ChiponEEData>(16);
	}

	public List<ChiponEEData> getEEDatas() {
		return eeDatas;
	}
	
	public void addEEData(ChiponEEData EEData){
		eeDatas.add(EEData);
	}
	
	public String getAscii(){
		StringBuffer buffer = new StringBuffer();
		for(ChiponEEData EEData : eeDatas){
			byte stringByte = ByteConvertor.hexToByte(EEData.getValue());
			byte[] array = new byte[] { stringByte};
			try {
				String tmp = new String(array, "US-ASCII");
				buffer.append(tmp);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return buffer.toString();
		
	}

	public String getColumnNum() {
		return columnNum;
	}

	public void setColumnNum(String columnNum) {
		this.columnNum = columnNum;
	}
	
}
