ChiponDebugTarget_22=调试发生错误，调试终止
ChiponDebugTarget_31=当前调试端口
ChiponDebugTarget_32=，请使用COM1~COM64或将大于该编号的端口配置在支持范围后重试
ChiponDebugTarget_61=解析文件失败，请重试
ChiponDebugTarget_62=发送命令:
ChiponDebugTarget_76=调试忙碌中，请稍后或暂停运行后重试...
ChiponDebugTarget_77=设置断点失败
ChiponDebugTarget_78=调试忙碌中，请稍后或暂停运行后重试...
ChiponDebugTarget_79=删除断点异常
ChiponDebugTarget_81=调试忙碌中，请稍后或暂停运行后重试...
ChiponDebugTarget_83=运行到main超时，检测选项gstabs+勾选，芯片程序同步下载，ISP编程条件移除，调试端口保留，或其他造成调试失败的情况
ChiponDebugTarget_84=其他调试失败可能为看门狗复位、 main函数前代码耗时过长或启动预运行芯片时程序运行出错等
ChiponDebugTarget_90=目标调试已启动...
ChiponDebugTarget_94=启动调试失败，请确认是否同步了程序或其他调试条件
ChiponDebugTarget_95=设置PC失败！

ChiponNullValue_0=不能被解析为一个变量,不存在此变量或已被优化

ChiponThread_16=调试目标被断点中断.
ChiponThread_17=单步跳入结束.
ChiponThread_25=已复位调试目标.
ChiponThread_29=目标调试终止.
ChiponThread_30=单步跳过...
ChiponThread_39=单步跳过结束.
ChiponThread_40=运行......
ChiponThread_46=目标已中断暂停.
ChiponThread_52=触发调试暂停.
ChiponThread_53=单步返回...
ChiponThread_56=当前函数不能退出，替换为单指令执行
ChiponThread_62=单步返回结束.
ChiponThread_69=调试目标暂停
ChiponThread_7=单步跳入...


ChiponWatchpointData_0=读取
ChiponWatchpointData_1=修改
ChiponWatchpointData_14=格式化命令匹配失败，请确认...
ChiponWatchpointData_2=读取或修改
ChiponWatchpointData_3=PC地址断点
ChiponWatchpointData_4=地址

ChiponWatchpointView_1=监控变量:
ChiponWatchpointView_2=地址段0x:

