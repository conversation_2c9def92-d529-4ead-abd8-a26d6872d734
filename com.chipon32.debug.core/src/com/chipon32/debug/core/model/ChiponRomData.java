package com.chipon32.debug.core.model;

import org.eclipse.swt.graphics.Color;

/**
 * <AUTHOR> @since 2013-6-6 上午9:01:56
 *内存信息
 */
public class ChiponRomData implements Cloneable{
	
	private Color color;

	private String value;
	
	private String address;
	//--------------------------------------------------	
	private boolean isHaveName;
	private String name;
	
	public String getName() {
		return name;
	}

	public boolean getIsHaveName() {
		return isHaveName;
	}

	public void setIsHaveName(boolean isHaveName) {
		this.isHaveName = isHaveName;
	}

	public void setName(String name) {
		this.name = name;
	}
//--------------------------------------------------
	public ChiponRomData(String address, String value) {
		super();
		this.value = value;
		this.address = address;
		this.isHaveName=false;
	}
	
	/**
	 * @return 行地址,即消亡16进制的尾字符的 16对齐.............
	 */
	public String getRomNum(){
		if(address != null && !"".equals(address.trim())){
			String addr = address.trim();
			int length = addr.length();
			StringBuffer strAddr = new StringBuffer();
			for(int i = 0; i < 8-length; i++){	
				strAddr.append("0");
			}
			if(length==1){
				strAddr.append("0");
			}else if(length >=2){
				strAddr.append(addr.substring(0, length-1)+"0");
			}else{
				return null;
			}
			return strAddr.toString();
		}
		return null;
	}
	
//	/**
//	 * @return 列地址
//	 */
//	public String getColumnNum(){
//		if(address != null && !"".equals(address.trim()) && address.length() > 0){
//			String addr = address.trim();
//			return addr.substring(addr.length()-1);
//		}
//		return null;
//	}
	
//	public String getRigisterAddress(){
//		if(address != null){
//			String addr = address.trim().substring(2);
//			if(addr.length() == 1){
//				return "0"+addr+"H";
//			}else {
//				return addr+"H";
//			}
//		}
//		return null;
//	}
	
	

	@Override
	protected ChiponRomData clone() throws CloneNotSupportedException {
		ChiponRomData data= (ChiponRomData)super.clone();
		data.setAddress(address);
		data.setValue(value);
		
		data.setIsHaveName(isHaveName);
		data.setName(name);
		return data;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((value == null) ? 0 : value.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ChiponRomData other = (ChiponRomData) obj;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		if (value == null) {
			if (other.value != null)
				return false;
		} else if (!value.equals(other.value))
			return false;
		return true;
	}



	public String getValue() {
		if(value != null){
			String val = value.trim();
			if(val.length() == 1){
				return "0"+val;
			}else{
				return val;
			}
		}
		return value;
	}
	public String getOrgValue() {
		if(value == null){		
			return ".";			
		}
		return value.trim();
	}
	public String getOldValue() {
		return value;
	}



	public void setValue(String value) {
		this.value = value;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Color getColor() {
		return color;
	}

	public void setColor(Color color) {
		this.color = color;
	}

	
}
