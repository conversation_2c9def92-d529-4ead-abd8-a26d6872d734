package com.chipon32.debug.core.model;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.debug.core.DebugException;
import org.eclipse.debug.core.model.IRegisterGroup;
import org.eclipse.debug.core.model.IStackFrame;
import org.eclipse.debug.core.model.IThread;
import org.eclipse.debug.core.model.IValue;
import org.eclipse.debug.core.model.IVariable;

import com.chipon32.debug.core.protocol.ChiponSetVariableCommand;
import com.chipon32.debug.core.protocol.ChiponSetVariableCommandResult;
import com.chipon32.debug.core.protocol.ChiponWatchCommand;
import com.chipon32.debug.core.protocol.ChiponWatchCommandResult;
import com.chipon32.debug.core.util.ChiponBracketParse;
import com.chipon32.debug.core.util.IChiponBracketParse;
import com.google.common.base.MoreObjects;


/**
 * ChiponStackFrame所做的唯一一件略微感兴趣的事就是解析并缓存解释器的栈帧回复，除此之外，getLineNumber的实现返回先前缓存的行数
 * setpOver的实现委托给线程等等
 * 
 * 控制工具条上运行、暂停、终止实际操作
 * <AUTHOR>
 *
 */
public class ChiponStackFrame extends ChiponDebugElement implements IStackFrame {
	
	private final ChiponDebugTarget fDebugTarget;

	public 	IPath SF_fFilePath;
	public 	String fFunction;
	public 	int fLine;
	public  String addr = "";
	public final String threadUuid;
	
	private final ChiponRegisterGroup[] registerGroups;
	//private ChiponRegister register;
	
	public ChiponStackFrame(ChiponDebugTarget target,List<String> stackUid, List<String> stackValues, String uuid) {
		super(target);
		this.fDebugTarget = target;
		registerGroups = new ChiponRegisterGroup[]{};
		this.threadUuid = uuid;
		updateStackValue(stackUid, stackValues);
	}
	
	public int getThreadIndex() {
		IThread[] threads;
		try {
			threads = fDebugTarget.getThreads();
			for (int i = 0; i < threads.length; i++) {
				if (threadUuid.equals(((ChiponThread) threads[i]).getUuid())) {
					return i;
				}
			}
		} catch (DebugException e) {
			e.printStackTrace();
		}

		return -1;
	}

	public void updateStackValue(List<String> stackUid, List<String> stackValues) {
		if (stackUid != null && stackUid.size() > 3) {
			SF_fFilePath = new Path(stackUid.get(0));
			fLine = Integer.parseInt(stackUid.get(1));
			fFunction = stackUid.get(2);
			addr = stackUid.get(3);
		}

		StringBuilder str = new StringBuilder();
		IChiponBracketParse bracketParse = new ChiponBracketParse();
		Map<String, ChiponVariableTree> values = new LinkedHashMap<>() {
			@Override
			public ChiponVariableTree put(String key, ChiponVariableTree value) {
				if (containsKey(key)) {
					// Find next available suffix number
					int suffix = 2;
					while (containsKey(key + " #" + suffix)) {
						suffix++;
					}
					String newKey = key + " #" + suffix;
					value.setName(newKey);
					return super.put(newKey, value);
				} else {
					return super.put(key, value);
				}
			}
		};

		for (String s : stackValues) {
			// response = gjztest = 3 '\003' ## 如果为8时，为'b'
			// response = gjztest1 = 3
			// response = gjzarr2 = {0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0}
			// response = newStudio = {studio1 = {name =
			// "g\000\000\200\001\000\000\000\001\000c", id = 5746, yuwenfenshu = 6578},
			// techer1 = {name = 103 'g', id = 1, yuwenfenshu = 1, shuxuefenshu = 99}}
			// 字符会进行转码，
			// ------------------------------处理变量换行显示流程------------------------------------------
			str.append(s.trim());

			// 个例会解析错误的提前退出而不影响其他，不然如特殊输入会错。
			try {
				String dealparsestring = str.toString();
				boolean isLikeStruct = false;
				// 通过"{"与"}"是否匹配判断判断这一行是否内容没有显示完整
				if (dealparsestring.contains("{")) {
					if (!bracketParse.check(dealparsestring)) {
						int a = dealparsestring.indexOf("{");
						String buf = dealparsestring.substring(0, a);
						if (!buf.contains("\\"))
							continue;
					} else
						isLikeStruct = true;
				}

				// 处理单行显示完整复杂变量
				if (isLikeStruct) {
					// 替换掉多余的空格，但是又不能替换char数组类型中的空格,交由方法识别中过滤
					try {
						// 调用综合解析方法进行构建
						for (ChiponVariableTree v : bracketParse.buildTrees(bracketParse.getTrees(str.toString()))) {
							values.put(v.getName(), v);
						}
					} catch (Exception e) {
						// TODO: handle exception
						// 异常的不识别返回的内容，但别throw异常退出调试器的捕获处理，本质返回内容不合法或其他原因
					}
				}
				// 处理简单变量 或字符串数组
				else {
					int a = dealparsestring.indexOf("=");
					if (a < 0)
						continue;
					String[] datas = new String[2];
					datas[0] = dealparsestring.substring(0, a);
					datas[1] = dealparsestring.substring(a + 1);

					// 可能字符串 的解析
					if (datas[1].contains("\"")) {
						Map<String, List<String>> rusult = bracketParse.ParseStrings(datas[1]);

						String strVaule = rusult.get("StringVaule").get(0);
						List<String> vaules = rusult.get("CharsVaule");
						// 树的建立
						// ## 根树
						ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);
						onlyTree.setName(datas[0].trim());
						onlyTree.setValue("'" + strVaule);
						values.put(onlyTree.getName(), onlyTree);
						// ## 数据内容
						for (int jj = 0; jj < vaules.size(); jj++) {
							ChiponVariableTree TreeChild = new ChiponVariableTree(0, 0);
							TreeChild.setName(datas[0].trim() + "[" + jj + "]");
							TreeChild.setValue(vaules.get(jj).trim());
							TreeChild.setParent(onlyTree);
							onlyTree.addChild(TreeChild);
						}

					}
					// 普通对象
					else {
						ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);
						onlyTree.setName(datas[0].trim());
						onlyTree.setValue(datas[1].trim());
						values.put(onlyTree.getName(), onlyTree);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			// 等待新的读取
			str = new StringBuilder();
		}

		((ChiponThread) getThread()).updateVariables(this, values.values());
	}

	@Override
	public boolean canStepInto() {
		if(getThread() != null)
			return getThread().canStepInto();
		else
			return false;
	}

	@Override
	public boolean canStepOver() {
		if(getThread() != null)
			return getThread().canStepOver();
		else
			return false;
	}

	@Override
	public boolean canStepReturn() {
		if(getThread() != null)
			return getThread().canStepReturn();
		else
			return false;
	}

	@Override
	public boolean isStepping() {
		if(getThread() != null)
			return getThread().isStepping();
		else
			return false;
	}
//#########################################################################################
	@Override
	public void stepInto() throws DebugException {
		getThread().stepInto();
	}

	@Override
	public void stepOver() throws DebugException {
		getThread().stepOver();
	}

	@Override
	public void stepReturn() throws DebugException {
		getThread().stepReturn();
	}
	//#########################################################################################
	@Override
	public boolean canResume() {
		if (getThread() != null)
			return getThread().canResume();
		else
			return false;
	}

	@Override
	public boolean canSuspend() {
		if (getThread() != null)
			return getThread().canSuspend();
		else
			return false;
	}

	@Override
	public boolean isSuspended() {
		if (getThread() != null)
			return getThread().isSuspended();
		else
			return false;
	}

	@Override
	public void resume() throws DebugException {
		getThread().resume();
	}

	@Override
	public void suspend() throws DebugException {
		getThread().suspend();
	}

	@Override
	public boolean canTerminate() {
		if (getThread() != null)
			return getThread().canTerminate();
		else
			return false;
	}

	@Override
	public boolean isTerminated() {
		return getThread().isTerminated();
	}

	@Override
	public void terminate() throws DebugException {
		getThread().terminate();
	}

	@Override
	public IThread getThread() {
		return fDebugTarget.getThread(threadUuid);
	}

	@Override
	public IVariable[] getVariables() throws DebugException {
		return ((ChiponThread) getThread()).getVariables(this);
	}

	@Override
	public boolean hasVariables() throws DebugException {
		return getVariables().length > 0;
	}
//#####################################################################################
	/**
	 * debug show file name 
	 */
	@Override
	public String getName() throws DebugException {
		if(SF_fFilePath!=null)
			return SF_fFilePath.lastSegment();
		else
			return "err";
	}
	// look for the source by container
	public String getSourceName() {
		if(SF_fFilePath!=null)
//			return SF_fFilePath.lastSegment();
			return SF_fFilePath.toOSString();
		return "fail";
	}

	/**
	 * 返回指针所指行数
	 */
	@Override
	public int getLineNumber() throws DebugException {
		return fLine;
	}

	@Override
	public int getCharStart() throws DebugException {
		return -1;
	}

	@Override
	public int getCharEnd() throws DebugException {
		return -1;
	}

	@Override
	public IRegisterGroup[] getRegisterGroups() throws DebugException {
		return registerGroups;
	}

	@Override
	public boolean hasRegisterGroups() throws DebugException {
		return registerGroups.length>0;
	}


	/**
	 * 取得Expressions视图中的值并通信，然后获得返回值，解析封装后返回
	 * @param expression 表达式的值
	 * @return
	 */
	public synchronized IValue evaluateExpression(final String expression) {
		IValue value = null;
		
		ChiponWatchCommandResult result = 
				(ChiponWatchCommandResult) sendCommand(new ChiponWatchCommand(expression));
		
		if(!result.resultText.equals("success")){
			return new ChiponNullValue(expression);
		}
		if("novariable".equalsIgnoreCase(result.resultText.trim())){
			return new ChiponNullValue(expression);
		}
		
		List<ChiponVariableTree> list = new ArrayList<ChiponVariableTree>();
		IChiponBracketParse bracketParse = new ChiponBracketParse();
		//###################################################################################
		// 识别 {的位置，存在即为集合，否则为简单的结果语句//有子元素如结构体和数组
//		int i = ChiponMessageParse.getBracketBeginNum(result.value);
		String dealparsestring= result.value;
		boolean isLikeStruct=false;
		//通过"{"与"}"是否匹配判断判断这一行是否内容没有显示完整 				
		if(dealparsestring.contains("{")){   
			if(!bracketParse.check(dealparsestring))
			{
//				int a=dealparsestring.indexOf("{");
//				String buf= dealparsestring.substring(0,a);
//				if(!buf.contains("\\") )
//					continue;
			}
			else
				isLikeStruct=true;
		}
		
		//###################################################################################
//		if(!(i < 0)){  //  { 复杂元素			
		if(isLikeStruct){
			try{
				//调用综合解析方法进行构建			
				list = bracketParse.buildTrees(bracketParse.getTrees(result.value, expression));
			}catch (Exception e) {
				// TODO: handle exception
				// 异常的不识别返回的内容，但别throw异常退出调试器的捕获处理，本质返回内容不合法或其他原因
			}
		}else{	//单个变量，也可能字符数组
			ChiponVariableTree onlyTree = new ChiponVariableTree(0, 0);   //ChiponVariableTree--表达式值的实体类
			onlyTree.setName(expression);
			String vs = result.value;
			//#######################################################################
			// 字符串或一维数组
			if(vs.contains("\"") && !vs.startsWith("("))
			{				
				Map <String, List<String>> rusult = bracketParse.ParseStrings(vs);
				// 可能字符串	
				String strVaule		=rusult.get("StringVaule").get(0);
				List<String>vaules	=rusult.get("CharsVaule");
				
				// 树的建立
				//## 根树				
				onlyTree.setValue("'"+strVaule);
				list.add( onlyTree);	
				//## 数据内容
				for(int jj=0;jj<vaules.size();jj++)
				{							
					ChiponVariableTree TreeChild = new ChiponVariableTree(0, 0);
					TreeChild.setName(expression+"["+jj+"]");
					TreeChild.setValue(vaules.get(jj).trim());
					TreeChild.setParent(onlyTree);
					onlyTree.addChild( TreeChild);	
				}				
			}
			//#######################################################################
			else
			{
				onlyTree.setValue(result.value.trim());
			}
			//#######################################################################
			list.add(onlyTree);
		}
		//###################################################################################
		ChiponVariableTree[] fVariableTree = list.toArray(new ChiponVariableTree[list.size()]);
		
		try {
			if(fVariableTree.length > 0){
				value = ChiponVariable.create(getChiponDebugTarget(), fVariableTree[0]).getValue();
			}else {
				return new ChiponNullValue(expression);
			}
		} catch (DebugException e) {
			
		}
	
		return value;
	}
	
	/**
	 * 修改表达式的值
	 * @param expression
	 * @param value
	 * @return
	 */
	public synchronized IValue evaluateExpression(final String expression,String value) {
		//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
		ChiponSetVariableCommandResult result = (ChiponSetVariableCommandResult) sendCommand(
				new ChiponSetVariableCommand(expression, value));
//		String returnText = result.resultText ;
//		返回：
//		address inpurt err!		地址不对
//		input err!			    值不对
//		type input err!		    类型不对
//		ok				        正常
		if(result.resultText !=null && ("success").equalsIgnoreCase(result.resultText.trim())){
			return  evaluateExpression(expression);
		}else{
			return null;
		}
	}
	
	/**
	 * 调试过程中鼠标滑到变量上显示变量的值
	 * @param expressionText
	 * @return
	 */
	public String evaluateHoverExpression(String expressionText) {
		
		ChiponWatchCommandResult result = 
				(ChiponWatchCommandResult) sendCommand(new ChiponWatchCommand(expressionText));
		
		if(result==null || !result.resultText.equals("success")){
			return "";
		}
		
		return result.value;
		
	}

	@Override
	public String toString() {
		return MoreObjects.toStringHelper(this)
			.add("SF_fFilePath", SF_fFilePath)
			.add("fFunction", fFunction)
			.add("fLine", fLine)
			.add("addr", addr)
			.toString();
	}
}
