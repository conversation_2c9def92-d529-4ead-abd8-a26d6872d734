package com.chipon32.debug.core.model;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import com.chipon32.hex.core.util.ByteConvertor;

/***
 *<AUTHOR>
 *
 ***/
public class ChiponGroupEEpromData {

	private List<ChiponEEpromData> eepromDatas;
	
	private String rowNum;

	public ChiponGroupEEpromData() {
		eepromDatas = new ArrayList<ChiponEEpromData>(16);
	}

	public List<ChiponEEpromData> getEEpromDatas() {
		return eepromDatas;
	}
	
	public void addEEpromData(ChiponEEpromData EEpromData){
		eepromDatas.add(EEpromData);
	}
	
	public String getAscii(){
		StringBuffer buffer = new StringBuffer();
		for(ChiponEEpromData EEData : eepromDatas){
			byte stringByte = ByteConvertor.hexToByte(EEData.getValue());
			byte[] array = new byte[] { stringByte};
			try {
				String tmp = new String(array, "US-ASCII");
				buffer.append(tmp);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return buffer.toString();
		
	}

	public String getRowNum() {
		return rowNum;
	}

	public void setRowNum(String rowNum) {
		this.rowNum = rowNum;
	}
	
}
