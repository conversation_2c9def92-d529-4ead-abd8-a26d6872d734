package com.chipon32.debug.core.model;

public class ChiponACCXData {
	
	private String aCCName;				//ACCX名称
	
	private String highData;			//高8位的值
	
	private String lowData;				//低8位的值
	
	private long decView;				// 联合10进制内容
	
	public long getDecView() {
		return decView;
	}

	public ChiponACCXData() {
		
	}

	public ChiponACCXData(String aCCName, String highData, String lowData) {
		super();
		this.aCCName = aCCName;
		setHighData(highData);
		setLowData(lowData);
		
		decView=Long.parseLong(highData+lowData,16);
	}



	public String getaCCName() {
		return aCCName;
	}

	public void setaCCName(String aCCName) {
		this.aCCName = aCCName;
	}

	public String getHighData() {
		return highData;
	}

	public void setHighData(String highData) {
		if(highData.contains("0x")){
			highData = highData.replace("0x", "");
		}
		if(highData.contains("0X")){
			highData = highData.replace("0X", "");
		}
		StringBuffer str = new StringBuffer(highData);
		for(int i = highData.length(); i < 8; i++){
			str.insert(0, "0");
		}
		this.highData = str.toString();
	}

	public String getLowData() {
		return lowData;
	}

	public void setLowData(String lowData) {
		//低8位前面补0
		if(lowData.contains("0x")){
			lowData = lowData.replace("0x", "");
		}
		if(lowData.contains("0X")){
			lowData = lowData.replace("0X", "");
		}
		StringBuffer str = new StringBuffer(lowData);
		for(int i = lowData.length(); i < 8; i++){
			str.insert(0, "0");
		}
		this.lowData = str.toString();
	}
	public void setVauleData( long vaule) {
		decView=vaule;
	}
	
}
