package com.chipon32.debug.core.elf;

/**
 * 全局变量信息
 * <AUTHOR>
 *
 */
public class DebugGlobalSymbol {
	private String variableName;
	private String variableAddress;
	
	private long size;  //字节长度
	private DebugType debugtype;
	
	public DebugGlobalSymbol(String varName, String address) {
		this.variableName = varName;
		this.variableAddress = address;
	}
	
	public DebugGlobalSymbol(String varName, long size, String address) {
		this.variableName = varName;
		this.size = size;
		this.variableAddress = address;
	}

	public String getVariableName() {
		return variableName;
	}

	public void setVariableName(String variableName) {
		this.variableName = variableName;
	}

	public String getVariableAddress() {
		return variableAddress;
	}

	public void setVariableAddress(String variableAddress) {
		this.variableAddress = variableAddress;
	}

	public long getSize() {
		return size;
	}

	public void setSize(long size) {
		this.size = size;
	}

	public DebugType getDebugtype() {
		return debugtype;
	}

	public void setDebugtype(DebugType debugtype) {
		this.debugtype = debugtype;
	}

}
