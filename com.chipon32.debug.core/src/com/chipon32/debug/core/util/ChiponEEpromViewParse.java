package com.chipon32.debug.core.util;

import java.util.List;

import org.eclipse.core.resources.IProject;

import com.chipon32.chiponide.core.properties.ChipOnProjectProperties;
import com.chipon32.chiponide.core.properties.ProjectPropertyManager;
import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponEEpromData;
import com.chipon32.debug.core.model.ChiponThread;
import com.chipon32.hex.core.util.IConfigurationProvider;
import com.chipon32.util.provider.ConfigurationFactory;

/***
 *<AUTHOR> 2017-4-19
 *	获取eeprom配置文件
 *
 ***/
public class ChiponEEpromViewParse {
	
	private IConfigurationProvider config;
	
	public ChiponEEpromViewParse(ChiponThread   fThread) {
		IProject project=null;
		if(fThread!=null)
			project =((ChiponDebugTarget)fThread.getDebugTarget()).getfProject();
		 
		 String chip=null;
		 if(project != null && project.exists()){
			 ProjectPropertyManager ppm = ProjectPropertyManager.getPropertyManager(project);
			 ChipOnProjectProperties fTargetProps = ppm.getProjectProperties();
			 chip = fTargetProps.getChipName();// 获取当前项目的芯片型号
		 }
		 config = ConfigurationFactory.getProvider(chip);
	}
	
	public String getStartAddr(){
		return config.getEEproms().get(0).getStartAddr();
	}

	//拼EEprom视图的命令
	public int getLength(){	
		return Integer.parseInt(config.getEEproms().get(0).getSize());
	}
	
	 /**
	  * <AUTHOR> 2017-4-19 设置eeprom相对地址
	  * @param eepromDataList
	  */
	    public void setRowNum(List<ChiponEEpromData> eepromDataList) {
			for(int i = 0; i < eepromDataList.size(); i++){
				
				String k = i/16 +"";	//判断在第几行
				StringBuffer str = new StringBuffer();
				//保证4位对齐
				for(int j =0; j<4 - k.length(); j++){
					str.append("0");
				}
				str.append(k);
				eepromDataList.get(i).setRowNum(str.toString());
			}
			
		}
	
}
