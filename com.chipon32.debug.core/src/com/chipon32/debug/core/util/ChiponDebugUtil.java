package com.chipon32.debug.core.util;

import org.eclipse.debug.core.DebugPlugin;
import org.eclipse.debug.core.ILaunch;
import org.eclipse.debug.core.ILaunchManager;
import org.eclipse.debug.core.model.IDebugTarget;

import com.chipon32.debug.core.model.ChiponDebugTarget;
import com.chipon32.debug.core.model.ChiponThread;

public class ChiponDebugUtil {
	
	public static ChiponDebugTarget getCurrentChiponDebugTarget() {
        ILaunchManager launchManager = DebugPlugin.getDefault().getLaunchManager();

        ILaunch[] launches = launchManager.getLaunches();
        if (launches.length > 0) {
            ILaunch currentLaunch = launches[launches.length - 1];
            IDebugTarget debugTarget = currentLaunch.getDebugTarget();
            if(debugTarget != null  && debugTarget instanceof ChiponDebugTarget) {
            	return (ChiponDebugTarget) debugTarget;
            }
        }
		return null;
	}
	
	public static ChiponThread getCurrentChiponThread() {
		ChiponDebugTarget chiponDebugTarget = getCurrentChiponDebugTarget();
		if(chiponDebugTarget != null) {
			return chiponDebugTarget.getCurrentThread();
		}
		return null;
	}
	
	public static boolean isCurrentChiponThreadSuspended() {
		ChiponThread chiponThread = getCurrentChiponThread();
		if(chiponThread != null && chiponThread.isSuspended()) {
			return true;
		}
		return false;
	}
	
	public static int calcuteUnite(int number) {
		int value = 1;
		int sum = 0;
		for (int i = 0; i < number; i++) {
			sum += value;
			value = value << 1;
		}
		return sum;
	}

}
